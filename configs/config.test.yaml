env: test
server:
  address: ':28000'
  openapi_path: /api.json
  swagger_path: /apidoc
  data_path: ./data/business-workflow-data
logger:
  level: all
  stdout: true
db:
  link: >-
    meta_rw:7C5*aoNNR4G5S*F4@tcp(avoice-business-cn-allvoice-cn-mysql-node-1.external.se.cluster.local:3306)/openapi_cn?charset=utf8mb4&parseTime=True&loc=Local
  max_idle_conns: 10
  max_open_conns: 25
  conn_max_lifetime_seconds: 3600
redis:
  host: avoice-business-cn-allvoice-cn-redis-node-1.external.se.cluster.local
  port: 6379
  password: ''
  database: 0
temporal:
  queue: goframe-local-task-queue-xsj
  host_port: 'temporal-frontend.basic-service.svc.cluster.local:7233'
  start_to_close_timeout: 5
  schedule_to_close_timeout: 1440
  initial_interval: 5
  backoff_coefficient: 2
  maximum_interval: 65
  maximum_attempts: 300
obs:
  vendor: ali
  access_key: LTAI5tKgvBShWP92WXvDoHpt
  secret_key: ******************************
  oss_arn: 'acs:ram::1538072316762279:role/ramoss-oss-test-ali-bj-xp-allvoice-cn'
  bucket_name: oss-test-ali-bj-xp-allvoice-cn
  endpoint: oss-cn-beijing-internal.aliyuncs.com
  public_end_point: oss-cn-beijing.aliyuncs.com
  region: cn-north-4
  cdn_name: cdn-allvoice-down-cn-testing.funnycp.com
  object_dir: allvoice
omni_business_grpc: 'api-business-cn.basic-service-cn.svc.cluster.local:50051'
omni_balance_grpc: 'omni-balance-cn.basic-service-cn.svc.cluster.local:50041'
omni_tenant_grpc: 'omni-tenant-cn.basic-service-cn.svc.cluster.local:50042'
omni_engine_grpc: 'omni-engine-cn.basic-service-cn.svc.cluster.local:50051'
timer_config:
  config_file: /app/config/timer-dye.json
id_gen:
  address: 'omni-openapi-testing.funnycp.com:80'
audit:
  is_cn: true
  host: 'http://avoice-audit-server-cn.basic-service-cn.svc.cluster.local'
  enable: false
  tenant_id: TT
  app_code: ALL-VOICE
  interval_ms: 1000
  timeout_min: 10
  scene:
    commentary_video_upload:
      scene_code: "1001"
      audit_type: "machine"
      audit_strategy: "use_machine_audit_result"
    commentary_audio_upload:
      scene_code: "2002"
      audit_type: "machine"
      audit_strategy: "use_machine_audit_result"

local_path: "/data/commentary/bisvideos"
