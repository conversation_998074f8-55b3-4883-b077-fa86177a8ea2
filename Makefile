#ROOT_DIR    = $(shell pwd)
#NAMESPACE   = "default"
#DEPLOY_NAME = "template-single"
#DOCKER_NAME = "template-single"
#
#include ./hack/hack.mk


# 定义编译后可执行文件的名称
BINARY_NAME=business_workflow 

export CONFIG_FILE=./configs/config.local.yaml

# 构建项目
build:
	@echo "Building $(BINARY_NAME) ..."
	go build -o bin/$(BINARY_NAME) main.go

# 运行项目
run:
	@echo "Running $(BINARY_NAME)..."
	./bin/$(BINARY_NAME) --config=./configs/config.local.yaml

# 清理构建文件
clean:
	@echo "Cleaning $(BINARY_NAME) ..."
	go clean
	rm -f bin/$(BINARY_NAME)
	rm -f coverage.out
	rm -f coverage.html

# 测试相关目标
.PHONY: test test-unit test-integration test-mock test-performance test-coverage

# 运行所有测试
test:
	@echo "运行所有测试..."
	go test -v ./...

# 运行单元测试（跳过集成测试）
test-unit:
	@echo "运行单元测试..."
	go test -short -v ./...

# 运行集成测试（需要真实数据库）
test-integration:
	@echo "运行集成测试..."
	@echo "请确保数据库已启动并配置正确"
	go test -v ./internal/service/commentary_svc/impl -run "Integration"

# 运行 Mock 测试
test-mock:
	@echo "运行 Mock 测试..."
	go test -v ./internal/service/commentary_svc/impl -run "WithMock"

# 运行性能测试
test-performance:
	@echo "运行性能测试..."
	go test -v ./internal/service/commentary_svc/impl -run "Performance"
	go test -bench=. -benchmem ./internal/service/commentary_svc/impl

# 运行测试并生成覆盖率报告
test-coverage:
	@echo "运行测试并生成覆盖率报告..."
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

# 生成 Mock 文件
generate-mocks:
	@echo "生成 Mock 文件..."
	@echo "确保已安装 mockgen: go install github.com/golang/mock/mockgen@latest"

	# 创建 mock 目录
	mkdir -p internal/mocks/mock_commentary_svc
	mkdir -p internal/mocks/mock_commentary_repo
	mkdir -p internal/mocks/mock_commentary_ctrl

	# 生成 service mocks
	mockgen -source=internal/service/commentary_svc/icommentary_main_task.go \
		-destination=internal/mocks/mock_commentary_svc/commentary_main_task.go \
		-package=mock_commentary_svc

	@echo "Mock 文件生成完成!"

# 数据库设置
db-setup:
	@echo "设置测试数据库..."
	@echo "请确保 MySQL 服务正在运行，并且已创建以下数据库："
	@echo "  - test (用于本地测试)"
	@echo "  - test_integration (用于集成测试)"
	@echo ""
	@echo "创建数据库的 SQL 命令："
	@echo "  CREATE DATABASE IF NOT EXISTS test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
	@echo "  CREATE DATABASE IF NOT EXISTS test_integration CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行特定测试函数
test-func:
	@if [ -z "$(FUNC)" ]; then \
		echo "请指定测试函数: make test-func FUNC=TestFunctionName"; \
	else \
		echo "运行测试函数: $(FUNC)"; \
		go test -v ./... -run $(FUNC); \
	fi

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  build             - 构建项目"
	@echo "  run               - 运行项目"
	@echo "  clean             - 清理构建文件"
	@echo "  test              - 运行所有测试"
	@echo "  test-unit         - 运行单元测试（快速）"
	@echo "  test-integration  - 运行集成测试（需要数据库）"
	@echo "  test-mock         - 运行 Mock 测试"
	@echo "  test-performance  - 运行性能测试"
	@echo "  test-coverage     - 运行测试并生成覆盖率报告"
	@echo "  generate-mocks    - 生成 Mock 文件"
	@echo "  db-setup          - 设置测试数据库"
	@echo "  test-func FUNC=   - 运行特定测试函数"
