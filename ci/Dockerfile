# 使用Go 1.23.1版本的官方镜像作为基础镜像
FROM golang:1.23.1-alpine as builder

# 更换APK源为清华大学的镜像
RUN sed -i 's|http://dl-cdn.alpinelinux.org/alpine/|https://mirrors.tuna.tsinghua.edu.cn/alpine/|g' /etc/apk/repositories

# 设置工作目录
WORKDIR /build

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖项
RUN GOPROXY=https://goproxy.cn go mod download

# 复制源代码
COPY . .

# 构建应用程序
# CGO_ENABLED=0，创建一个静态链接的二进制文件，不依赖于运行时的C库
RUN CGO_ENABLED=0 go build -o /build/x-video-translator main.go
RUN chmod +x /build/x-video-translator

# 最终的运行阶段
FROM cr.ttyuyin.com/x-project/ubuntu:22-ffmpeg6

# 创建应用程序和配置文件所需的目录结构
RUN mkdir -p /app/bin && \
    mkdir -p /app/configs && \
    mkdir -p /app/fonts && \
    mkdir -p /app/data

# 设置时区
ENV TZ=Asia/Shanghai

# 从构建阶段复制可执行文件到最终镜像
COPY --from=builder /build/x-video-translator /app/bin/x-video-translator

# 复制配置文件到最终镜像
COPY configs/config.dev.yaml /app/configs/config.dev.yaml
COPY configs/config.test.yaml /app/configs/config.test.yaml
COPY configs/config.prod.yaml /app/configs/config.prod.yaml

# 复制字体
COPY fonts /app/fonts/

# 定义构建时的环境变量
ARG CONFIG_ENV=dev

# 根据构建时的环境变量设置运行时的环境变量
ENV CONFIG_ENV=$CONFIG_ENV

# HTTP端口
EXPOSE 28000

# 运行服务
CMD ["/bin/sh", "-c", "env; mkdir -p ~/.fonts && cp /app/fonts/*.ttf ~/.fonts && fc-cache -f -v && /app/bin/x-video-translator --config=/app/configs/config.${CONFIG_ENV}.yaml"]
