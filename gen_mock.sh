# service
mockgen -source ./internal/service/hello_svc/hello_svc.go -destination ./internal/mocks/mock_hello_svc/hello_svc.go

mockgen -source=internal/service/commentary_svc/icommentary_main_task.go -destination=internal/mocks/mock_commentary_svc/commentary_main_task.go -package=mock_commentary_svc

# repo
mockgen -source ./internal/repo/hello_repo/hello_repo.go -destination ./internal/mocks/mock_hello_repo/hello_repo.go