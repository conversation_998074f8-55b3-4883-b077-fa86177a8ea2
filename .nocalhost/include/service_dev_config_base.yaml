devContainerName: service
gitUrl: ""
image: cr.ttyuyin.com/tt_oss/tt-golang-dev:202505090945
shell: ""
workDir: ""
storageClass: ""
resources:
  limits:
    memory: 8Gi
    cpu: "8"
  requests:
    memory: 1Gi
    cpu: "512m"
persistentVolumeDirs: []
debug:
  remoteDebugPort: 9999
  language: go
env:
  - name: GONOSUMDB
    value: gitlab.ttyuyin.com
  - name: GOPROXY
    value: https://goproxy.ttyuyin.com