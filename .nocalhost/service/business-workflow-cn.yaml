- name: business-workflow-cn # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./main.go
            - --config=./configs/config.test.yaml
          debug: # debug参数
            ${_INCLUDE_:- ../include/debug_args.yaml | nindent 12}
            - ./main.go
            - -- --config=./configs/config.test.yaml -- --timer_config_file=./configs/timer-dye.json
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "./" # 服务代码目录
          ignoreFilePattern: [ ]
        env:
          - name: DYEING_ENVIRONMENT_MARK
            value: T3344
          - name: CONFIG_ENV
            value: pre
        portForward: []
