package vo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// 重命名任务
type RenameSubtitleReq struct {
	g.Meta `path:"/commentary/rename_subtitle" tags:"CommentarySubTask" method:"post" summary:"重命名任务"`
	common.BaseParam
	Tid     int64  `json:"tid,string" dc:"任务ID"`
	NewName string `json:"new_name" dc:"新任务名称"`
}
type RenameSubtitleRes struct {
}

// CommentarySubTaskGetReq 获取解说子任务请求
type CommentarySubTaskGetReq struct {
	g.Meta `path:"/commentary/get_sub_task" tags:"CommentarySubTask" method:"post" summary:"获取解说子任务详情"`
	common.BaseParam
	SubTaskId int64 `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
}

type CommentarySubTaskReplaceBgmReq struct {
	g.Meta `path:"/commentary/replace_sub_task_bgm" tags:"CommentarySubTask" method:"post" summary:"更换解说子任务背景音"`
	common.BaseParam
	SubTaskId int64  `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	BgmUrl    string `json:"bgm_url" v:"required#背景音不能为空"`
}

type CommentarySubTaskReplaceBgmRes struct {
	Success bool `json:"success"`
}

// SaveSubtitleModeReq 保存字幕开关状态请求
type SaveSubtitleModeReq struct {
	g.Meta `path:"/commentary/save_subtitle_mode" tags:"CommentarySubTask" method:"post" summary:"保存字幕开关状态"`
	common.BaseParam
	Tid          int64 `json:"tid,string"`    // 任务ID
	SubtitleMode int32 `json:"subtitle_mode"` // 字幕开关状态 0:开启 1:关闭
}

// SaveSubtitleModeRes 保存字幕开关状态响应
type SaveSubtitleModeRes struct{}

// 合成视频
type MergeReq struct {
	g.Meta `path:"/commentary/merge_sync" tags:"CommentarySubTask" method:"post" summary:"合成视频"`
	common.BaseParam
	Tid         int64 `json:"tid,string"`
	Mode        int32 `json:"mode"`          //字幕开关（0：开启；1：关闭）
	LipSync     int32 `json:"lip_sync"`      // （1：关闭；2：开启；0：保持与上次一致）
	IsSkipMerge bool  `json:"is_skip_merge"` // 是否跳过合成 直接下载上次合成视频
}

type MergeRes struct {
	PostVideoUrl string `json:"post_video_url"`
}

// CommentarySubTaskUpdateBgmReq 更新解说子任务背景音请求
type CommentarySubTaskUpdateBgmReq struct {
	g.Meta `path:"/commentary/update_sub_task_bgm" tags:"CommentarySubTask" method:"post" summary:"更新解说子任务背景音"`
	common.BaseParam
	SubTaskId int64  `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	BgmUrl    string `json:"bgm_url" v:"required#背景音不能为空"`
}

// CommentarySubTaskUpdateBgmRes 更新解说子任务背景音响应
type CommentarySubTaskUpdateBgmRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmModeReq 更新子任务BGM模式请求
type UpdateSubTaskBgmModeReq struct {
	g.Meta `path:"/commentary/update_bgm_config" tags:"CommentarySubTask" method:"post" summary:"更新解说子任务BGM模式"`
	common.BaseParam
	Tid        int64 `json:"tid,string"`  // 任务ID
	DisableBGM int8  `json:"disable_bgm"` // 是否禁用BGM 0: 否  1: 是
}

// UpdateSubTaskBgmModeRes 更新子任务BGM模式响应
type UpdateSubTaskBgmModeRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskStatusReq 更新子任务状态请求
type UpdateSubTaskStatusReq struct {
	SubTaskId int64  `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	Status    int    `json:"status" v:"required#状态不能为空"`
	ErrMsg    string `json:"err_msg"`
}

// UpdateSubTaskStatusRes 更新子任务状态响应
type UpdateSubTaskStatusRes struct {
	Success bool `json:"success"`
}

// DeleteSubTaskReq 删除子任务请求
type DeleteSubTaskReq struct {
	SubTaskId int64 `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
}

// DeleteSubTaskRes 删除子任务响应
type DeleteSubTaskRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskEraseModeReq 更新子任务擦除模式请求
type UpdateSubTaskEraseModeReq struct {
	SubTaskId    int64 `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	EraseMode    int   `json:"erase_mode" v:"required#擦除模式不能为空"`
	EraseEdition int   `json:"erase_edition" v:"required#擦除版本不能为空"`
}

// UpdateSubTaskEraseModeRes 更新子任务擦除模式响应
type UpdateSubTaskEraseModeRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskOcrRectInfoReq 更新子任务OCR区域信息请求
type UpdateSubTaskOcrRectInfoReq struct {
	SubTaskId   int64       `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	OcrRectInfo interface{} `json:"ocr_rect_info" v:"required#OCR区域信息不能为空"`
}

// UpdateSubTaskOcrRectInfoRes 更新子任务OCR区域信息响应
type UpdateSubTaskOcrRectInfoRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmModeByMainTaskIdReq 根据主任务ID更新所有子任务的BGM模式请求
type UpdateSubTaskBgmModeByMainTaskIdReq struct {
	MainTaskId int64 `json:"main_task_id,string" v:"required#主任务ID不能为空"`
	BgmMode    int   `json:"bgm_mode" v:"required#BGM模式不能为空"`
}

// UpdateSubTaskBgmModeByMainTaskIdRes 根据主任务ID更新所有子任务的BGM模式响应
type UpdateSubTaskBgmModeByMainTaskIdRes struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmModeReq2 更新单个子任务BGM模式请求
type UpdateSubTaskBgmModeReq2 struct {
	SubTaskId int64 `json:"sub_task_id,string" v:"required#子任务ID不能为空"`
	BgmMode   int   `json:"bgm_mode" v:"required#BGM模式不能为空"`
}

// UpdateSubTaskBgmModeRes2 更新单个子任务BGM模式响应
type UpdateSubTaskBgmModeRes2 struct {
	Success bool `json:"success"`
}

// CommentarySubTaskRes 获取解说子任务响应
type GetSubTaskInfoRes struct {
	*CommentarySubTaskVO
}

type GetSubTaskInfoReq struct {
	g.Meta `path:"/commentary/get_sub_task_info" tags:"CommentarySubTask" method:"post" summary:"获取解说子任务详情"`
	common.BaseParam
	Tid int64 `json:"tid,string" v:"required#子任务ID不能为空"`
}

// 批量查询子任务状态
type BatchGetSubTaskStatusReq struct {
	g.Meta `path:"/commentary/batch_get_sub_task_status" tags:"CommentarySubTask" method:"post" summary:"批量查询子任务状态"`
	common.BaseParam
	Tids []string `json:"tids" v:"required#子任务ID不能为空"`
}

type BatchGetSubTaskStatusRes struct {
	SubTaskList []*CommentarySubTaskVO `json:"sub_task_list"`
}

// 批量合成视频请求
type MergeBatchReq struct {
	g.Meta `path:"/commentary/merge_batch" tags:"CommentarySubTask" method:"post" summary:"批量合成视频"`
	common.BaseParam
	Tasks []MergeBatchTaskItem `json:"tasks"` // 任务列表
}

// 批量合成视频响应
type MergeBatchRes struct {
	Items []MergeBatchItemRes `json:"items"`
}

// 批量合成视频响应项
type MergeBatchItemRes struct {
	Id           int64  `json:"id,string"`      // 任务ID
	PostVideoUrl string `json:"post_video_url"` // 合成后的视频URL
	Status       int32  `json:"status"`         // 状态：0-处理中，1-成功，2-失败
}

// 批量合成视频任务项
type MergeBatchTaskItem struct {
	Tid int64 `json:"tid,string"` // 任务ID
	//Mode    int32 `json:"mode"`               // 字幕开关（0：开启；1：关闭）
	//LipSync int32 `json:"lip_sync,omitempty"` // （1：关闭；2：开启；0：保持与上次一致）
}

type CommentarySubTaskVO struct {
	Id                        int64                          `json:"id,string"`
	MainTaskId                int64                          `json:"main_task_id,string"`
	Name                      string                         `json:"name"`
	SpeakerId                 int64                          `json:"speaker_id,string"`
	VoiceId                   int64                          `json:"voice_id,string"`
	TenantId                  int64                          `json:"tenant_id,string"`
	VideoDuration             float64                        `json:"video_duration"`
	MaterialHighlightDuration float64                        `json:"material_highlight_duration"`
	MergeStatus               consts.CommentaryMergeStatus   `json:"merge_status" comment:"0-待合成，1-合成中，2-已合成，3-合成失败"`
	Status                    consts.CommentarySubTaskStatus `json:"status" comment:"0-待执行，1-执行中，2-成功，3-失败"`
	AuditStatus               consts.AuditStatus             `json:"audit_status" comment:"0-待审核，1-审核中，2-成功，3-不通过，4-异常"`
	ErrCode                   int                            `json:"err_code" comment:"异常码"`
	ErrMsg                    string                         `json:"err_msg"`
	EraseMode                 consts.CommentaryEraseMode     `json:"erase_mode" comment:"擦除模式：0-关，1-开"`
	EraseEdition              consts.CommentaryEraseEdition  `json:"erase_edition" comment:"擦除版本：0-普通，1-专业"`
	OcrRectInfo               *OcrRectInfoVO                 `json:"ocr_rect_info,omitempty" comment:"OCR区域信息"`
	BgmMode                   consts.CommentaryBgmMode       `json:"bgm_mode" comment:"BGM模式：0-关，1-开"`
	SubtitleMode              consts.CommentarySubtitleMode  `json:"subtitle_mode" comment:"字幕模式：0-关，1-开"`
	NoSubtitleVideoUrl        string                         `json:"no_subtitle_video_url"`
	NoSubtitleVideoHlsUrl     string                         `json:"no_subtitle_video_hls_url"`
	MergedVideoUrl            string                         `json:"merged_video_url"`
	MergedVideoHlsUrl         string                         `json:"merged_video_hls_url"`
	Size                      float64                        `json:"size"`
	Resolution                string                         `json:"resolution"`
	CoverUrl                  string                         `json:"cover_url"`
	CustomSubtitleStyle       *common.CustomSubtitleStyle    `json:"custom_subtitle_style,omitempty"`
	VideoSegmentInfo          []*VideoSegmentInfoVO          `json:"video_segment_info,omitempty"`
	BgmUrl                    string                         `json:"bgm_url"`
	CommentaryAgentRes        *CommentaryAgentResVO          `json:"commentary_agent_res,omitempty"`
	MaterialHighlightUrl      string                         `json:"material_highlight_url"`
	HighlightEpisodesUrls     []string                       `json:"highlight_episodes_urls"`
	EndTagUrl                 string                         `json:"end_tag_url"`
	SubtitleFileUrl           string                         `json:"subtitle_file_url"`
	NeedMerge                 int8                           `json:"need_merge"`
	SourceLangId              string                         `json:"source_lang_id"`
	TargetLangId              string                         `json:"target_lang_id"`
	CreatedAt                 time.Time                      `json:"created_at"`
	UpdatedAt                 time.Time                      `json:"updated_at"`
	SubtitleItemList          []*SubtitleItem                `json:"subtitle_item_list,omitempty"`
}

// VideoSegmentInfoVO 视频片段信息VO
type VideoSegmentInfoVO struct {
	StartTime   string  `json:"start_time"`
	EndTime     string  `json:"end_time"`
	SourceUrl   string  `json:"source_url"`
	Duration    float64 `json:"duration"`
	SegmentType int     `json:"segment_type" dc:"1:总时长 2:素材 3:高光剧集 4:落版"`
}

// SecondSourceUrlsVO 高光剧集VO
type SecondSourceUrlsVO struct {
	Sources []string `json:"sources"`
}

// CommentaryAgentResVO 解说模型返回结果VO
type CommentaryAgentResVO struct {
	TotalContext   string                  `json:"total_context"`
	TimeFrameRange []CommentaryTimeFrameVO `json:"time_frame_range"`
}

// CommentaryTimeFrameVO 解说时间帧VO
type CommentaryTimeFrameVO struct {
	StartTimestamp string `json:"start_timestamp"`
	EndTimestamp   string `json:"end_timestamp"`
	Context        string `json:"context"`
	Episode        string `json:"episode"`
}

// SaveGlobalSubtitleStyleReq 保存全局字幕样式请求
type SaveGlobalSubtitleStyleReq struct {
	g.Meta `path:"/commentary/subtitle_item/save_global_subtitle_style" tags:"CommentarySubTask" method:"post" summary:"保存全局字幕样式"`
	common.BaseParam
	Tid                 int64                       `json:"tid,string"`            // 任务id
	CustomSubtitleStyle *common.CustomSubtitleStyle `json:"custom_subtitle_style"` // 字幕样式
	Subtitles           []struct {
		ItemId      int64        `json:"item_id,string"` // 片段id
		SubItemList []*SubItemVO `json:"sub_item_list"`  // 字幕项
	} `json:"subtitles"` //有修改过的片段
}

type SubItemVO struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Text      string `json:"text"`
}

// SaveGlobalSubtitleStyleRes 保存全局字幕样式响应
type SaveGlobalSubtitleStyleRes struct {
}
