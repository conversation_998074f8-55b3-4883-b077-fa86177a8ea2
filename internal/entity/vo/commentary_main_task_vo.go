package vo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// CommentaryMainTaskCreateReq 创建解说主任务请求
// @Description 创建解说主任务的请求参数
type CommentaryMainTaskCreateReq struct {
	g.Meta `path:"/commentary/task_submit" tags:"CommentaryMainTask" method:"post" summary:"创建解说主任务"`
	common.BaseParam
	Name                        string                         `json:"name" dc:"任务名称"`
	TaskType                    consts.CommentaryTaskType      `json:"task_type" v:"required#任务类型不能为空" dc:"任务类型"`
	BizMode                     consts.CommentaryBizMode       `json:"biz_type" dc:"业务模式"`
	BgmMode                     consts.CommentaryBgmMode       `json:"bgm_mode" dc:"背景音模式"`
	SubtitleMode                consts.CommentarySubtitleMode  `json:"subtitle_mode" dc:"字幕模式 0:关闭 1:开启"`
	EraseMode                   consts.CommentaryEraseMode     `json:"erase_mode" dc:"擦除模式 0 无擦除 1: 字幕擦除 2:手动选框 3: 全屏"`
	EraseEdition                consts.CommentaryEraseEdition  `json:"erase_edition" dc:"擦除版本 0 无擦擦 1:普通 2:专业"`
	TextRect                    []int32                        `json:"text_rect" dc:"ocr 区域" required:"true"` // 4个 textRectRatio: [20, 60, 60, 30],  按比率 x1 y2  x2 y2 代表左上角和右下角的坐标比率百分比
	TargetLangId                string                         `json:"target_lang_id" v:"required#目标语言不能为空" dc:"目标语言ID"`
	AspectRatio                 string                         `json:"aspect_ratio" v:"required#高宽比不能为空" dc:"视频宽高比"`
	TargetDurationRangeType     consts.TargetDurationRangeType `json:"target_duration_range_type" v:"required#预估时长区间类型不能为空" dc:"预估时长区间类型(1:1min以内 2:1-3min 3:3-5min 4:5-10min)"`
	TargetDurationRangeStr      string                         `json:"target_duration_range_str" dc:"预估时长区间字符串"`
	TargetNumber                int                            `json:"target_number" v:"required#生成视频数量不能为空" dc:"生成视频数量"`
	VoiceIDList                 []int64                        `json:"voice_id_list" dc:"音色id列表"`
	MaterialSourceList          []*MaterialSourceVO            `json:"material_highlight_source_list" dc:"素材源列表"`
	HighlightEpisodesSourceList []*HighlightEpisodesSource     `json:"highlight_episodes_source_list" dc:"高光剧集源列表"`
	EndTagSourceList            []*MainTaskCommonSourceVO      `json:"end_tag_source_list" dc:"结尾标签源列表"`
	BgmSourceList               []*MainTaskCommonSourceVO      `json:"bgm_source_list" dc:"背景音源列表"`
	DubbingType                 int                            `json:"dubbing_type,omitempty"` // 配音类型 0-标准配音 2-11labs配音
}

type HighlightEpisodesSource struct {
	SourceList []*MainTaskCommonSourceVO `json:"source_list" dc:"高光剧集源列表"`
}

type MaterialSourceVO struct {
	MainTaskCommonSourceVO
	NoSubtitlePreference int `json:"no_subtitle_preference" dc:"优先无字幕 1: 是 0:否"`
}

type MainTaskCommonSourceVO struct {
	Name           string                `json:"name"`
	SourceUrl      string                `json:"source_url" v:"required#资源地址不能为空"`
	SourceFileType consts.SourceFileType `json:"source_file_type" dc:"资源类型 1:图片 2:视频 3:音频"` // 资源类型：图片,视频,音频
	FromType       int                   `json:"from_type" dc:"1-本地,2-视频翻译"`
}

// CommentaryMainTaskCreateRes 创建解说主任务响应
type CommentaryMainTaskCreateRes struct {
	Id int64 `json:"id,string"`
}

// OcrRectInfoVO OCR区域信息VO
type OcrRectInfoVO struct {
	TopLeft     []float64 `json:"topLeft"`     // 左上角坐标 [x, y]
	TopRight    []float64 `json:"topRight"`    // 右上角坐标 [x, y]
	BottomLeft  []float64 `json:"bottomLeft"`  // 左下角坐标 [x, y]
	BottomRight []float64 `json:"bottomRight"` // 右下角坐标 [x, y]
}
type GetCommentaryTaskHistoryReq struct {
	g.Meta `path:"/commentary/history" tags:"CommentaryMainTask" method:"post" summary:"租户历史任务查询"`
	common.BaseParam
	Page                   int                    `json:"page" p:"page" v:"min:1#页码不能小于1"`
	PageSize               int                    `json:"page_size" p:"page_size" v:"min:1|max:100#每页数量不能小于1|每页数量不能大于100"`
	CommentaryTaskFilterVO CommentaryTaskFilterVO `json:"filter"`
}

// 筛选条件
type CommentaryTaskFilterVO struct {
	TargetLangIdList []string                        `json:"target_lang_id_list"`
	BizModeList      []consts.CommentaryBizMode      `json:"biz_mode_list"`
	EraseEditionList []consts.CommentaryEraseEdition `json:"erase_edition_list"`
	AspectRatioList  []string                        `json:"aspect_ratio_list"`
}

type GetCommentaryTaskInfoRes struct {
	CommentaryTaskList []*CommentaryMainTaskVO `json:"commentary_task_list"`
}

// BatchGetCommentaryTasksByOrderIdsReq 根据订单ID批量获取解说任务请求
type BatchGetCommentaryTasksByOrderIdsReq struct {
	g.Meta `path:"/commentary/batch_get_tasks_by_order_ids" tags:"CommentaryMainTask" method:"post" summary:"根据订单ID批量获取解说任务"`
	common.BaseParam
	OrderIds []string `json:"order_ids" v:"required#订单ID列表不能为空" dc:"订单ID列表"`
}

// BatchGetCommentaryTasksByOrderIdsRes 根据订单ID批量获取解说任务响应
type BatchGetCommentaryTasksByOrderIdsRes struct {
	TaskList []*CommentaryTaskDetails `json:"task_list" dc:"任务详情列表"`
}

// CommentaryTaskDetails 解说任务详情
type CommentaryTaskDetails struct {
	TenantId    int64  `json:"tenant_id,string" dc:"租户ID"`
	Status      int    `json:"status" dc:"任务状态"`
	TaskId      int64  `json:"task_id,string" dc:"任务ID"`
	TaskName    string `json:"task_name" dc:"任务名称"`
	ProjectName string `json:"project_name" dc:"项目名称"`
	CreateTime  string `json:"create_time" dc:"创建时间"`
	Duration    int    `json:"duration" dc:"时长（秒）"`
	BizMode     int    `json:"biz_mode" dc:"业务模式"`
}

// CommentaryMainTaskVO 解说主任务VO
type CommentaryMainTaskVO struct {
	Id                      int64                           `json:"id,string"` // 主任务ID
	AppId                   int64                           `json:"appid"`
	Name                    string                          `json:"name"`                       // 任务名称
	TenantId                int64                           `json:"tenant_id,string"`           // 租户ID
	TaskType                consts.CommentaryTaskType       `json:"task_type"`                  // 任务类型：解说/高光剪辑
	BizMode                 consts.CommentaryBizMode        `json:"biz_mode"`                   // 业务类型：组合
	BgmMode                 consts.CommentaryBgmMode        `json:"bgm_mode"`                   // BGM模式：0-关，1-开
	SubtitleMode            consts.CommentarySubtitleMode   `json:"subtitle_mode"`              // 字幕模式：0-关，1-开
	EraseMode               consts.CommentaryEraseMode      `json:"erase_mode"`                 // 擦除模式：0-关，1-开
	EraseEdition            consts.CommentaryEraseEdition   `json:"erase_edition"`              // 擦除版本：0-普通，1-专业
	OcrRectInfo             *common.OcrRectInfo             `json:"ocr_rect_info"`              // OCR区域信息
	TargetLangId            string                          `json:"target_lang_id"`             // 目标语言
	TargetResolution        string                          `json:"target_resolution"`          // 分辨率
	AspectRatio             string                          `json:"aspect_ratio"`               // 高宽比：16:9，9:16，4:3等
	TargetDurationRangeType consts.TargetDurationRangeType  `json:"target_duration_range_type"` // 预估时长区间类型
	TargetDurationRangeStr  string                          `json:"target_duration_range_str"`  // 预估时长字符串
	Status                  consts.CommentaryMainTaskStatus `json:"status"`                     // 状态：0-待执行，1-执行中，2-完成
	ErrMsg                  string                          `json:"err_msg"`                    // 异常信息
	PayOrderId              string                          `json:"pay_order_id"`               // 支付订单ID
	PayOrderStatus          consts.PayOrderStatus           `json:"pay_order_status"`           // 支付订单状态 0-未支付 1-已支付
	TargetNumber            int                             `json:"target_number"`              // 生成视频数量
	VoiceIDList             []int64                         `json:"voice_id_list"`              // 音色id列表
	DubbingType             int                             `json:"dubbing_type"`               // 配音类型 0-标准配音 2-11labs配音
	RequestID               string                          `json:"request_id"`                 // 请求ID
	CreatedAt               *time.Time                      `json:"created_at"`                 // 创建时间
	UpdatedAt               *time.Time                      `json:"updated_at"`                 // 更新时间
	SubTasks                []*CommentarySubTaskVO          `json:"sub_tasks"`                  // 子任务列表
}

// ConvertTextRectToOcrRectInfo 将TextRect转换为OcrRectInfo
// aspectRatio: 高宽比，如 "16:9", "9:16"
func ConvertTextRectToOcrRectInfo(textRect []int32, aspectRatio string) *OcrRectInfoVO {
	if len(textRect) != 4 {
		return nil
	}

	var width, height float64

	// 根据高宽比确定分辨率
	switch aspectRatio {
	case "16:9":
		width, height = 1280, 720
	case "9:16":
		width, height = 720, 1280
	default:
		// 默认使用9:16
		width, height = 720, 1280
	}

	// 将比例坐标转换为实际像素坐标
	x1 := float64(textRect[0]) / 100.0 * width
	y1 := float64(textRect[1]) / 100.0 * height
	x2 := float64(textRect[2]) / 100.0 * width
	y2 := float64(textRect[3]) / 100.0 * height

	return &OcrRectInfoVO{
		TopLeft:     []float64{x1, y1},
		TopRight:    []float64{x2, y1},
		BottomLeft:  []float64{x1, y2},
		BottomRight: []float64{x2, y2},
	}
}
