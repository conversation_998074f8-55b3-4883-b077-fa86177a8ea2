package vo

import "github.com/gogf/gf/v2/frame/g"

type HelloReq struct {
	g.<PERSON>a `path:"/hello" tags:"Hello" method:"get" summary:"You first hello api"`
}
type HelloRes struct {
	g.<PERSON>a `mime:"text/html" example:"string"`
}

type TestReq struct {
	g.Meta `path:"/test" tags:"test" method:"get" summary:"You first hello api"`
}

type TestRes struct {
	g.Meta `mime:"text/html" example:"string"`
}
type DubbingReq struct {
	g.Meta      `path:"/dubbing" tags:"Hello" method:"get" summary:"dubbing workflow"`
	Concurrency int `json:"concurrency"`
}
type DubbingRes struct {
	WorkflowID string `json:"workflow_id"`
	RunID      string `json:"run_id"`
}
