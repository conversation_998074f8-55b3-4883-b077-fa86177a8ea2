package common

// 入参
type CreateCommentaryQiFeiReq struct {
	Urls           string `json:"urls"`
	OriginLanguage string `json:"origin_language"` // 原语言，现在仅支持 zh
	Timeline       int    `json:"timeline"`        // 生成的解说长度预期值，单位秒
}

// 回参
type QueryCommentaryQiFeiResp struct {
	Code      int                           `json:"code"`
	Msg       string                        `json:"msg"`
	RequestID string                        `json:"requestId"`
	Data      *QueryCommentaryQiFeiRespData `json:"data"`
}

type QueryCommentaryQiFeiRespData struct {
	TaskID string                 `json:"task_id"`
	Type   string                 `json:"type"`
	Result *CommentaryQiFeiResult `json:"result"`
}

type CommentaryQiFeiResult struct {
	TimeFrameRange []*CommentaryQiFeiTimeFrame `json:"time_frame_range"`
	TotalContext   string                      `json:"total_context"`
}

type CommentaryQiFeiTimeFrame struct {
	Context        string `json:"context"`
	EndTimestamp   string `json:"end_timestamp"`
	Episode        string `json:"episode"`
	StartTimestamp string `json:"start_timestamp"`
}
