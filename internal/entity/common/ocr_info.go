package common

// OcrRectInfo OCR区域信息通用结构体
// 统一用于 DO、BO、VO 层
type OcrRectInfo struct {
	TopLeft     []float64 `json:"topLeft" gorm:"type:json"`     // 左上角坐标 [x, y]
	TopRight    []float64 `json:"topRight" gorm:"type:json"`    // 右上角坐标 [x, y]
	BottomLeft  []float64 `json:"bottomLeft" gorm:"type:json"`  // 左下角坐标 [x, y]
	BottomRight []float64 `json:"bottomRight" gorm:"type:json"` // 右下角坐标 [x, y]
}

// Clone 深拷贝 OcrRectInfo
func (o *OcrRectInfo) Clone() *OcrRectInfo {
	if o == nil {
		return nil
	}

	cloned := &OcrRectInfo{}

	// 深拷贝坐标数组
	if o.TopLeft != nil {
		cloned.TopLeft = make([]float64, len(o.TopLeft))
		copy(cloned.TopLeft, o.TopLeft)
	}

	if o.TopRight != nil {
		cloned.TopRight = make([]float64, len(o.TopRight))
		copy(cloned.TopRight, o.TopRight)
	}

	if o.BottomLeft != nil {
		cloned.BottomLeft = make([]float64, len(o.BottomLeft))
		copy(cloned.BottomLeft, o.BottomLeft)
	}

	if o.BottomRight != nil {
		cloned.BottomRight = make([]float64, len(o.BottomRight))
		copy(cloned.BottomRight, o.BottomRight)
	}

	return cloned
}

// IsEmpty 检查 OcrRectInfo 是否为空
func (o *OcrRectInfo) IsEmpty() bool {
	if o == nil {
		return true
	}

	return len(o.TopLeft) == 0 && len(o.TopRight) == 0 &&
		len(o.BottomLeft) == 0 && len(o.BottomRight) == 0
}

// IsValid 检查 OcrRectInfo 是否有效
// 有效的条件：每个角的坐标都应该有2个元素 [x, y]
func (o *OcrRectInfo) IsValid() bool {
	if o.IsEmpty() {
		return false
	}

	return len(o.TopLeft) == 2 && len(o.TopRight) == 2 &&
		len(o.BottomLeft) == 2 && len(o.BottomRight) == 2
}
