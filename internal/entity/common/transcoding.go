package common

type VideoTranscodingRequest struct {
	VideoParams []*VideoParam `json:"video_params"` // 视频参数
}

type VideoParam struct {
	VideoInfos    []*VideoInfo `json:"video_infos"`     // 视频信息
	FPS           int          `json:"fps"`             // 默认30
	CRF           int          `json:"crf"`             // 控制视频质量（范围0-51，值越小质量越高），默认28
	Resolution    string       `json:"resolution"`      // 分辨率1280x720
	Mode          int          `json:"mode"`            // 模式，0:默认只转码，1: 转码并合并视频
	MergeVideoUrl string       `json:"merge_video_url"` // 合成视频url，mode=1时必填
}

type VideoInfo struct {
	ID             int64  `json:"id"`
	OriginVideoUrl string `json:"origin_video_url"`
	ResultVideoUrl string `json:"result_video_url"`
}

type VideoTranscodingResponse struct {
	Code    int    `json:"code"` // 1000成功
	Message string `json:"message"`
}
