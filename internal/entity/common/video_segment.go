package common

import (
	"strconv"
	"strings"
)

// VideoSegmentInfo 视频片段信息集合
type VideoSegmentInfo struct {
	StartTime   string  `json:"start_time" gorm:"column:start_time"`     // 开始时间
	EndTime     string  `json:"end_time" gorm:"column:end_time"`         // 结束时间
	SourceUrl   string  `json:"source_url" gorm:"column:source_url"`     // 资源地址
	Duration    float64 `json:"duration" gorm:"column:duration"`         // 时长（秒）
	SegmentType int     `json:"segment_type" gorm:"column:segment_type"` // 片段类型
}

// Clone 深拷贝视频片段信息
func (vi *VideoSegmentInfo) Clone() *VideoSegmentInfo {
	if vi == nil {
		return nil
	}

	cloned := &VideoSegmentInfo{
		StartTime:   vi.StartTime,
		EndTime:     vi.EndTime,
		SourceUrl:   vi.SourceUrl,
		Duration:    vi.Duration,
		SegmentType: vi.SegmentType,
	}

	return cloned
}

// parseTimeToSeconds 解析时间字符串为秒数
// 支持格式：
// - "123.45" (直接的秒数)
// - "HH:MM:SS" (时:分:秒)
// - "HH:MM:SS.mmm" (时:分:秒.毫秒)
// - "MM:SS" (分:秒)
// - "MM:SS.mmm" (分:秒.毫秒)
func parseTimeToSeconds(timeStr string) float64 {
	if timeStr == "" {
		return 0.0
	}

	// 尝试直接解析为浮点数（秒）
	if seconds, err := strconv.ParseFloat(timeStr, 64); err == nil {
		return seconds
	}

	// 解析时间格式
	parts := strings.Split(timeStr, ":")
	if len(parts) < 2 {
		return 0.0
	}

	var hours, minutes, seconds float64
	var err error

	switch len(parts) {
	case 2: // MM:SS 或 MM:SS.mmm
		if minutes, err = strconv.ParseFloat(parts[0], 64); err != nil {
			return 0.0
		}
		if seconds, err = strconv.ParseFloat(parts[1], 64); err != nil {
			return 0.0
		}

	case 3: // HH:MM:SS 或 HH:MM:SS.mmm
		if hours, err = strconv.ParseFloat(parts[0], 64); err != nil {
			return 0.0
		}
		if minutes, err = strconv.ParseFloat(parts[1], 64); err != nil {
			return 0.0
		}
		if seconds, err = strconv.ParseFloat(parts[2], 64); err != nil {
			return 0.0
		}

	default:
		return 0.0
	}

	return hours*3600 + minutes*60 + seconds
}

// FormatSecondsToTime 将秒数格式化为时间字符串
func FormatSecondsToTime(seconds float64) string {
	if seconds < 0 {
		return "00:00:00.000"
	}

	hours := int(seconds / 3600)
	minutes := int((seconds - float64(hours)*3600) / 60)
	remainingSeconds := seconds - float64(hours)*3600 - float64(minutes)*60

	// 使用简单的字符串格式化，避免导入 fmt
	return formatTime(hours, minutes, remainingSeconds)
}

// formatTime 格式化时间的辅助函数
func formatTime(hours int, minutes int, seconds float64) string {
	// 简单的字符串拼接，避免使用 fmt.Sprintf
	h := intToString(hours, 2)
	m := intToString(minutes, 2)
	s := floatToString(seconds, 6, 3)

	return h + ":" + m + ":" + s
}

// intToString 将整数转换为指定宽度的字符串
func intToString(num int, width int) string {
	str := strconv.Itoa(num)
	for len(str) < width {
		str = "0" + str
	}
	return str
}

// floatToString 将浮点数转换为指定格式的字符串
func floatToString(num float64, width int, precision int) string {
	str := strconv.FormatFloat(num, 'f', precision, 64)
	for len(str) < width {
		str = "0" + str
	}
	return str
}
