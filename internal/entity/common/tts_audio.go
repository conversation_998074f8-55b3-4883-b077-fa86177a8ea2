package common

import (
	"business-workflow/internal/consts"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/jinzhu/copier"
)

// LatestTTSInfo 最新TTS信息通用结构体
// 统一用于 DO、BO、VO 层
type LatestTTSInfo struct {
	TargetSubtitle   string     `json:"target_subtitle" gorm:"column:target_subtitle"`       // 富文本内容
	VoiceInfo        *VoiceInfo `json:"voice_info" gorm:"type:json"`                         // 音色信息
	IsFirstGenerated bool       `json:"is_first_generated" gorm:"column:is_first_generated"` // 是否首次生成
}

type LatestTextTranslateInfo struct {
	LatestTranslateBackText string `json:"latest_translate_back_text" gorm:"column:latest_translate_back_text"` // 最新翻译回文本
	LatestOriginSubtitle    string `json:"latest_origin_subtitle" gorm:"column:latest_origin_subtitle"`         // 最新原始字幕
}

// Clone 深拷贝 LatestTextTranslateInfo
func (l *LatestTextTranslateInfo) Clone() *LatestTextTranslateInfo {
	if l == nil {
		return nil
	}
	return &LatestTextTranslateInfo{
		LatestOriginSubtitle:    l.LatestOriginSubtitle,
		LatestTranslateBackText: l.LatestTranslateBackText,
	}
}

// VoiceInfo 音色信息通用结构体
// 音色信息
type VoiceInfo struct {
	VoiceId     int64              `json:"voice_id"`
	VoiceSource consts.VoiceSource `json:"voice_source"`
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Url         string             `json:"url"`
}

// 音频配置
type AudioConfig struct {
	Speed        float32 `json:"speed"`          // 语速
	VolumeGainDB float32 `json:"volume_gain_db"` // 音量加减值
}

// 获取语速
func (a *AudioConfig) GetSpeed() float32 {
	if a == nil {
		return 1
	}
	if a.Speed <= 0 {
		g.Log().Error(context.Background(), "AudioConfig Speed is invalid: %v, using default speed 1", a.Speed)
		return 1 // 默认语速为1
	}
	return a.Speed
}

// 获取音量
func (a *AudioConfig) GeVolumeGainDB() float32 {
	if a == nil {
		return 0
	}
	return a.VolumeGainDB
}

func NewDefaultAudioConfig() *AudioConfig {
	return &AudioConfig{
		Speed:        1, // 默认语速
		VolumeGainDB: 0, // 默认音量加减值
	}
}

// WordInfo 词语信息通用结构体
type WordInfo struct {
	Start   float64 `json:"start"`    // 词语开始时间,单位秒,相对本句，未应用语速
	End     float64 `json:"end"`      // 词语结束时间
	Word    string  `json:"word"`     // 词语内容
	WidthPx int32   `json:"width_px"` // 像素宽度
}

type LineInfo struct {
	Words []*WordInfo `json:"words"` // 词列表
}

func (w *WordInfo) GetStartMs() int64 {
	return int64(w.Start * 1000)
}

func (w *WordInfo) GetEndMs() int64 {
	return int64(w.End * 1000)
}

func (w *WordInfo) GetDurationMs() int64 {
	return w.GetEndMs() - w.GetStartMs()
}

// 行开始时间，单位毫秒，相对本句，未应用语速
func (l *LineInfo) GetStartMs() int64 {
	if len(l.Words) == 0 {
		return 0
	}
	return l.Words[0].GetStartMs()
}

func (l *LineInfo) GetEndMs() int64 {
	if len(l.Words) == 0 {
		return 0
	}
	return l.Words[len(l.Words)-1].GetEndMs()
}

func (l *LineInfo) GetText() string {
	text := ""
	for _, word := range l.Words {
		text = text + word.Word
	}
	return text
}

// SubItem 子项通用结构体
type SubItem struct {
	BeginMs     int64       `json:"begin_ms"`      // 子片段开始时间，单位毫秒，相对视频，已应用语速
	EndMs       int64       `json:"end_ms"`        // 子片段结束时间
	SubPostText string      `json:"sub_post_text"` // 子片段文本内容
	Lines       []*LineInfo `json:"lines"`         // 行列表
}

// Clone 深拷贝 LatestTTSInfo
func (l *LatestTTSInfo) Clone() *LatestTTSInfo {
	if l == nil {
		return nil
	}

	cloned := &LatestTTSInfo{
		TargetSubtitle:   l.TargetSubtitle,
		IsFirstGenerated: l.IsFirstGenerated,
	}

	if l.VoiceInfo != nil {
		cloned.VoiceInfo = l.VoiceInfo.Clone()
	}

	return cloned
}

// IsEmpty 检查 LatestTTSInfo 是否为空
func (l *LatestTTSInfo) IsEmpty() bool {
	return l == nil || (l.TargetSubtitle == "" && l.VoiceInfo == nil)
}

// Clone 深拷贝 VoiceInfo
func (v *VoiceInfo) Clone() *VoiceInfo {
	if v == nil {
		return nil
	}
	res := &VoiceInfo{}
	copier.Copy(res, v)
	return res
}

// IsEmpty 检查 VoiceInfo 是否为空
func (v *VoiceInfo) IsEmpty() bool {
	return v == nil || (v.VoiceId == 0 && v.Name == "" && v.Url == "")
}

// Clone 深拷贝 AudioConfig
func (a *AudioConfig) Clone() *AudioConfig {
	if a == nil {
		return nil
	}
	res := &AudioConfig{}
	copier.Copy(res, a)
	return res
}

// Clone 深拷贝 WordInfo
func (w *WordInfo) Clone() *WordInfo {
	if w == nil {
		return nil
	}

	return &WordInfo{
		Start: w.Start,
		End:   w.End,
		Word:  w.Word,
	}
}

// IsEmpty 检查 WordInfo 是否为空
func (w *WordInfo) IsEmpty() bool {
	return w == nil || w.Word == ""
}

// Duration 获取词语时长
func (w *WordInfo) Duration() float64 {
	if w == nil || w.End <= w.Start {
		return 0.0
	}
	return w.End - w.Start
}

// Clone 深拷贝 SubItem
func (s *SubItem) Clone() *SubItem {
	if s == nil {
		return nil
	}

	return &SubItem{
		BeginMs:     s.BeginMs,
		EndMs:       s.EndMs,
		SubPostText: s.SubPostText,
	}
}

// IsEmpty 检查 SubItem 是否为空
func (s *SubItem) IsEmpty() bool {
	return s == nil || s.SubPostText == ""
}

// Duration 获取子项时长（毫秒）
func (s *SubItem) Duration() int64 {
	if s == nil || s.EndMs <= s.BeginMs {
		return 0
	}
	return s.EndMs - s.BeginMs
}

// DurationSeconds 获取子项时长（秒）
func (s *SubItem) DurationSeconds() float64 {
	return float64(s.Duration()) / 1000.0
}

// CloneWordInfoSlice 深拷贝 WordInfo 切片
func CloneWordInfoSlice(words []*WordInfo) []*WordInfo {
	if words == nil {
		return nil
	}

	cloned := make([]*WordInfo, len(words))
	for i, word := range words {
		cloned[i] = word.Clone()
	}

	return cloned
}

// CloneSubItemSlice 深拷贝 SubItem 切片
func CloneSubItemSlice(items []*SubItem) []*SubItem {
	if items == nil {
		return nil
	}

	cloned := make([]*SubItem, len(items))
	for i, item := range items {
		cloned[i] = item.Clone()
	}

	return cloned
}
