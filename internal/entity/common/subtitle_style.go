package common

// CustomSubtitleStyle 通用字幕样式结构体
// 统一用于 DO、BO、VO 层，避免重复定义
type CustomSubtitleStyle struct {
	FontName        string `json:"FontName"`
	FontSize        int32  `json:"FontSize"`
	PrimaryColor    string `json:"PrimaryColor"`
	SecondaryColour string `json:"SecondaryColour"`
	OutlineColour   string `json:"OutlineColour"`
	BackColour      string `json:"BackColour"`
	Bold            int    `json:"Bold"`
	Italic          int    `json:"Italic"`
	Underline       int    `json:"Underline"`
	Strikeout       int    `json:"Strikeout"`
	ScaleX          int    `json:"ScaleX"`
	ScaleY          int    `json:"ScaleY"`
	Spacing         int    `json:"Spacing"`
	BorderStyle     int    `json:"BorderStyle"`
	Outline         int    `json:"Outline"`
	Shadow          int    `json:"Shadow"`
	Alignment       int    `json:"Alignment"`
	MarginL         int32  `json:"MarginL"`
	MarginR         int32  `json:"MarginR"`
	MarginV         int32  `json:"MarginV"`
	Encoding        int    `json:"Encoding"`
	Width           int32  `json:"width"`  //视频的宽高，不用前端传
	Height          int32  `json:"height"` //视频的宽高，不用前端传
	PosX            int32  `json:"PosX"`
	PosY            int32  `json:"PosY"`
	RowsLimit       int32  `json:"RowsLimit"` //行数限制
	Rect            *Rect  `json:"Rect"`      //字幕框矩形
}

// Rect 通用字幕框坐标结构体
type Rect struct {
	X1 int32 `json:"X1"` // 左上角X坐标
	Y1 int32 `json:"Y1"` // 左上角Y坐标
	X2 int32 `json:"X2"` // 右下角X坐标
	Y2 int32 `json:"Y2"` // 右下角Y坐标
}

func (r *Rect) GetWidth() int32 {
	return r.X2 - r.X1
}

// Clone 深拷贝字幕样式
func (c *CustomSubtitleStyle) Clone() *CustomSubtitleStyle {
	if c == nil {
		return nil
	}

	return &CustomSubtitleStyle{
		Alignment:       c.Alignment,
		BackColour:      c.BackColour,
		Bold:            c.Bold,
		BorderStyle:     c.BorderStyle,
		Encoding:        c.Encoding,
		FontName:        c.FontName,
		FontSize:        c.FontSize,
		Italic:          c.Italic,
		MarginL:         c.MarginL,
		MarginR:         c.MarginR,
		MarginV:         c.MarginV,
		Outline:         c.Outline,
		OutlineColour:   c.OutlineColour,
		PosX:            c.PosX,
		PosY:            c.PosY,
		PrimaryColor:    c.PrimaryColor,
		ScaleX:          c.ScaleX,
		ScaleY:          c.ScaleY,
		SecondaryColour: c.SecondaryColour,
		Shadow:          c.Shadow,
		Spacing:         c.Spacing,
		Strikeout:       c.Strikeout,
		Underline:       c.Underline,
		Height:          c.Height,
		Width:           c.Width,
		RowsLimit:       c.RowsLimit,
		Rect:            c.Rect,
	}
}

// IsEmpty 检查字幕样式是否为空
func (c *CustomSubtitleStyle) IsEmpty() bool {
	if c == nil {
		return true
	}

	// 检查关键字段是否都为默认值
	return c.FontName == "" && c.FontSize == 0 && c.PrimaryColor == ""
}

// SetDefaults 设置默认值 等1.3.3后续调整视频翻译一致
func (c *CustomSubtitleStyle) SetDefaults() {
	if c == nil {
		return
	}

	if c.FontName == "" {
		c.FontName = "Arial"
	}
	if c.FontSize == 0 {
		c.FontSize = 16
	}
	if c.PrimaryColor == "" {
		c.PrimaryColor = "#FFFFFF"
	}
	if c.Alignment == 0 {
		c.Alignment = 2 // 居中对齐
	}
}

// Clone 深拷贝坐标
func (r *Rect) Clone() Rect {
	return Rect{
		X1: r.X1,
		Y1: r.Y1,
		X2: r.X2,
		Y2: r.Y2,
	}
}

// IsEmpty 检查坐标是否为空
func (r *Rect) IsEmpty() bool {
	return r.X1 == 0 && r.Y1 == 0 && r.X2 == 0 && r.Y2 == 0
}

// Width 获取宽度
func (r *Rect) Width() int32 {
	if r.X2 > r.X1 {
		return r.X2 - r.X1
	}
	return 0
}

// Height 获取高度
func (r *Rect) Height() int32 {
	if r.Y2 > r.Y1 {
		return r.Y2 - r.Y1
	}
	return 0
}
