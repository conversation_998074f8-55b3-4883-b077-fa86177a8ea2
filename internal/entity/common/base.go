package common

type BaseParam struct {
	Appid    int64  `json:"appid,string" p:"appid" v:"required"`         //appid
	TenantId int64  `json:"tenant_id,string" p:"tenant_id" v:"required"` //租户id
	ApiKey   string `json:"api_key" p:"api_key"`                         //api_key
}

type GhostBalancePointAsset struct {
	Company      string  `json:"company"`
	Ctime        int64   `json:"ctime"`
	ExpireTime   int64   `json:"expireTime"`
	Id           int64   `json:"id"`
	PointAmount  float64 `json:"pointAmount"`
	PointBalance float64 `json:"pointBalance"`
	Remark       string  `json:"remark"`
}

type GhostBalanceBody struct {
	Body []GhostBalancePointAsset `json:"pointAssets"`
}

type GhostBalance struct {
	Code  int              `json:"code"`
	Msg   string           `json:"msg"`
	Trace string           `json:"trace"`
	Body  GhostBalanceBody `json:"body"`
}

type CommonDefaultRes struct {
}
