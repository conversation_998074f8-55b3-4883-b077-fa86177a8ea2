package do

import (
	"business-workflow/internal/entity/common"
	"time"

	"gorm.io/gorm"
)

// 解说字幕表
type CommentarySubtitleSnapshotItem struct {
	Id                         int64                           `gorm:"column:id;primary_key" json:"id"`
	SubTaskId                  int64                           `gorm:"column:sub_task_id;type:bigint;not null;index" json:"sub_task_id"`                               // 子任务ID
	MainTaskId                 int64                           `gorm:"column:main_task_id;type:bigint;not null;index" json:"main_task_id"`                             // 主任务ID
	ItemIdx                    int32                           `gorm:"column:item_idx;type:int;not null" json:"item_idx"`                                              // 项目索引
	OriginSubtitle             string                          `gorm:"column:origin_subtitle;type:varchar(1000)" json:"origin_subtitle"`                               // 原始字幕
	TargetSubtitle             string                          `gorm:"column:target_subtitle;type:varchar(1000)" json:"target_subtitle"`                               // 目标字幕
	SourceLangId               string                          `gorm:"column:source_lang_id;type:varchar(50)" json:"source_lang_id"`                                   // 源语言
	TargetLangId               string                          `gorm:"column:target_lang_id;type:varchar(50)" json:"target_lang_id"`                                   // 目标语言
	SubtitleStartStr           string                          `gorm:"column:subtitle_start_str;type:varchar(50)" json:"subtitle_start_str"`                           // 字幕开始时间字符串
	SubtitleEndStr             string                          `gorm:"column:subtitle_end_str;type:varchar(50)" json:"subtitle_end_str"`                               // 字幕结束时间字符串
	OriginSubtitleStartStr     string                          `gorm:"column:origin_subtitle_start_str;type:varchar(50)" json:"origin_subtitle_start_str"`             // 原始字幕开始时间字符串
	OriginSubtitleEndStr       string                          `gorm:"column:origin_subtitle_end_str;type:varchar(50)" json:"origin_subtitle_end_str"`                 // 原始字幕结束时间字符串
	ClipSubtitleStartStr       string                          `gorm:"column:clip_subtitle_start_str;type:varchar(50)" json:"clip_subtitle_start_str"`                 // 裁剪视频 字幕开始时间字符串
	ClipSubtitleEndStr         string                          `gorm:"column:clip_subtitle_end_str;type:varchar(50)" json:"clip_subtitle_end_str"`                     // 裁剪视频 字幕结束时间字符串
	ClipOriginSubtitleStartStr string                          `gorm:"column:clip_origin_subtitle_start_str;type:varchar(50)" json:"clip_origin_subtitle_start_str"`   // 裁剪视频 原始字幕开始时间字符串
	ClipOriginSubtitleEndStr   string                          `gorm:"column:clip_origin_subtitle_end_str;type:varchar(50)" json:"clip_origin_subtitle_end_str"`       // 裁剪视频 原始字幕结束时间字符串
	OriginAudio                string                          `gorm:"column:origin_audio;type:varchar(500)" json:"origin_audio"`                                      // 原始音频地址
	TTSUrl                     string                          `gorm:"column:tts_url;type:varchar(500)" json:"tts_url"`                                                // TTS音频地址
	GenerateVoiceStatus        int                             `gorm:"column:generate_voice_status;type:int;not null;default:0" json:"generate_voice_status"`          // 生成语音状态
	TextTranslateStatus        int                             `gorm:"column:text_translate_status;type:int;not null;default:0" json:"text_translate_status"`          // 文本翻译状态
	BackTranslateText          string                          `gorm:"column:back_translate_text;type:varchar(1000)" json:"back_translate_text"`                       // 回译文本
	LatestTTSInfo              *common.LatestTTSInfo           `gorm:"column:latest_tts_info;type:json;serializer:json;" json:"latest_tts_info"`                       // 最新TTS信息
	LatestTextTranslateInfo    *common.LatestTextTranslateInfo `gorm:"column:latest_text_translate_info;type:json;serializer:json;" json:"latest_text_translate_info"` // 最新文本翻译信息
	SpeakerId                  int64                           `gorm:"column:speaker_id;type:bigint;not null" json:"speaker_id"`                                       // 说话人ID
	SpeakerName                string                          `gorm:"column:speaker_name;type:varchar(100)" json:"speaker_name"`                                      // 说话人名称
	CustomPrompt               string                          `gorm:"column:custom_prompt;type:varchar(1000)" json:"custom_prompt"`                                   // 自定义提示
	AudioConfig                *common.AudioConfig             `gorm:"column:audio_config;type:json;serializer:json;" json:"audio_config"`                             // 音频配置
	VoiceInfo                  *common.VoiceInfo               `gorm:"column:voice_info;type:json;serializer:json;" json:"voice_info"`                                 // 音色信息
	TTSWords                   []*common.WordInfo              `gorm:"column:tts_words;type:json;serializer:json;" json:"tts_words"`                                   // TTS词汇
	SubItemList                []*common.SubItem               `gorm:"column:sub_item_list;type:json;serializer:json;" json:"sub_item_list"`                           // 子项列表
	CreatedAt                  time.Time                       `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	UpdatedAt                  time.Time                       `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt                  gorm.DeletedAt                  `gorm:"column:deleted_at;index;comment:'删除时间'"`
}

func (CommentarySubtitleSnapshotItem) TableName() string {
	return "commentary_subtitle_snapshot_item"
}
