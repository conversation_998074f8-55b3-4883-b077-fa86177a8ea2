package do

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"

	"gorm.io/gorm"
)

// 解说子任务表
type CommentarySubTask struct {
	Id                             int64                                `gorm:"column:id;primary_key" json:"id"`
	MainTaskId                     int64                                `gorm:"column:main_task_id;type:bigint;not null;index" json:"main_task_id"`          // 主任务ID
	Name                           string                               `gorm:"column:name;type:varchar(255);not null" json:"name"`                          // 名称
	PayOrderStatus                 consts.PayOrderStatus                `gorm:"column:pay_order_status;type:int;not null;default:0" json:"pay_order_status"` // 支付订单状态 0-未支付 1-已支付
	Status                         consts.CommentarySubTaskStatus       `gorm:"column:status;type:int;not null;default:0;index" json:"status"`               // 状态：0-待执行，1-执行中，2-成功，3-失败
	AuditStatus                    consts.AuditStatus                   `gorm:"column:audit_status;type:int;not null;default:0;index" json:"audit_status"`   // 审核状态：0-待审核，1-审核中，2-成功，3-不通过，4-异常
	AuditReason                    string                               `gorm:"column:audit_reason;type:varchar(255)" json:"audit_reason"`                   // 审核不通过原因
	MergeStatus                    consts.CommentaryMergeStatus         `gorm:"column:merge_status;type:int;not null;default:0;index" json:"merge_status"`   // 合成状态  0:待合成 1: 合成中 2: 已合成 3: 合成失败
	SpeakerId                      int64                                `gorm:"column:speaker_id;type:bigint;not null" json:"speaker_id"`
	VoiceId                        int64                                `gorm:"column:voice_id;type:bigint;not null" json:"voice_id"`                                                  //音色id                                // 说话人ID
	TenantId                       int64                                `gorm:"column:tenant_id;type:bigint;not null;index" json:"tenant_id"`                                          // 租户ID
	VideoDuration                  float64                              `gorm:"column:video_duration;type:double" json:"video_duration"`                                               // 视频时长
	MaterialHighlightDuration      float64                              `gorm:"column:material_highlight_duration;type:double" json:"material_highlight_duration"`                     // 素材高光时长
	ErrCode                        int                                  `gorm:"column:err_code;type:varchar(100)" json:"err_code"`                                                     // 异常码 1010: 素材审核不通过	,1020: 转码失败, 1030: 解说生成失败, 1040: 字幕审核异常, 1050: 配音失败,1060: OCR失败, 1061:擦除预处理失败, 1062:擦除失败, 1063:擦除后处理失败, 1100: 合成失败,
	ErrMsg                         string                               `gorm:"column:err_msg;type:varchar(255)" json:"err_msg"`                                                       // 异常信息
	EraseMode                      consts.CommentaryEraseMode           `gorm:"column:erase_mode;type:int;not null;default:0" json:"erase_mode"`                                       // 擦除模式：0-关，1-开
	EraseEdition                   consts.CommentaryEraseEdition        `gorm:"column:erase_edition;type:int;not null;default:0" json:"erase_edition"`                                 // 擦除版本：0-普通，1-专业
	OcrRectInfo                    *common.OcrRectInfo                  `gorm:"column:ocr_rect_info;type:json;serializer:json" json:"ocr_rect_info"`                                   // OCR区域信息
	BgmMode                        consts.CommentaryBgmMode             `gorm:"column:bgm_mode;type:int;not null;default:0" json:"bgm_mode"`                                           // BGM模式：0-关，1-开
	SubtitleMode                   consts.CommentarySubtitleMode        `gorm:"column:subtitle_mode;type:int;not null;default:0" json:"subtitle_mode"`                                 // 字幕模式：0-关，1-开
	NoSubtitleVideoUrl             string                               `gorm:"column:no_subtitle_video_url;type:varchar(500)" json:"no_subtitle_video_url"`                           // 无字幕视频CDN地址 完整视频
	NoSubtitleVideoHlsUrl          string                               `gorm:"column:no_subtitle_video_hls_url;type:varchar(500)" json:"no_subtitle_video_hls_url"`                   // 无字幕视频CDN流地址 完整视频
	MergedVideoUrl                 string                               `gorm:"column:merged_video_url;type:varchar(500)" json:"merged_video_url"`                                     // 合成后视频 完整视频
	MergedVideoHlsUrl              string                               `gorm:"column:merged_video_hls_url;type:varchar(500)" json:"merged_video_hls_url"`                             // 合成后视频流地址 完整视频
	Size                           float64                              `gorm:"column:size;type:double" json:"size"`                                                                   // 大小
	Resolution                     string                               `gorm:"column:resolution;type:varchar(20)" json:"resolution"`                                                  // 分辨率
	CoverUrl                       string                               `gorm:"column:cover_url;type:varchar(500)" json:"cover_url"`                                                   // 封面
	CustomSubtitleStyle            *common.CustomSubtitleStyle          `gorm:"column:custom_subtitle_style;type:json;serializer:json" json:"custom_subtitle_style"`                   // 字幕样式
	BgmUrl                         string                               `gorm:"column:bgm_url;type:varchar(500)" json:"bgm_url"`                                                       // 背景音
	CommentaryAgentRes             *common.QueryCommentaryQiFeiRespData `gorm:"column:commentary_agent_res;type:json;serializer:json" json:"commentary_agent_res"`                     // 解说模型返回结果
	MaterialHighlightUrl           string                               `gorm:"column:material_highlight_url;type:varchar(500)" json:"material_highlight_url"`                         // 素材高光 裁剪合并后的
	NoSubtitleMaterialHighlightUrl string                               `gorm:"column:no_subtitle_material_highlight_url;type:varchar(500)" json:"no_subtitle_material_highlight_url"` // 素材高光无字幕, 无擦除模式就是空
	MergedMaterialHighlightUrl     string                               `gorm:"column:merged_material_highlight_url;type:varchar(500)" json:"merged_material_highlight_url"`           // 合并后的素材地址
	HighlightEpisodesUrls          []string                             `gorm:"column:highlight_episodes_urls;type:json;serializer:json" json:"highlight_episodes_urls"`               // 高光剧集(一组)
	EndTagUrl                      string                               `gorm:"column:end_tag_url;type:varchar(500)" json:"end_tag_url"`                                               // 落版视频
	SubtitleFileUrl                string                               `gorm:"column:subtitle_file_url;type:varchar(500)" json:"subtitle_file_url"`                                   // 字幕文件地址
	NeedMerge                      int8                                 `gorm:"column:need_merge;type:tinyint;not null;default:0" json:"need_merge"`                                   // 是否需要重新合成
	SourceLangId                   string                               `gorm:"column:source_lang_id;type:varchar(50)" json:"source_lang_id"`                                          // 源语言
	TargetLangId                   string                               `gorm:"column:target_lang_id;type:varchar(50)" json:"target_lang_id"`                                          // 目标语言
	CreatedAt                      time.Time                            `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	UpdatedAt                      time.Time                            `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt                      gorm.DeletedAt                       `gorm:"column:deleted_at;index;comment:'删除时间'"`
}

func (CommentarySubTask) TableName() string {
	return "commentary_sub_task"
}
