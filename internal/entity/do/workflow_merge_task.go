package do

import (
	"time"
)

type WorkflowMergeTask struct {
	Id                  int64     `gorm:"column:id;primary_key;comment:'唯一ID'" json:"id"`                           // 唯一ID
	AppId               int64     `gorm:"column:app_id;type:bigint;comment:'应用ID'" json:"app_id"`                   // 应用ID
	MainTaskId          int64     `gorm:"column:main_task_id;type:bigint;comment:'业务任务ID'" json:"main_task_id"`   // 业务任务ID
	SubTaskId           int64     `gorm:"column:sub_task_id;type:bigint;index;comment:'子任务ID'" json:"sub_task_id"` // 子任务ID
	TenantID            int64     `gorm:"column:tenant_id;type:bigint;comment:'用户ID'" json:"tenant_id"`             // 用户ID
	VideoUrl            string    `gorm:"column:video_url;type:varchar(512);comment:'视频URL'" json:"video_url"`      // 视频URL
	AudioUrl            string    `gorm:"type:varchar(512);column:audio_url;comment:'音频文件'" json:"audio_url"`
	AssUrl              string    `gorm:"type:varchar(512);column:ass_url;comment:'字幕文件'" json:"ass_url"`
	PostUrl             string    `gorm:"type:varchar(512);column:post_url;comment:'生成后文件'" json:"post_url"`
	PostVideoObjectName string    `gorm:"type:varchar(512);column:post_video_object_name;comment:'生成后文件对象名'" json:"post_video_object_name"`
	Status              int       `gorm:"column:status;type:int;index:idx_status;comment:'状态'" json:"status"`
	DisableBgm          int       `gorm:"column:disable_bgm;type:int;comment:'是否关闭BGM'" json:"disable_bgm"`
	Vocal               int       `gorm:"column:vocal;type:int;comment:'是否配音'" json:"vocal"`
	Subtitle            int       `gorm:"column:subtitle;type:int;comment:'是否合成字幕'" json:"subtitle"`
	MergeType           int       `gorm:"column:merge_type;type:int;comment:'字幕合成类型'" json:"merge_type"`
	VideoDuration       float32   `gorm:"column:video_duration;type:float;comment:'视频时长'" json:"video_duration"` //视频时长
	ProcessSubmitAt     time.Time `gorm:"column:process_submit_at;not null;default:CURRENT_TIMESTAMP(3);comment:'提交到engine处理任务时间'"`
	ProcessStartAt      time.Time `gorm:"column:process_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'engine任务开始时间'"`
	ProcessEndAt        time.Time `gorm:"column:process_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'engine任务结束时间'"`
	MergeStartAt        time.Time `gorm:"column:merge_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'合成开始时间'"`
	MergeEndAt          time.Time `gorm:"column:merge_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'合成结束时间'"`
	UploadStartAt       time.Time `gorm:"column:upload_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'上传开始时间'"`
	UploadEndAt         time.Time `gorm:"column:upload_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'上传结束时间'"`
	CreatedAt           time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`                                // 创建时间
	UpdatedAt           time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"` // 更新时间
	DeletedAt           time.Time `gorm:"column:deleted_at;default:null;comment:'删除时间'"`                                                // 删除时间
}

func (WorkflowMergeTask) TableName() string {
	return "workflow_merge_task"
}
