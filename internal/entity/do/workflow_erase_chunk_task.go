package do

import (
	"time"
)

type WorkflowEraseChunkTask struct {
	Id            int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	OcrId         int64     `gorm:"column:ocr_id;type:bigint;not null;comment:'OCR任务对应ID'" json:"ocr_id"`
	SubTaskId     int64     `gorm:"column:sub_task_id;type:bigint;index;comment:'子任务ID'" json:"sub_task_id"` // 子任务ID
	Status        int       `gorm:"column:status;type:int;index:idx_status;comment:'状态'" json:"status"`
	ChunkId       int32     `gorm:"column:chunk_id" json:"chunk_id"`
	Chunk         string    `gorm:"column:chunk" json:"chunk"`
	RetryCount    int32     `gorm:"column:retry_count" json:"retry_count"`
	ChunkSize     int32     `gorm:"column:chunk_size" json:"chunk_size"`
	EraseSubmitAt time.Time `gorm:"column:erase_submit_at;not null;default:CURRENT_TIMESTAMP(3);comment:'提交到擦除任务时间'"`
	EraseStartAt  time.Time `gorm:"column:erase_start_at;not null;default:CURRENT_TIMESTAMP(3);comment:'开始擦除任务时间'"`
	EraseEndAt    time.Time `gorm:"column:erase_end_at;not null;default:CURRENT_TIMESTAMP(3);comment:'结束擦除任务时间'"`
	HandleTimeout time.Time `gorm:"column:handle_timeout" json:"handle_timeout"`
	CreatedAt     time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`                                // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"` // 更新时间
	DeletedAt     time.Time `gorm:"column:deleted_at;default:null;comment:'删除时间'" json:"deleted_at"`
}

func (WorkflowEraseChunkTask) TableName() string {
	return "workflow_erase_chunk_task"
}
