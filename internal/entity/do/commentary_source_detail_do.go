package do

import (
	"business-workflow/internal/consts"
	"gorm.io/gorm"
	"time"
)

// 解说任务资源详情表
type CommentarySourceDetail struct {
	Id                  int64                 `gorm:"column:id;primary_key" json:"id"`
	MainTaskId          int64                 `gorm:"column:main_task_id;type:bigint;not null;index" json:"main_task_id"`          // 主任务ID
	Name                string                `gorm:"column:name;type:varchar(255);not null" json:"name"`                          // 资源名称
	TenantId            int64                 `gorm:"column:tenant_id;type:bigint;not null;index" json:"tenant_id"`                // 租户ID
	BizType             consts.SourceBizType  `gorm:"column:biz_type;type:int;not null;index" json:"biz_type"`                     // 业务类型：解说,高光,落版
	SourceFileType      consts.SourceFileType `gorm:"column:source_file_type;type:int;not null;index" json:"source_file_type"`     // 资源类型：图片,视频,音频
	GroupId             int64                 `gorm:"column:group_id;type:bigint;index" json:"group_id"`                           // 分组ID
	Width               int                   `gorm:"column:width;type:int" json:"width"`                                          // 宽度
	Height              int                   `gorm:"column:height;type:int" json:"height"`                                        // 高度
	SourceUrl           string                `gorm:"column:source_url;type:varchar(500);not null" json:"source_url"`              // 资源CDN地址
	ObjectName          string                `gorm:"column:object_name;type:varchar(250)" json:"object_name"`                     // OSS对象名称
	CoverUrl            string                `gorm:"column:cover_url;type:varchar(255)" json:"cover_url"`                         // 封面地址
	Duration            float64               `gorm:"column:duration;type:double" json:"duration"`                                 // 时长
	Resolution          string                `gorm:"column:resolution;type:varchar(20)" json:"resolution"`                        // 原始分辨率
	Size                float64               `gorm:"column:size;type:double" json:"size"`                                         // 大小
	AuditStatus         consts.AuditStatus    `gorm:"column:audit_status;type:int;not null;default:0;index" json:"audit_status"`   // 审核状态：0-待审核，1-审核中，2-成功，3-不通过，4-异常
	ErrCode             int                   `gorm:"column:err_code;type:int" json:"err_code"`                                    // 异常码
	ErrMsg              string                `gorm:"column:err_msg;type:varchar(255)" json:"err_msg"`                             // 异常信息
	LowResolutionUrl    string                `gorm:"column:low_resolution_url;type:varchar(500)" json:"low_resolution_url"`       //480P低分辨率url 用来解说用
	LowResolutionKey    string                `gorm:"column:low_resolution_key;type:varchar(255)" json:"low_resolution_key"`       //480P低分辨率oss object key
	LowResolution       string                `gorm:"column:low_resolution;type:varchar(20)" json:"low_resolution"`                //480P 分辨率值 854x480 480x854
	TargetResolutionUrl string                `gorm:"column:target_resolution_url;type:varchar(255)" json:"target_resolution_url"` //目标分辨率url
	TargetResolutionKey string                `gorm:"column:target_resolution_key;type:varchar(255)" json:"target_resolution_key"` //目标分辨率oss object key
	CreatedAt           time.Time             `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	UpdatedAt           time.Time             `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt           gorm.DeletedAt        `gorm:"column:deleted_at;index;comment:'删除时间'"`
}

func (CommentarySourceDetail) TableName() string {
	return "commentary_source_detail"
}
