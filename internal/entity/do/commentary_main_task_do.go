package do

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"

	"gorm.io/gorm"
)

// 解说主任务表
type CommentaryMainTask struct {
	Id                      int64                           `gorm:"column:id;primary_key" json:"id"`
	Name                    string                          `gorm:"column:name;type:varchar(255);not null" json:"name"` // 名称
	AppId                   int64                           `gorm:"column:appid" json:"appid"`
	ApiKey                  string                          `gorm:"column:api_key;type:varchar(100);not null" json:"api_key"`                            // API Key
	TenantId                int64                           `gorm:"column:tenant_id;type:bigint;not null;index" json:"tenant_id"`                        // 租户ID
	PayOrderStatus          consts.PayOrderStatus           `gorm:"column:pay_order_status;type:int;not null;default:0" json:"pay_order_status"`         // 支付订单状态 0-未支付 1-已支付
	TaskType                consts.CommentaryTaskType       `gorm:"column:task_type;type:int;not null;index" json:"task_type"`                           // 任务类型：解说/高光剪辑
	BizMode                 consts.CommentaryBizMode        `gorm:"column:biz_mode;type:int;not null;index" json:"biz_mode"`                             // 业务类型：组合
	BgmMode                 consts.CommentaryBgmMode        `gorm:"column:bgm_mode;type:int;not null;default:0" json:"bgm_mode"`                         // BGM模式：0-关，1-开
	SubtitleMode            consts.CommentarySubtitleMode   `gorm:"column:subtitle_mode;type:int;not null;default:0" json:"subtitle_mode"`               // 字幕模式：0-关，1-开
	EraseMode               consts.CommentaryEraseMode      `gorm:"column:erase_mode;type:int;not null;default:0" json:"erase_mode"`                     // 擦除模式：0-关，1-开
	EraseEdition            consts.CommentaryEraseEdition   `gorm:"column:erase_edition;type:int;not null;default:0" json:"erase_edition"`               // 擦除版本：0-普通，1-专业
	OcrRectInfo             *common.OcrRectInfo             `gorm:"column:ocr_rect_info;type:json;serializer:json" json:"ocr_rect_info"`                 // OCR区域信息
	TargetLangId            string                          `gorm:"column:target_lang_id;type:varchar(50)" json:"target_lang_id"`                        // 目标语言
	TargetResolution        string                          `gorm:"column:target_resolution;type:varchar(20)" json:"target_resolution"`                  // 分辨率
	AspectRatio             string                          `gorm:"column:aspect_ratio;type:varchar(20)" json:"aspect_ratio"`                            // 高宽比：16:9，9:16，4:3等
	TargetDurationRangeType consts.TargetDurationRangeType  `gorm:"column:target_duration_range_type;type:int" json:"target_duration_range_type"`        // 预估时长区间类型
	TargetDurationRangeStr  string                          `gorm:"column:target_duration_range_str;type:varchar(100)" json:"target_duration_range_str"` // 预估时长字符串
	Status                  consts.CommentaryMainTaskStatus `gorm:"column:status;type:int;not null;default:0;index" json:"status"`                       // 状态：0-待执行，1-执行中，2-完成
	ErrMsg                  string                          `gorm:"column:err_msg;type:varchar(500)" json:"err_msg"`                                     // 异常信息
	PayOrderId              string                          `gorm:"column:pay_order_id;type:varchar(100)" json:"pay_order_id"`                           // 支付订单ID
	TargetNumber            int                             `gorm:"column:target_number;type:int;not null;default:1" json:"target_number"`               // 生成视频数量
	CompressVideoUrl        string                          `gorm:"column:compress_video_url;type:varchar(500)" json:"compress_video_url"`               // 提供起飞解说 用压缩后的视频
	ClipMergeVideoUrl       string                          `gorm:"column:clip_merge_video_url;type:varchar(500)" json:"clip_merge_video_url"`           // 用来裁剪的视频(素材合并)
	VoiceIDList             []int64                         `gorm:"column:voice_id_list;type:json;serializer:json" json:"voice_id_list"`                 // 音色ID列表
	DubbingType             int                             `gorm:"column:dubbing_type;type:int;not null;default:0" json:"dubbing_type"`                 // 配音类型 0-标准配音 2-11labs配音
	RequestID               string                          `gorm:"column:request_id;type:varchar(100)" json:"request_id"`
	CreatedAt               time.Time                       `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP(3);comment:'创建时间'"`
	UpdatedAt               time.Time                       `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3);comment:'更新时间'"`
	DeletedAt               gorm.DeletedAt                  `gorm:"column:deleted_at;index;comment:'删除时间'"`
}

func (CommentaryMainTask) TableName() string {
	return "commentary_main_task"
}
