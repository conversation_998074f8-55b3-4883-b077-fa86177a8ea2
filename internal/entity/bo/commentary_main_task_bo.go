package bo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"
)

// CommentaryMainTaskBO 解说主任务业务对象
type CommentaryMainTaskBO struct {
	Id                      int64                           `json:"id,omitempty"`
	Name                    string                          `json:"name,omitempty"`
	AppId                   int64                           `json:"appid,omitempty"`
	TenantId                int64                           `json:"tenant_id,omitempty"`
	ApiKey                  string                          `json:"api_key,omitempty"` // API Key
	TaskType                consts.CommentaryTaskType       `json:"task_type,omitempty"`
	BizMode                 consts.CommentaryBizMode        `json:"biz_mode,omitempty"`
	BgmMode                 consts.CommentaryBgmMode        `json:"bgm_mode,omitempty"`
	SubtitleMode            consts.CommentarySubtitleMode   `json:"subtitle_mode,omitempty"`
	EraseMode               consts.CommentaryEraseMode      `json:"erase_mode,omitempty"`
	EraseEdition            consts.CommentaryEraseEdition   `json:"erase_edition,omitempty"`
	OcrRectInfo             *common.OcrRectInfo             `json:"ocr_rect_info,omitempty"`
	TargetLangId            string                          `json:"target_lang_id,omitempty"`
	TargetResolution        string                          `json:"target_resolution,omitempty"`
	AspectRatio             string                          `json:"aspect_ratio,omitempty"`
	TargetDurationRangeType consts.TargetDurationRangeType  `json:"target_duration_range_type,omitempty"`
	TargetDurationRangeStr  string                          `json:"target_duration_range_str,omitempty"`
	Status                  consts.CommentaryMainTaskStatus `json:"status,omitempty"`
	ErrMsg                  string                          `json:"err_msg,omitempty"`
	PayOrderId              string                          `json:"pay_order_id,omitempty"`
	PayOrderStatus          consts.PayOrderStatus           `json:"pay_order_status,omitempty"`
	TargetNumber            int                             `json:"target_number,omitempty"`
	ClipMergeVideoUrl       string                          `json:"clip_merge_video_url"` // 用来裁剪的视频(素材合并)
	VoiceIDList             []int64                         `json:"voice_id_list,omitempty"`
	DubbingType             int                             `json:"dubbing_type,omitempty"`
	RequestID               string                          `json:"request_id,omitempty"`
	CreatedAt               time.Time                       `json:"created_at,omitempty"`
	UpdatedAt               time.Time                       `json:"updated_at,omitempty"`
}

// OcrRectInfoBO OCR区域信息业务对象
type OcrRectInfoBO struct {
	TopLeft     []float64 `json:"topLeft,omitempty"`
	TopRight    []float64 `json:"topRight,omitempty"`
	BottomLeft  []float64 `json:"bottomLeft,omitempty"`
	BottomRight []float64 `json:"bottomRight,omitempty"`
}

// CommentaryMainTaskCreateReqBO 创建解说主任务请求 BO
type CommentaryMainTaskCreateReqBO struct {
	AppId                       int64                          `json:"appid"`
	TenantId                    int64                          `json:"tenant_id"`
	ApiKey                      string                         `json:"api_key"`
	TaskType                    consts.CommentaryTaskType      `json:"task_type"`
	BizMode                     consts.CommentaryBizMode       `json:"biz_mode"`
	BgmMode                     consts.CommentaryBgmMode       `json:"bgm_mode"`
	SubtitleMode                consts.CommentarySubtitleMode  `json:"subtitle_mode"`
	EraseMode                   consts.CommentaryEraseMode     `json:"erase_mode"`
	EraseEdition                consts.CommentaryEraseEdition  `json:"erase_edition"`
	TextRect                    []int32                        `json:"text_rect"`
	TargetLangId                string                         `json:"target_lang_id"`
	AspectRatio                 string                         `json:"aspect_ratio"`
	TargetDurationRangeType     consts.TargetDurationRangeType `json:"target_duration_range_type"`
	TargetDurationRangeStr      string                         `json:"target_duration_range_str"`
	TargetNumber                int                            `json:"target_number"`
	VoiceIDList                 []int64                        `json:"voice_id_list"`
	MaterialSourceList          []*MaterialSourceBO            `json:"material_highlight_source_list"`
	HighlightEpisodesSourceList []*HighlightEpisodesSourceBO   `json:"highlight_episodes_source_list"`
	EndTagSourceList            []*MainTaskCommonSourceBO      `json:"end_tag_source_list"`
	BgmSourceList               []*MainTaskCommonSourceBO      `json:"bgm_source_list"`
	DubbingType                 int                            `json:"dubbing_type"`
}

// HighlightEpisodesSourceBO 高光剧集源 BO
type HighlightEpisodesSourceBO struct {
	SourceList []*MainTaskCommonSourceBO `json:"source_list"`
}

// MaterialSourceBO 素材源 BO
type MaterialSourceBO struct {
	MainTaskCommonSourceBO
	NoSubtitlePreference int `json:"no_subtitle_preference"`
}

// MainTaskCommonSourceBO 主任务通用源 BO
type MainTaskCommonSourceBO struct {
	Name           string                `json:"name"`
	SourceUrl      string                `json:"source_url"`
	SourceFileType consts.SourceFileType `json:"source_file_type"`
	FromType       int                   `json:"from_type"`
}

// CommentaryMainTaskCreateResBO 创建解说主任务响应 BO
type CommentaryMainTaskCreateResBO struct {
	Id int64 `json:"id"`
}

// GetCommentaryTaskHistoryReqBO 获取解说任务信息请求 BO
type GetCommentaryTaskHistoryReqBO struct {
	TenantId                int64 `json:"tenant_id"`
	*CommentaryTaskFilterBO `json:"filter"`
	Page                    int `json:"page"`
	PageSize                int `json:"page_size"`
}

// 筛选条件
type CommentaryTaskFilterBO struct {
	TargetLangIdList []string                        `json:"target_lang_id_list"`
	BizModeList      []consts.CommentaryBizMode      `json:"biz_mode_list"`
	EraseEditionList []consts.CommentaryEraseEdition `json:"erase_edition_list"`
	AspectRatioList  []string                        `json:"aspect_ratio_list"`
}

// GetCommentaryTaskHistoryResBO 获取解说任务信息响应 BO
type CommentaryTaskHistoryBO struct {
	MainTask *CommentaryMainTaskBO  `json:"main_task"`
	SubTasks []*CommentarySubTaskBO `json:"sub_tasks"`
}

// CommentaryMainTaskDetailBO 解说主任务详情 BO
type CommentaryMainTaskDetailBO struct {
	Id                      int64                           `json:"id"`
	AppId                   int64                           `json:"appid"`
	ApiKey                  string                          `json:"api_key"`
	Name                    string                          `json:"name"`
	TenantId                int64                           `json:"tenant_id"`
	TaskType                consts.CommentaryTaskType       `json:"task_type"`
	BizMode                 consts.CommentaryBizMode        `json:"biz_mode"`
	BgmMode                 int                             `json:"bgm_mode"`
	SubtitleMode            consts.CommentarySubtitleMode   `json:"subtitle_mode"`
	EraseMode               consts.CommentaryEraseMode      `json:"erase_mode"`
	EraseEdition            consts.CommentaryEraseEdition   `json:"erase_edition"`
	OcrRectInfo             *common.OcrRectInfo             `json:"ocr_rect_info"`
	TargetLangId            string                          `json:"target_lang_id"`
	TargetResolution        string                          `json:"target_resolution"`
	AspectRatio             string                          `json:"aspect_ratio"`
	TargetDurationRangeType consts.TargetDurationRangeType  `json:"target_duration_range_type"`
	TargetDurationRangeStr  string                          `json:"target_duration_range_str"`
	Status                  consts.CommentaryMainTaskStatus `json:"status"`
	ErrMsg                  string                          `json:"err_msg"`
	PayOrderId              string                          `json:"pay_order_id"`
	TargetNumber            int                             `json:"target_number"`
	ClipMergeVideoUrl       string                          `json:"clip_merge_video_url"`
	VoiceIDList             []int64                         `json:"voice_id_list"`
	DubbingType             int                             `json:"dubbing_type"`
	RequestID               string                          `json:"request_id"`
	CreatedAt               *time.Time                      `json:"created_at"`
	UpdatedAt               *time.Time                      `json:"updated_at"`
	SubTasks                []*CommentarySubTaskBO          `json:"sub_tasks"`
}

// UpdateTaskStatusReqBO 更新任务状态请求 BO
type UpdateTaskStatusReqBO struct {
	Id     int64  `json:"id"`
	Status int    `json:"status"`
	ErrMsg string `json:"err_msg"`
}

// UpdateTaskStatusResBO 更新任务状态响应 BO
type UpdateTaskStatusResBO struct {
	Success bool `json:"success"`
}

// DeleteTaskReqBO 删除任务请求 BO
type DeleteTaskReqBO struct {
	Id int64 `json:"id"`
}

// DeleteTaskResBO 删除任务响应 BO
type DeleteTaskResBO struct {
	Success bool `json:"success"`
}

// BatchGetCommentaryTasksByOrderIdsReqBO 根据订单ID批量获取解说任务请求 BO
type BatchGetCommentaryTasksByOrderIdsReqBO struct {
	OrderIds []string `json:"order_ids"` // 订单ID列表
}

// BatchGetCommentaryTasksByOrderIdsResBO 根据订单ID批量获取解说任务响应 BO
type BatchGetCommentaryTasksByOrderIdsResBO struct {
	Tasks []*CommentaryTaskDetailsBO `json:"tasks"` // 任务详情列表
}

// CommentaryTaskDetailsBO 解说任务详情 BO
type CommentaryTaskDetailsBO struct {
	OrderId    string                 `json:"order_id"`    // 订单ID
	MainTask   *CommentaryMainTaskBO  `json:"main_task"`   // 主任务信息
	SubTasks   []*CommentarySubTaskBO `json:"sub_tasks"`   // 子任务列表
	TaskStatus string                 `json:"task_status"` // 任务状态
}
