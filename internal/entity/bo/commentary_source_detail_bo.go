package bo

import (
	"business-workflow/internal/consts"
	"time"
)

// CommentarySourceDetailBO 解说任务资源详情业务对象
type CommentarySourceDetailBO struct {
	Id                  int64                 `json:"id,omitempty"`
	SourceDetailId      int64                 `json:"source_detail_id,omitempty"`
	MainTaskId          int64                 `json:"main_task_id,omitempty"`
	Name                string                `json:"name,omitempty"`
	TenantId            int64                 `json:"tenant_id,omitempty"`
	BizType             consts.SourceBizType  `json:"biz_type,omitempty"`
	SourceFileType      consts.SourceFileType `json:"source_file_type,omitempty"`
	GroupId             int64                 `json:"group_id,omitempty"`
	Width               int                   `json:"width,omitempty"`
	Height              int                   `json:"height,omitempty"`
	SourceUrl           string                `json:"source_url,omitempty"`
	ObjectName          string                `json:"object_name,omitempty"`
	CoverUrl            string                `json:"cover_url,omitempty"`
	Duration            float64               `json:"duration,omitempty"`
	Resolution          string                `json:"resolution,omitempty"`
	Size                float64               `json:"size,omitempty"`
	AuditStatus         consts.AuditStatus    `json:"audit_status"`          // 审核状态：0-待审核，1-审核中，2-成功，3-不通过，4-异常
	ErrCode             int                   `json:"err_code"`              // 异常码
	ErrMsg              string                `json:"err_msg"`               // 异常信息
	LowResolutionUrl    string                `json:"low_resolution_url"`    //480P低分辨率url 用来解说用
	LowResolution       string                `json:"low_resolution"`        //480P 分辨率值 854x480 480x854
	TargetResolutionUrl string                `json:"target_resolution_url"` //目标分辨率url
	CreatedAt           *time.Time            `json:"created_at,omitempty"`
	UpdatedAt           *time.Time            `json:"updated_at,omitempty"`
}
