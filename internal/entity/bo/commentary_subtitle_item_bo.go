package bo

import (
	"business-workflow/internal/entity/common"
	"time"
)

// CommentarySubtitleItemBO 解说字幕项业务对象
type CommentarySubtitleItemBO struct {
	Id                      int64                           `json:"id,omitempty"`
	SubTaskId               int64                           `json:"sub_task_id,omitempty"`
	MainTaskId              int64                           `json:"main_task_id,omitempty"`
	ItemIdx                 int32                           `json:"item_idx,omitempty"`
	OriginSubtitle          string                          `json:"origin_subtitle,omitempty"`
	TargetSubtitle          string                          `json:"target_subtitle,omitempty"`
	SourceLangId            string                          `json:"source_lang_id,omitempty"`
	TargetLangId            string                          `json:"target_lang_id,omitempty"`
	SubtitleStartStr        string                          `json:"subtitle_start_str,omitempty"`
	SubtitleEndStr          string                          `json:"subtitle_end_str,omitempty"`
	OriginSubtitleStartStr  string                          `json:"origin_subtitle_start_str,omitempty"`
	OriginSubtitleEndStr    string                          `json:"origin_subtitle_end_str,omitempty"`
	OriginAudio             string                          `json:"origin_audio"`
	GenerateVoiceStatus     int                             `json:"generate_voice_status,omitempty"`
	TextTranslateStatus     int                             `json:"text_translate_status,omitempty"`
	LatestTTSInfo           *common.LatestTTSInfo           `json:"latest_tts_info,omitempty"`
	LatestTextTranslateInfo *common.LatestTextTranslateInfo `json:"latest_text_translate_info,omitempty"`
	BackTranslateText       string                          `json:"back_translate_text,omitempty"`
	TTSUrl                  string                          `json:"tts_url,omitempty"`
	SpeakerId               int64                           `json:"speaker_id,omitempty"`
	SpeakerName             string                          `json:"speaker_name,omitempty"`
	CustomPrompt            string                          `json:"custom_prompt,omitempty"`
	AudioConfig             *common.AudioConfig             `json:"audio_config,omitempty"`
	VoiceInfo               *common.VoiceInfo               `json:"voice_info,omitempty"`
	TTSWords                []*common.WordInfo              `json:"tts_words,omitempty"`
	SubItemList             []*common.SubItem               `json:"sub_item_list,omitempty"`
	LastModify              time.Time                       `json:"last_modify,omitempty"`
	CreatedAt               time.Time                       `json:"created_at,omitempty"`
	UpdatedAt               time.Time                       `json:"updated_at,omitempty"`
}

// MergeSubtitleReqBO 合并字幕片段请求 BO
type MergeSubtitleReqBO struct {
	AppId        int64  `json:"appid"`
	TenantId     int64  `json:"tenant_id"`
	ApiKey       string `json:"api_key"`
	SubTaskId    int64  `json:"sub_task_id"`
	ItemIdxList  []int  `json:"item_idx_list"`
	MergeText    string `json:"merge_text"`
	CustomPrompt string `json:"custom_prompt"`
}

// MergeSubtitleResBO 合并字幕片段响应 BO
type MergeSubtitleResBO struct {
	SubtitleItem *CommentarySubtitleItemBO `json:"subtitle_item"`
}

// SplitSubtitleReqBO 拆分字幕片段请求 BO
type SplitSubtitleReqBO struct {
	AppId          int64  `json:"appid"`
	TenantId       int64  `json:"tenant_id"`
	ApiKey         string `json:"api_key"`
	SubTaskId      int64  `json:"sub_task_id"`
	SubtitleItemId int64  `json:"subtitle_item_id"` // 要拆分的字幕项ID
	OriginText     string `json:"origin_text"`      // 原文（用于时间戳计算）
	OriginTextUp   string `json:"origin_text_up"`   // 拆分的第一段原文
	OriginTextDown string `json:"origin_text_down"` // 拆分的第二段原文
}

// SplitSubtitleResBO 拆分字幕片段响应 BO
type SplitSubtitleResBO struct {
	SubtitleItems []*CommentarySubtitleItemBO `json:"subtitle_items"`
}

// DeleteSubtitleItemReqBO 删除字幕项请求 BO
type DeleteSubtitleItemReqBO struct {
	AppId          int64  `json:"appid"`
	TenantId       int64  `json:"tenant_id"`
	ApiKey         string `json:"api_key"`
	SubTaskId      int64  `json:"sub_task_id"`
	SubtitleItemID int    `json:"subtitle_item_id"`
}

// DeleteSubtitleItemResBO 删除字幕项响应 BO
type DeleteSubtitleItemResBO struct {
	Success bool `json:"success"`
}

// ModifySubtitleReqBO 修改字幕请求 BO
type ModifySubtitleReqBO struct {
	AppId            int64   `json:"appid"`
	TenantId         int64   `json:"tenant_id"`
	ApiKey           string  `json:"api_key"`
	SubTaskId        int64   `json:"sub_task_id"`
	SubtitleItemId   int64   `json:"subtitle_item_id"`
	OriginSubtitle   string  `json:"origin_subtitle"`    //原文文本
	TargetSubtitle   string  `json:"target_subtitle"`    //译文文本
	SubtitleStartStr string  `json:"subtitle_start_str"` //译文字幕开始时间，逗号分隔
	SubtitleEndStr   string  `json:"subtitle_end_str"`   //译文字幕结束时间，逗号分隔
	Speed            float32 `json:"speed"`              // 语速
}

// ModifySubtitleResBO 修改字幕响应 BO
type ModifySubtitleResBO struct {
	SubtitleItem *CommentarySubtitleItemBO `json:"subtitle_item"`
}

// AddSubtitleReqBO 添加字幕请求 BO
type AddSubtitleReqBO struct {
	AppId                  int64             `json:"appid"`
	TenantId               int64             `json:"tenant_id"`
	ApiKey                 string            `json:"api_key"`
	SubTaskId              int64             `json:"sub_task_id"`
	ItemIdx                int32             `json:"item_idx"`
	OffId                  int64             `json:"off_id"`
	OriginSubtitle         string            `json:"origin_subtitle"`
	TargetSubtitle         string            `json:"target_subtitle"`
	SubtitleStartStr       string            `json:"subtitle_start_str"`
	SubtitleEndStr         string            `json:"subtitle_end_str"`
	OriginSubtitleStartStr string            `json:"origin_subtitle_start_str"`
	OriginSubtitleEndStr   string            `json:"origin_subtitle_end_str"`
	SpeakerId              int64             `json:"speaker_id"`
	VoiceInfo              *common.VoiceInfo `json:"voice_info"`
}

// AddSubtitleResBO 添加字幕响应 BO
type AddSubtitleResBO struct {
	SubtitleItem *CommentarySubtitleItemBO `json:"subtitle_item"`
}

// TextTranslateResBO 文本翻译响应 BO
type TextTranslateResBO struct {
	TaskCount int `json:"task_count"`
}

// GetBatchTextTranslateStatusReqBO 获取批量翻译状态请求 BO
type GetBatchTextTranslateStatusReqBO struct {
	AppId     int64  `json:"appid"`
	TenantId  int64  `json:"tenant_id"`
	ApiKey    string `json:"api_key"`
	SubTaskId int64  `json:"sub_task_id"`
}

// GenerateVoiceReqBO 生成配音请求 BO
type GenerateVoiceReqBO struct {
	AppId          int64  `json:"appid"`
	TenantId       int64  `json:"tenant_id"`
	ApiKey         string `json:"api_key"`
	SubTaskId      int64  `json:"sub_task_id"`
	SubtitleItemID int    `json:"subtitle_item_id"`
}

// GenerateVoiceResBO 生成配音响应 BO
type GenerateVoiceResBO struct {
	TaskCount int `json:"task_count"`
}

// GetBatchGenerateVoiceStatusReqBO 获取批量配音状态请求 BO
type GetBatchGenerateVoiceStatusReqBO struct {
	AppId     int64  `json:"appid"`
	TenantId  int64  `json:"tenant_id"`
	ApiKey    string `json:"api_key"`
	SubTaskId int64  `json:"sub_task_id"`
}

// GenerateVoiceStatusInfoBO 生成配音状态信息 BO
type GenerateVoiceStatusInfoBO struct {
	ItemIdx             int                   `json:"item_idx"`
	GenerateVoiceStatus int                   `json:"generate_voice_status"`
	TtsUrl              string                `json:"tts_url"`
	LatestTTSInfo       *common.LatestTTSInfo `json:"latest_tts_info"`
	AudioConfig         *common.AudioConfig   `json:"audio_config"`
	TtsWords            []*common.WordInfo    `json:"tts_words"`
	SubItemList         []*common.SubItem     `json:"sub_item_list"`
}

// GetBatchGenerateVoiceStatusResBO 获取批量配音状态响应 BO
type GetBatchGenerateVoiceStatusResBO struct {
	StatusInfos []*GenerateVoiceStatusInfoBO `json:"status_infos"`
}

// ModifySubtitleSpeedReqBO 修改片段语速请求 BO
type ModifySubtitleSpeedReqBO struct {
	AppId          int64   `json:"appid"`
	TenantId       int64   `json:"tenant_id"`
	ApiKey         string  `json:"api_key"`
	SubTaskId      int64   `json:"sub_task_id"`
	SubtitleItemId int64   `json:"subtitle_item_id"` // 片段的ID
	Speed          float32 `json:"speed"`            // 语速
	ApplyToAll     bool    `json:"apply_to_all"`     // 是否应用到全部片段
}

// SubtitleSpeedModificationBO 字幕语速修改 BO
type SubtitleSpeedModificationBO struct {
	SubtitleItemID int     `json:"subtitle_item_id"`
	Speed          float64 `json:"speed"`
}

// ModifySubtitleSpeedResBO 修改片段语速响应 BO
type ModifySubtitleSpeedResBO struct {
	SubtitleItems []*CommentarySubtitleItemBO `json:"subtitle_items"`
}

// ModifySubtitleVolumeReqBO 修改片段音量请求 BO
type ModifySubtitleVolumeReqBO struct {
	AppId          int64   `json:"appid"`
	TenantId       int64   `json:"tenant_id"`
	ApiKey         string  `json:"api_key"`
	SubTaskId      int64   `json:"sub_task_id"`
	SubtitleItemId int64   `json:"subtitle_item_id"` // 片段的ID
	VolumeGainDB   float32 `json:"volume_gain_db"`   // 音量加减值
	ApplyToAll     bool    `json:"apply_to_all"`     // 是否应用到全部片段
}

// SubtitleVolumeModificationBO 字幕音量修改 BO
type SubtitleVolumeModificationBO struct {
	SubtitleItemID int     `json:"subtitle_item_id"`
	Volume         float64 `json:"volume"`
}

// ModifySubtitleVolumeResBO 修改片段音量响应 BO
type ModifySubtitleVolumeResBO struct {
	SubtitleItems []*CommentarySubtitleItemBO `json:"subtitle_items"`
}
