package bo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/common"
	"time"
)

// CommentarySubTaskBO 解说子任务业务对象
type CommentarySubTaskBO struct {
	Id                        int64                                `json:"id,omitempty"`
	MainTaskId                int64                                `json:"main_task_id,omitempty"`
	Name                      string                               `json:"name,omitempty"`
	SpeakerId                 int64                                `json:"speaker_id,omitempty"`
	VoiceId                   int64                                `json:"voice_id,omitempty"`
	TenantId                  int64                                `json:"tenant_id,omitempty"`
	VideoDuration             float64                              `json:"video_duration,omitempty"`
	MaterialHighlightDuration float64                              `json:"material_highlight_duration,omitempty"`
	Status                    consts.CommentarySubTaskStatus       `json:"status,omitempty"`
	AuditStatus               consts.AuditStatus                   `json:"audit_status,omitempty"`
	MergeStatus               consts.CommentaryMergeStatus         `json:"merge_status,omitempty"`
	ErrCode                   int                                  `json:"err_code,omitempty"`
	ErrMsg                    string                               `json:"err_msg,omitempty"`
	EraseMode                 consts.CommentaryEraseMode           `json:"erase_mode,omitempty"`
	EraseEdition              consts.CommentaryEraseEdition        `json:"erase_edition,omitempty"`
	OcrRectInfo               *common.OcrRectInfo                  `json:"ocr_rect_info,omitempty"`
	BgmMode                   consts.CommentaryBgmMode             `json:"bgm_mode,omitempty"`
	SubtitleMode              consts.CommentarySubtitleMode        `json:"subtitle_mode,omitempty"`
	NoSubtitleVideoUrl        string                               `json:"no_subtitle_video_url,omitempty"`
	NoSubtitleVideoHlsUrl     string                               `json:"no_subtitle_video_hls_url,omitempty"`
	MergedVideoUrl            string                               `json:"merged_video_url,omitempty"`
	MergedVideoHlsUrl         string                               `json:"merged_video_hls_url,omitempty"`
	Size                      float64                              `json:"size,omitempty"`
	Resolution                string                               `json:"resolution,omitempty"`
	CoverUrl                  string                               `json:"cover_url,omitempty"`
	CustomSubtitleStyle       *common.CustomSubtitleStyle          `json:"custom_subtitle_style,omitempty"`
	VideoSegmentInfo          []*common.VideoSegmentInfo           `json:"video_segment_info,omitempty"`
	BgmUrl                    string                               `json:"bgm_url,omitempty"`
	CommentaryAgentRes        *common.QueryCommentaryQiFeiRespData `json:"commentary_agent_res,omitempty"`
	MaterialHighlightUrl      string                               `json:"material_highlight_url,omitempty"`
	HighlightEpisodesUrls     []string                             `json:"highlight_episodes_urls,omitempty"`
	EndTagUrl                 string                               `json:"end_tag_url,omitempty"`
	SubtitleFileUrl           string                               `json:"subtitle_file_url,omitempty"`
	NeedMerge                 int8                                 `json:"need_merge,omitempty"`
	SourceLangId              string                               `json:"source_lang_id,omitempty"`
	TargetLangId              string                               `json:"target_lang_id,omitempty"`
	SubtitleItemList          []*CommentarySubtitleItemBO          `json:"subtitle_item_list,omitempty"`
	CreatedAt                 time.Time                            `json:"created_at,omitempty"`
	UpdatedAt                 time.Time                            `json:"updated_at,omitempty"`
}

// CreateSubTasksReqBO 批量创建子任务请求 BO
type CreateSubTasksReqBO struct {
	SubTasks []*CommentarySubTaskBO `json:"sub_tasks"`
}

// CreateSubTasksResBO 批量创建子任务响应 BO
type CreateSubTasksResBO struct {
	Success bool `json:"success"`
}

// GetSubTasksByMainTaskIdReqBO 根据主任务ID获取子任务列表请求 BO
type GetSubTasksByMainTaskIdReqBO struct {
	MainTaskId int64 `json:"main_task_id"`
}

// GetSubTasksByMainTaskIdResBO 根据主任务ID获取子任务列表响应 BO
type GetSubTasksByMainTaskIdResBO struct {
	SubTasks []*CommentarySubTaskBO `json:"sub_tasks"`
}

// GetSubTaskByIdReqBO 根据ID获取子任务详情请求 BO
type GetSubTaskByIdReqBO struct {
	Id int64 `json:"id"`
}

// GetSubTaskByIdResBO 根据ID获取子任务详情响应 BO
type GetSubTaskByIdResBO struct {
	SubTask *CommentarySubTaskBO `json:"sub_task"`
}

// UpdateSubTaskStatusReqBO 更新子任务状态请求 BO
type UpdateSubTaskStatusReqBO struct {
	SubTaskId int64  `json:"sub_task_id"`
	Status    int    `json:"status"`
	ErrMsg    string `json:"err_msg"`
}

// UpdateSubTaskStatusResBO 更新子任务状态响应 BO
type UpdateSubTaskStatusResBO struct {
	Success bool `json:"success"`
}

// DeleteSubTaskReqBO 删除子任务请求 BO
type DeleteSubTaskReqBO struct {
	SubTaskId int64 `json:"sub_task_id"`
}

// DeleteSubTaskResBO 删除子任务响应 BO
type DeleteSubTaskResBO struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmReqBO 更新子任务背景音请求 BO
type UpdateSubTaskBgmReqBO struct {
	SubTaskId int64  `json:"sub_task_id"`
	BgmUrl    string `json:"bgm_url"`
}

// UpdateSubTaskBgmResBO 更新子任务背景音响应 BO
type UpdateSubTaskBgmResBO struct {
	Success bool `json:"success"`
}

// UpdateSubTaskEraseModeReqBO 更新子任务擦除模式请求 BO
type UpdateSubTaskEraseModeReqBO struct {
	SubTaskId    int64 `json:"sub_task_id"`
	EraseMode    int   `json:"erase_mode"`
	EraseEdition int   `json:"erase_edition"`
}

// UpdateSubTaskEraseModeResBO 更新子任务擦除模式响应 BO
type UpdateSubTaskEraseModeResBO struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmModeReqBO 更新子任务BGM模式请求 BO
type UpdateSubTaskBgmModeReqBO struct {
	SubTaskId int64 `json:"sub_task_id"`
	BgmMode   int   `json:"bgm_mode"`
}

// UpdateSubTaskBgmModeResBO 更新子任务BGM模式响应 BO
type UpdateSubTaskBgmModeResBO struct {
	Success bool `json:"success"`
}

// UpdateSubTaskOcrRectInfoReqBO 更新子任务OCR区域信息请求 BO
type UpdateSubTaskOcrRectInfoReqBO struct {
	SubTaskId   int64       `json:"sub_task_id"`
	OcrRectInfo interface{} `json:"ocr_rect_info"`
}

// UpdateSubTaskOcrRectInfoResBO 更新子任务OCR区域信息响应 BO
type UpdateSubTaskOcrRectInfoResBO struct {
	Success bool `json:"success"`
}

// UpdateSubTaskBgmModeByMainTaskIdReqBO 根据主任务ID更新所有子任务的BGM模式请求 BO
type UpdateSubTaskBgmModeByMainTaskIdReqBO struct {
	MainTaskId int64 `json:"main_task_id"`
	BgmMode    int   `json:"bgm_mode"`
}

// UpdateSubTaskBgmModeByMainTaskIdResBO 根据主任务ID更新所有子任务的BGM模式响应 BO
type UpdateSubTaskBgmModeByMainTaskIdResBO struct {
	Success bool `json:"success"`
}

// SaveGlobalSubtitleStyleReqBO 保存全局字幕样式请求
type SaveGlobalSubtitleStyleReqBO struct {
	SubTaskId           int64                       `json:"sub_task_id"`
	CustomSubtitleStyle *common.CustomSubtitleStyle `json:"custom_subtitle_style"` // 字幕样式
	Subtitles           []struct {
		ItemId      int64             `json:"item_id,string"` // 片段id
		SubItemList []*common.SubItem `json:"sub_item_list"`  // 字幕项
	} `json:"subtitles"` //有修改过的片段
}

// SaveGlobalSubtitleStyleResBO 保存全局字幕样式响应
type SaveGlobalSubtitleStyleResBO struct {
}
