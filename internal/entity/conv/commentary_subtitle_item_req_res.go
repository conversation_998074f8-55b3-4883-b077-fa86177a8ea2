package conv

import (
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/vo"
	"strings"
)

// DeleteSubtitleItemVOToBO 转换删除字幕项请求 VO 到 BO
func DeleteSubtitleItemVOToBO(voReq *vo.DeleteSubtitleItemReq) *bo.DeleteSubtitleItemReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.DeleteSubtitleItemReqBO{
		AppId:          voReq.Appid,
		TenantId:       voReq.TenantId,
		ApiKey:         voReq.ApiKey,
		SubTaskId:      voReq.Tid,
		SubtitleItemID: int(voReq.SubId), // 这里可能需要根据实际业务逻辑调整
	}
}

// ModifySubtitleVOToBO 转换修改字幕请求 VO 到 BO
func ModifySubtitleVOToBO(voReq *vo.ModifySubtitleReq) *bo.ModifySubtitleReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.ModifySubtitleReqBO{
		AppId:            voReq.Appid,
		TenantId:         voReq.TenantId,
		ApiKey:           voReq.ApiKey,
		SubTaskId:        voReq.Tid,
		SubtitleItemId:   voReq.SubId,
		OriginSubtitle:   voReq.OriginText,
		TargetSubtitle:   voReq.PostText,
		SubtitleStartStr: strings.Replace(voReq.StartMs, ".", ",", 1),
		SubtitleEndStr:   strings.Replace(voReq.EndMs, ".", ",", 1),
		Speed:            voReq.Speed,
	}
}

// AddSubtitleVOToBO 转换添加字幕请求 VO 到 BO
func AddSubtitleVOToBO(voReq *vo.AddSubtitleReq) *bo.AddSubtitleReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.AddSubtitleReqBO{
		AppId:                  voReq.Appid,
		TenantId:               voReq.TenantId,
		ApiKey:                 voReq.ApiKey,
		SubTaskId:              voReq.Tid,
		ItemIdx:                voReq.ItemIdx,
		SpeakerId:              int64(voReq.SpeakerId),
		OffId:                  voReq.OffId,
		TargetSubtitle:         voReq.PostText,
		OriginSubtitle:         voReq.OriginText,
		SubtitleStartStr:       strings.Replace(voReq.StartMs, ".", ",", 1),
		SubtitleEndStr:         strings.Replace(voReq.EndMs, ".", ",", 1),
		OriginSubtitleStartStr: strings.Replace(voReq.OriginStartMs, ".", ",", 1),
		OriginSubtitleEndStr:   strings.Replace(voReq.OriginEndMs, ".", ",", 1),
		VoiceInfo:              voReq.VoiceInfo.Clone(),
	}
}

// ModifySubtitleSpeedVOToBO 转换修改片段语速请求 VO 到 BO
func ModifySubtitleSpeedVOToBO(voReq *vo.ModifySubtitleSpeedReq) *bo.ModifySubtitleSpeedReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.ModifySubtitleSpeedReqBO{
		AppId:          voReq.Appid,
		TenantId:       voReq.TenantId,
		ApiKey:         voReq.ApiKey,
		SubTaskId:      voReq.Tid,
		SubtitleItemId: voReq.SubtitleItemId,
		Speed:          voReq.Speed,
		ApplyToAll:     voReq.ApplyToAll,
	}
}

// ModifySubtitleVolumeVOToBO 转换修改片段音量请求 VO 到 BO
func ModifySubtitleVolumeVOToBO(voReq *vo.ModifySubtitleVolumeReq) *bo.ModifySubtitleVolumeReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.ModifySubtitleVolumeReqBO{
		AppId:          voReq.Appid,
		TenantId:       voReq.TenantId,
		ApiKey:         voReq.ApiKey,
		SubTaskId:      voReq.Tid,
		SubtitleItemId: voReq.SubtitleItemId,
		VolumeGainDB:   voReq.VolumeGainDB,
		ApplyToAll:     voReq.ApplyToAll,
	}
}

// SplitSubtitleVOToBO 转换拆分字幕请求 VO 到 BO
func SplitSubtitleVOToBO(voReq *vo.SplitSubtitleReq) *bo.SplitSubtitleReqBO {
	if voReq == nil {
		return nil
	}

	return &bo.SplitSubtitleReqBO{
		AppId:          voReq.Appid,
		TenantId:       voReq.TenantId,
		ApiKey:         voReq.ApiKey,
		SubTaskId:      voReq.Tid,
		SubtitleItemId: voReq.SubId,
		OriginText:     voReq.OriginText,     // 原文
		OriginTextUp:   voReq.OriginTextUp,   // 拆分的第一段原文
		OriginTextDown: voReq.OriginTextDown, // 拆分的第二段原文
	}
}

// MergeSubtitleVOToBO 转换合并字幕请求 VO 到 BO
func MergeSubtitleVOToBO(voReq *vo.MergeSubtitleReq) *bo.MergeSubtitleReqBO {
	if voReq == nil {
		return nil
	}

	// 提取 ItemIdxList
	itemIdxList := make([]int, len(voReq.MergeSubtitleItemList))
	for i, item := range voReq.MergeSubtitleItemList {
		itemIdxList[i] = int(item.SubId)
	}

	// 使用第一个项目的基本参数
	if len(voReq.MergeSubtitleItemList) > 0 {
		firstItem := voReq.MergeSubtitleItemList[0]
		return &bo.MergeSubtitleReqBO{
			AppId:        voReq.Appid,
			TenantId:     voReq.TenantId,
			ApiKey:       voReq.ApiKey,
			SubTaskId:    firstItem.Tid,
			ItemIdxList:  itemIdxList,
			MergeText:    "", // 根据需要设置
			CustomPrompt: "", // 根据需要设置
		}
	}

	return &bo.MergeSubtitleReqBO{
		AppId:        voReq.Appid,
		TenantId:     voReq.TenantId,
		ApiKey:       voReq.ApiKey,
		ItemIdxList:  itemIdxList,
		MergeText:    "", // 根据需要设置
		CustomPrompt: "", // 根据需要设置
	}
}
