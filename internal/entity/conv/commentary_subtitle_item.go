package conv

import (
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util"
)

// CommentarySubtitleItemDOToBO 从 DO 转换为 BO
func CommentarySubtitleItemDOToBO(doObj *do.CommentarySubtitleItem) *bo.CommentarySubtitleItemBO {
	if doObj == nil {
		return nil
	}

	bo := &bo.CommentarySubtitleItemBO{
		Id:                      doObj.Id,
		SubTaskId:               doObj.SubTaskId,
		MainTaskId:              doObj.MainTaskId,
		ItemIdx:                 doObj.ItemIdx,
		OriginSubtitle:          doObj.OriginSubtitle,
		TargetSubtitle:          doObj.TargetSubtitle,
		SourceLangId:            doObj.SourceLangId,
		TargetLangId:            doObj.TargetLangId,
		SubtitleStartStr:        doObj.SubtitleStartStr,
		SubtitleEndStr:          doObj.SubtitleEndStr,
		OriginSubtitleStartStr:  doObj.OriginSubtitleStartStr,
		OriginSubtitleEndStr:    doObj.OriginSubtitleEndStr,
		GenerateVoiceStatus:     doObj.GenerateVoiceStatus,
		TextTranslateStatus:     doObj.TextTranslateStatus,
		LatestTextTranslateInfo: doObj.LatestTextTranslateInfo,
		TTSUrl:                  doObj.TTSUrl,
		SpeakerId:               doObj.SpeakerId,
		CustomPrompt:            doObj.CustomPrompt,
		AudioConfig:             doObj.AudioConfig,
		VoiceInfo:               doObj.VoiceInfo,
		TTSWords:                doObj.TTSWords,
		SubItemList:             doObj.SubItemList,
		LatestTTSInfo:           doObj.LatestTTSInfo,
		OriginAudio:             doObj.OriginAudio,
		SpeakerName:             doObj.SpeakerName,
		LastModify:              doObj.UpdatedAt,
		CreatedAt:               doObj.CreatedAt,
		UpdatedAt:               doObj.UpdatedAt,
	}

	// 处理复杂嵌套对象 - 使用通用结构体的 Clone 方法
	if doObj.LatestTTSInfo != nil {
		bo.LatestTTSInfo = doObj.LatestTTSInfo.Clone()
	}

	if doObj.AudioConfig != nil {
		bo.AudioConfig = doObj.AudioConfig.Clone()
	}
	if doObj.LatestTextTranslateInfo != nil {
		bo.LatestTextTranslateInfo = doObj.LatestTextTranslateInfo.Clone()
	}

	// 转换 TTSWords - 使用通用的克隆函数
	if len(doObj.TTSWords) > 0 {
		bo.TTSWords = common.CloneWordInfoSlice(doObj.TTSWords)
	}

	// 转换 SubItemList - 使用通用的克隆函数
	if len(doObj.SubItemList) > 0 {
		bo.SubItemList = common.CloneSubItemSlice(doObj.SubItemList)
	}

	return bo
}

// CommentarySubtitleItemBOToDO 从 BO 转换为 DO
func CommentarySubtitleItemBOToDO(bo *bo.CommentarySubtitleItemBO) *do.CommentarySubtitleItem {
	if bo == nil {
		return nil
	}

	doObj := &do.CommentarySubtitleItem{
		Id:                      bo.Id,
		SubTaskId:               bo.SubTaskId,
		MainTaskId:              bo.MainTaskId,
		ItemIdx:                 bo.ItemIdx,
		OriginSubtitle:          bo.OriginSubtitle,
		TargetSubtitle:          bo.TargetSubtitle,
		SourceLangId:            bo.SourceLangId,
		TargetLangId:            bo.TargetLangId,
		SubtitleStartStr:        bo.SubtitleStartStr,
		SubtitleEndStr:          bo.SubtitleEndStr,
		OriginSubtitleStartStr:  bo.OriginSubtitleStartStr,
		OriginSubtitleEndStr:    bo.OriginSubtitleEndStr,
		GenerateVoiceStatus:     bo.GenerateVoiceStatus,
		TextTranslateStatus:     bo.TextTranslateStatus,
		LatestTextTranslateInfo: bo.LatestTextTranslateInfo,
		BackTranslateText:       bo.BackTranslateText,
		TTSUrl:                  bo.TTSUrl,
		SpeakerId:               bo.SpeakerId,
		CustomPrompt:            bo.CustomPrompt,
	}

	// 处理时间字段
	doObj.CreatedAt = bo.CreatedAt
	doObj.UpdatedAt = bo.UpdatedAt

	// 处理复杂嵌套对象 - 直接使用通用结构体的 Clone 方法
	if bo.LatestTTSInfo != nil {
		doObj.LatestTTSInfo = bo.LatestTTSInfo.Clone()
	}

	if bo.AudioConfig != nil {
		doObj.AudioConfig = bo.AudioConfig.Clone()
	}
	if bo.LatestTextTranslateInfo != nil {
		doObj.LatestTextTranslateInfo = bo.LatestTextTranslateInfo.Clone()
	}

	// 转换 TTSWords - 使用通用的克隆函数
	if len(bo.TTSWords) > 0 {
		doObj.TTSWords = common.CloneWordInfoSlice(bo.TTSWords)
	}

	// 转换 SubItemList - 使用通用的克隆函数
	if len(bo.SubItemList) > 0 {
		doObj.SubItemList = common.CloneSubItemSlice(bo.SubItemList)
	}

	return doObj
}

// CommentarySubtitleItemDOToVO 从 DO 转换为 SubtitleItem VO
func CommentarySubtitleItemDOToVO(doObj *do.CommentarySubtitleItem) *vo.SubtitleItem {
	if doObj == nil {
		return nil
	}

	vo := &vo.SubtitleItem{
		Id:                        doObj.Id,
		OriginText:                doObj.OriginSubtitle,
		PostText:                  doObj.TargetSubtitle,
		StartTime:                 doObj.SubtitleStartStr,
		EndTime:                   doObj.SubtitleEndStr,
		OriginStartTime:           doObj.OriginSubtitleStartStr,
		OriginEndTime:             doObj.OriginSubtitleEndStr,
		TransAudio:                doObj.TTSUrl,
		SpeakerId:                 int(doObj.SpeakerId),
		BackTranslateText:         doObj.BackTranslateText,
		LatestTranslateBackText:   doObj.LatestTextTranslateInfo.LatestTranslateBackText,
		LatestTranslateOriginText: doObj.LatestTextTranslateInfo.LatestOriginSubtitle,
	}

	// 处理复杂嵌套对象 - 直接使用通用结构体的 Clone 方法
	if doObj.LatestTTSInfo != nil {
		vo.LatestTTSInfo = doObj.LatestTTSInfo.Clone()
	}

	if doObj.AudioConfig != nil {
		vo.AudioConfig = doObj.AudioConfig.Clone()
	}

	// 转换 SubtitleWords - 使用通用的克隆函数
	if len(doObj.TTSWords) > 0 {
		vo.SubtitleWords = common.CloneWordInfoSlice(doObj.TTSWords)
	}

	// 转换 SubItemList - 暂时注释，因为类型解析问题
	// if len(doObj.SubItemList) > 0 {
	// 	vo.SubItemList = make([]*vo.SubItemVO, len(doObj.SubItemList))
	// 	for i, subItem := range doObj.SubItemList {
	// 		vo.SubItemList[i] = &vo.SubItemVO{
	// 			StartTime: fmt.Sprintf("%d", subItem.BeginMs), // 转换为字符串
	// 			EndTime:   fmt.Sprintf("%d", subItem.EndMs),   // 转换为字符串
	// 			Text:      subItem.SubPostText,
	// 		}
	// 	}
	// }

	return vo
}

// BO 到 VO 转换方法

// CommentarySubtitleItemBOToVO 转换字幕项 BO 到 VO
func CommentarySubtitleItemBOToVO(bo *bo.CommentarySubtitleItemBO) *vo.SubtitleItem {
	if bo == nil {
		return nil
	}

	return &vo.SubtitleItem{
		Id:                        bo.Id,
		UuId:                      bo.Id,
		OriginText:                bo.OriginSubtitle,
		PostText:                  bo.TargetSubtitle,
		StartTime:                 bo.SubtitleStartStr,
		EndTime:                   bo.SubtitleEndStr,
		OriginStartTime:           bo.OriginSubtitleStartStr,
		OriginEndTime:             bo.OriginSubtitleEndStr,
		OriginAudio:               bo.OriginAudio,
		TransAudio:                bo.TTSUrl,
		LatestTTSInfo:             bo.LatestTTSInfo.Clone(),
		BackTranslateText:         bo.BackTranslateText,
		LastModify:                bo.UpdatedAt.Unix(),
		SpeakerId:                 int(bo.SpeakerId),
		SpeakerName:               bo.SpeakerName,
		RegenerateStatus:          bo.GenerateVoiceStatus,
		ReTranslateStatus:         bo.TextTranslateStatus,
		AudioConfig:               bo.AudioConfig,
		VoiceInfo:                 bo.VoiceInfo,
		LatestTranslateOriginText: bo.LatestTextTranslateInfo.LatestOriginSubtitle,
		LatestTranslateBackText:   bo.LatestTextTranslateInfo.LatestTranslateBackText,
		TargetSubtitle:            bo.TargetSubtitle,
		SubtitleWords:             bo.TTSWords,
		SubItemList:               CommentarySubItemListBOToVO(bo.SubItemList),
	}
}
func CommentarySubItemListBOToVO(subItemList []*common.SubItem) []*vo.SubItemVO {
	subItems := make([]*vo.SubItemVO, 0, len(subItemList))
	for _, val := range subItemList {
		unit := &vo.SubItemVO{
			StartTime: util.SrtTimeMs2String(val.BeginMs),
			EndTime:   util.SrtTimeMs2String(val.EndMs),
			Text:      val.SubPostText,
		}
		subItems = append(subItems, unit)
	}
	return subItems
}
