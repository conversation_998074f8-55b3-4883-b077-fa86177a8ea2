package conv

import (
	"business-workflow/internal/entity/bo"
	ebo "business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util/srt_util"

	"github.com/samber/lo"
)

// CommentarySubTaskDOToBO 从 DO 转换为 BO
func CommentarySubTaskDOToBO(doObj *do.CommentarySubTask) *bo.CommentarySubTaskBO {
	if doObj == nil {
		return nil
	}

	bo := &bo.CommentarySubTaskBO{
		Id:                        doObj.Id,
		MainTaskId:                doObj.MainTaskId,
		Name:                      doObj.Name,
		SpeakerId:                 doObj.SpeakerId,
		VoiceId:                   doObj.VoiceId,
		TenantId:                  doObj.TenantId,
		VideoDuration:             doObj.VideoDuration,
		MaterialHighlightDuration: doObj.MaterialHighlightDuration,
		Status:                    doObj.Status,
		AuditStatus:               doObj.AuditStatus,
		MergeStatus:               doObj.MergeStatus,
		ErrCode:                   doObj.ErrCode,
		ErrMsg:                    doObj.ErrMsg,
		EraseMode:                 doObj.EraseMode,
		EraseEdition:              doObj.EraseEdition,
		OcrRectInfo:               doObj.OcrRectInfo,
		BgmMode:                   doObj.BgmMode,
		SubtitleMode:              doObj.SubtitleMode,
		NoSubtitleVideoUrl:        doObj.NoSubtitleVideoUrl,
		NoSubtitleVideoHlsUrl:     doObj.NoSubtitleVideoHlsUrl,
		MergedVideoUrl:            doObj.MergedVideoUrl,
		MergedVideoHlsUrl:         doObj.MergedVideoHlsUrl,
		Size:                      doObj.Size,
		Resolution:                doObj.Resolution,
		CoverUrl:                  doObj.CoverUrl,
		CustomSubtitleStyle:       doObj.CustomSubtitleStyle,
		//VideoSegmentInfo:      doObj.VideoSegmentInfo,
		BgmUrl:                doObj.BgmUrl,
		CommentaryAgentRes:    doObj.CommentaryAgentRes,
		MaterialHighlightUrl:  doObj.MaterialHighlightUrl,
		HighlightEpisodesUrls: doObj.HighlightEpisodesUrls,
		EndTagUrl:             doObj.EndTagUrl,
		SubtitleFileUrl:       doObj.SubtitleFileUrl,
		NeedMerge:             doObj.NeedMerge,
		SourceLangId:          doObj.SourceLangId,
		TargetLangId:          doObj.TargetLangId,
		CreatedAt:             doObj.CreatedAt,
		UpdatedAt:             doObj.UpdatedAt,
	}

	return bo
}

// CommentarySubTaskBOToDO 从 BO 转换为 DO
func CommentarySubTaskBOToDO(bo *bo.CommentarySubTaskBO) *do.CommentarySubTask {
	if bo == nil {
		return nil
	}

	doObj := &do.CommentarySubTask{
		Id:                        bo.Id,
		MainTaskId:                bo.MainTaskId,
		Name:                      bo.Name,
		SpeakerId:                 bo.SpeakerId,
		VoiceId:                   bo.VoiceId,
		TenantId:                  bo.TenantId,
		VideoDuration:             bo.VideoDuration,
		MaterialHighlightDuration: bo.MaterialHighlightDuration,
		Status:                    bo.Status,
		AuditStatus:               bo.AuditStatus,
		MergeStatus:               bo.MergeStatus,
		ErrCode:                   bo.ErrCode,
		ErrMsg:                    bo.ErrMsg,
		EraseMode:                 bo.EraseMode,
		EraseEdition:              bo.EraseEdition,
		OcrRectInfo:               bo.OcrRectInfo,
		BgmMode:                   bo.BgmMode,
		SubtitleMode:              bo.SubtitleMode,
		NoSubtitleVideoUrl:        bo.NoSubtitleVideoUrl,
		NoSubtitleVideoHlsUrl:     bo.NoSubtitleVideoHlsUrl,
		MergedVideoUrl:            bo.MergedVideoUrl,
		MergedVideoHlsUrl:         bo.MergedVideoHlsUrl,
		Size:                      bo.Size,
		Resolution:                bo.Resolution,
		CoverUrl:                  bo.CoverUrl,
		CustomSubtitleStyle:       bo.CustomSubtitleStyle,
		//VideoSegmentInfo:          bo.VideoSegmentInfo,
		BgmUrl:                bo.BgmUrl,
		CommentaryAgentRes:    bo.CommentaryAgentRes,
		MaterialHighlightUrl:  bo.MaterialHighlightUrl,
		HighlightEpisodesUrls: bo.HighlightEpisodesUrls,
		EndTagUrl:             bo.EndTagUrl,
		SubtitleFileUrl:       bo.SubtitleFileUrl,
		NeedMerge:             bo.NeedMerge,
		SourceLangId:          bo.SourceLangId,
		TargetLangId:          bo.TargetLangId,
	}

	// 处理时间字段
	doObj.CreatedAt = bo.CreatedAt
	doObj.UpdatedAt = bo.UpdatedAt

	return doObj
}

// CommentarySubTaskBOToVO 从 BO 转换为 VO
func CommentarySubTaskBOToVO(bo *bo.CommentarySubTaskBO) *vo.CommentarySubTaskVO {
	if bo == nil {
		return nil
	}

	voObj := &vo.CommentarySubTaskVO{
		Id:                        bo.Id,
		MainTaskId:                bo.MainTaskId,
		Name:                      bo.Name,
		SpeakerId:                 bo.SpeakerId,
		VoiceId:                   bo.VoiceId,
		TenantId:                  bo.TenantId,
		VideoDuration:             bo.VideoDuration,
		Status:                    bo.Status,
		AuditStatus:               bo.AuditStatus,
		MergeStatus:               bo.MergeStatus,
		MaterialHighlightDuration: bo.MaterialHighlightDuration,
		ErrCode:                   bo.ErrCode,
		ErrMsg:                    bo.ErrMsg,
		EraseMode:                 bo.EraseMode,
		EraseEdition:              bo.EraseEdition,
		BgmMode:                   bo.BgmMode,
		SubtitleMode:              bo.SubtitleMode,
		NoSubtitleVideoUrl:        bo.NoSubtitleVideoUrl,
		NoSubtitleVideoHlsUrl:     bo.NoSubtitleVideoHlsUrl,
		MergedVideoUrl:            bo.MergedVideoUrl,
		MergedVideoHlsUrl:         bo.MergedVideoHlsUrl,
		Size:                      bo.Size,
		Resolution:                bo.Resolution,
		CoverUrl:                  bo.CoverUrl,
		CustomSubtitleStyle:       bo.CustomSubtitleStyle,
		BgmUrl:                    bo.BgmUrl,
		MaterialHighlightUrl:      bo.MaterialHighlightUrl,
		HighlightEpisodesUrls:     bo.HighlightEpisodesUrls,
		EndTagUrl:                 bo.EndTagUrl,
		SubtitleFileUrl:           bo.SubtitleFileUrl,
		NeedMerge:                 bo.NeedMerge,
		SourceLangId:              bo.SourceLangId,
		TargetLangId:              bo.TargetLangId,
		CreatedAt:                 bo.CreatedAt,
		UpdatedAt:                 bo.UpdatedAt,
	}
	if bo.VideoSegmentInfo != nil {
		// VideoSegmentInfo 在 common 中是单个 VideoSegment，不是数组
		voObj.VideoSegmentInfo = lo.Map(bo.VideoSegmentInfo, func(vi *common.VideoSegmentInfo, _ int) *vo.VideoSegmentInfoVO {
			return &vo.VideoSegmentInfoVO{
				StartTime:   vi.StartTime,
				EndTime:     vi.EndTime,
				SourceUrl:   vi.SourceUrl,
				Duration:    vi.Duration,
				SegmentType: vi.SegmentType,
			}
		})
	}
	if len(bo.SubtitleItemList) > 0 {
		voObj.SubtitleItemList = lo.Map(bo.SubtitleItemList, func(subItemBO *ebo.CommentarySubtitleItemBO, _ int) *vo.SubtitleItem {
			return CommentarySubtitleItemBOToVO(subItemBO)
		})
	}

	return voObj
}

// SaveGlobalSubtitleStyleVOToBO 转换保存全局字幕样式请求从VO到BO
func SaveGlobalSubtitleStyleVOToBO(voReq *vo.SaveGlobalSubtitleStyleReq) *bo.SaveGlobalSubtitleStyleReqBO {
	if voReq == nil {
		return nil
	}

	// 转换字幕项列表
	subtitles := make([]struct {
		ItemId      int64             `json:"item_id,string"`
		SubItemList []*common.SubItem `json:"sub_item_list"`
	}, 0, len(voReq.Subtitles))

	for _, voSubtitle := range voReq.Subtitles {
		subItemVOList, err := toSubItemDOList(voSubtitle.SubItemList)
		if err != nil {
			return nil
		}
		subtitle := struct {
			ItemId      int64             `json:"item_id,string"`
			SubItemList []*common.SubItem `json:"sub_item_list"`
		}{
			ItemId:      voSubtitle.ItemId,
			SubItemList: subItemVOList,
		}
		subtitles = append(subtitles, subtitle)
	}

	return &bo.SaveGlobalSubtitleStyleReqBO{
		SubTaskId:           voReq.Tid,
		CustomSubtitleStyle: voReq.CustomSubtitleStyle,
		Subtitles:           subtitles,
	}
}

func toSubItemVOList(subItemList []*common.SubItem) []*vo.SubItemVO {
	subItems := make([]*vo.SubItemVO, 0, len(subItemList))
	for _, val := range subItemList {
		unit := &vo.SubItemVO{
			StartTime: srt_util.SrtTimeMs2String(val.BeginMs),
			EndTime:   srt_util.SrtTimeMs2String(val.EndMs),
			Text:      val.SubPostText,
		}
		subItems = append(subItems, unit)
	}
	return subItems
}

func toSubItemDOList(subItemList []*vo.SubItemVO) (entitySubItems []*common.SubItem, err error) {
	for _, val := range subItemList {
		beginMs, err := srt_util.SrtStringTime2MsDot(val.StartTime)
		if err != nil {
			return nil, err
		}
		endMs, err := srt_util.SrtStringTime2MsDot(val.EndTime)
		if err != nil {
			return nil, err
		}
		entitySubItem := &common.SubItem{
			BeginMs:     beginMs,
			EndMs:       endMs,
			SubPostText: val.Text,
		}
		entitySubItems = append(entitySubItems, entitySubItem)
	}
	return
}

// SaveGlobalSubtitleStyleBOToVO 转换保存全局字幕样式响应从BO到VO
func SaveGlobalSubtitleStyleBOToVO(boRes *bo.SaveGlobalSubtitleStyleResBO) *vo.SaveGlobalSubtitleStyleRes {
	if boRes == nil {
		return nil
	}

	return &vo.SaveGlobalSubtitleStyleRes{}
}
