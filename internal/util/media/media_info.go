package media

import (
	"strconv"
	"strings"
)

var (
	videoCodecs = []string{"h264", "hevc", "avc", "h265", "av1", "vp9", "vp8", "mpeg", "mpeg4", "mpeg-4", "prores"}
)

func isVideoCodec(codecName string) bool {
	codecName = strings.ToLower(codecName)
	for _, val := range videoCodecs {
		if strings.Contains(codecName, val) {
			return true
		}
	}
	return false
}

func isImageCodec(codecName string) bool {
	codecName = strings.ToLower(codecName)
	if strings.Contains(codecName, "png") || strings.Contains(codecName, "jpg") || strings.Contains(codecName, "jpeg") {
		return true
	}
	return false
}

type MediaStreamInfo struct {
	Index              int    `json:"index"`
	CodecName          string `json:"codec_name"`
	CodecType          string `json:"codec_type"`
	AvgFrameRate       string `json:"avg_frame_rate"`
	BitRate            string `json:"bit_rate"`
	SampleAspectRatio  string `json:"sample_aspect_ratio"`
	DisplayAspectRatio string `json:"display_aspect_ratio"`
	Width              int32  `json:"width"`
	Height             int32  `json:"height"`
	Duration           string `json:"duration"`
	SampleRate         string `json:"sample_rate"`
	Channels           int    `json:"channels"`
	TimeBase           string `json:"time_base"`
	CodecTagString     string `json:"codec_tag_string"`
}

type StreamsInfo struct {
	Streams []*MediaStreamInfo `json:"streams"`
}

type WaterMarkInfo struct {
	PngPath      string //水印图片路径
	PngWidth     int    //水印的宽高
	PngHeight    int
	VideoWidth   int //对应视频的宽高
	VideoHeight  int
	MarginRight  int //右边距
	MarginBottom int //底部边距
}

type VideoInfo struct {
	AvgFrameRate       int //帧率
	Width              int32
	Height             int32
	BitRate            int32
	Duration           int
	DurationFloat      float64
	CodecName          string
	SampleAspectRatio  string
	DisplayAspectRatio string
	TimeBase           string
}

type AudioInfo struct {
	BitRate       int
	Duration      int
	DurationFloat float64
	DurationStr   string
	SampleRate    int
	Channels      int
	CodecName     string
}

type ImageInfo struct {
	Width     int32
	Height    int32
	CodecName string
	Duration  int
}

type DataInfo struct {
	CodecTagString string
}

type AudioVideoInfo struct {
	VideoInfo
	AudioInfo
	DataInfo
	ImageInfo
}

func (s *StreamsInfo) GetAudioVideoInfo() *AudioVideoInfo {
	info := &AudioVideoInfo{}
	info.AudioInfo.Duration = -1
	info.VideoInfo.Duration = -1
	info.ImageInfo.Duration = -1
	for _, val := range s.Streams {
		if val.CodecType == "video" {
			if isImageCodec(val.CodecName) {
				info.ImageInfo.CodecName = val.CodecName
				info.ImageInfo.Width = val.Width
				info.ImageInfo.Height = val.Height
				continue
			}
			if !isVideoCodec(val.CodecName) {
				continue
			}
			idx := strings.Index(val.AvgFrameRate, "/")
			if idx >= 0 {
				numStr := val.AvgFrameRate[0:idx]
				denStr := val.AvgFrameRate[(idx + 1):]
				num, err := strconv.Atoi(numStr)
				den, err2 := strconv.Atoi(denStr)
				if err == nil && err2 == nil && den > 0 {
					info.VideoInfo.AvgFrameRate = num / den
				}
			}
			info.VideoInfo.CodecName = val.CodecName
			//宽高
			info.VideoInfo.Width = val.Width
			info.VideoInfo.Height = val.Height
			info.SampleAspectRatio = val.SampleAspectRatio
			info.DisplayAspectRatio = val.DisplayAspectRatio
			//码率
			bitRate, err := strconv.Atoi(val.BitRate)
			if err == nil {
				info.VideoInfo.BitRate = int32(bitRate)
			}
			//时长（秒）
			duration, err := strconv.ParseFloat(val.Duration, 64)
			if err == nil {
				info.VideoInfo.Duration = int(duration)
				info.VideoInfo.DurationFloat = duration
			}
			info.VideoInfo.TimeBase = val.TimeBase
		}
		//音频
		if val.CodecType == "audio" {
			//码率
			bitRate, err := strconv.Atoi(val.BitRate)
			if err == nil {
				info.AudioInfo.BitRate = bitRate
			}
			//时长（秒）
			duration, err := strconv.ParseFloat(val.Duration, 64)
			if err == nil {
				info.AudioInfo.Duration = int(duration)
				info.AudioInfo.DurationFloat = duration
				info.AudioInfo.DurationStr = val.Duration
			}
			info.AudioInfo.CodecName = val.CodecName
			//通道数
			info.AudioInfo.Channels = val.Channels
			//采样率
			sampleRate, err := strconv.Atoi(val.SampleRate)
			if err == nil {
				info.AudioInfo.SampleRate = sampleRate
			}
		}
		if val.CodecType == "data" {
			info.DataInfo.CodecTagString = val.CodecTagString
		}
	}

	return info
}

func (s *StreamsInfo) GetVideoFrameNum() int {
	for _, val := range s.Streams {
		if val.CodecType == "video" {
			idx := strings.Index(val.AvgFrameRate, "/")
			if idx >= 0 {
				numStr := val.AvgFrameRate[0:idx]
				num, err := strconv.Atoi(numStr)
				if err == nil {
					return num
				}
			}
			break
		}
	}

	return 0
}
