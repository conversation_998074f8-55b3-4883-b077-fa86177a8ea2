package media

import (
	"business-workflow/internal/common"
	"business-workflow/internal/common/config"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"github.com/google/uuid"

	hobs "business-workflow/internal/util/obs"
)

var (
	FfmpegPath string

	hWaterFormat = WaterMarkInfo{
		PngPath:   "/translate_server/bin/watermark_h.png",
		PngWidth:  480,
		PngHeight: 128,
		//VideoWidth:   251,
		//VideoHeight:  446,
		MarginRight:  1,
		MarginBottom: 1,
	}

	wWaterFormat = WaterMarkInfo{
		PngPath:   "/translate_server/bin/watermark_w.png",
		PngWidth:  480,
		PngHeight: 128,
		//VideoWidth:   794,
		//VideoHeight:  446,
		MarginRight:  1,
		MarginBottom: 1,
	}
)

func InitWaterMarkImage() error {
	avInfo, err := GetAudioVideoInfo(hWaterFormat.PngPath)
	if err != nil {
		return err
	}
	hWaterFormat.PngWidth = int(avInfo.ImageInfo.Width)
	hWaterFormat.PngHeight = int(avInfo.ImageInfo.Height)

	avInfo, err = GetAudioVideoInfo(wWaterFormat.PngPath)
	if err != nil {
		return err
	}
	wWaterFormat.PngWidth = int(avInfo.ImageInfo.Width)
	wWaterFormat.PngHeight = int(avInfo.ImageInfo.Height)
	return nil
}

func ExtractFirstImage(inputFile string, outputFile string) error {
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-ss", "00:00:00",
		"-vframes", "1",
		"-y", outputFile,
	)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		return err
	}
	//durationStr := strings.TrimSpace(out.String())
	return nil
}

// CheckFFmpegInstallation 检查 ffmpeg 是否已正确安装并返回版本信息
func CheckFFmpegInstallation() (string, error) {
	cmd := exec.Command(FfmpegPath+"ffmpeg", "-version")
	var stdout bytes.Buffer
	cmd.Stdout = &stdout
	if err := cmd.Run(); err != nil {
		return "", errors.New("ffmpeg is not installed or not found in PATH")
	}
	versionInfo := stdout.String()
	return versionInfo, nil
}

func GetAudioVideoInfo(inputFile string) (*AudioVideoInfo, error) {
	//ffprobe -v error -print_format json -show_format -show_streams x.mp4
	cmd := exec.Command(FfmpegPath+"ffprobe", "-v", "error", "-print_format", "json", "-show_format", "-show_streams", inputFile)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		g.Log().Errorf(context.TODO(), "cmd: %s", cmd.String())
		return nil, err
	} else {
		Str := strings.TrimSpace(out.String())

		info := &StreamsInfo{}
		err := json.Unmarshal([]byte(Str), info)
		if err != nil {
			return nil, err
		} else {
			//num := info.GetVideoFrameNum()
			//fmt.Println(num)
			avInfo := info.GetAudioVideoInfo()
			return avInfo, nil
		}
	}
}

func GetVideoDuration(url string) (int, error) {
	cmd := exec.Command(FfmpegPath+"ffprobe", "-i", url, "-show_entries", "format=duration", "-v", "quiet", "-of", "csv=p=0")
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		return 0, fmt.Errorf("failed to get video duration: %v", err)
	}

	durationStr := strings.TrimSpace(out.String())
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse video duration: %v", err)
	}

	return int(duration), nil
}

func GetVideoDurationByObjectName(objectName string) (float64, error) {
	// 获取视频预签名链接
	presignUrl, err := hobs.GetOsClient().Presign(objectName)
	if err != nil {
		return 0, err
	}
	avInfo, err := GetAudioVideoInfo(presignUrl)
	if err != nil {
		return 0, err
	}
	g.Log().Debugf(context.Background(), "GetVideoDurationByObjectName avInfo: %v", avInfo)
	return avInfo.VideoInfo.DurationFloat, nil
}

// func GetVideoDurationUserMediaInfo(url string) (int, error) {

// 	cmd := exec.Command("mediainfo", "--Inform=General;%Duration%", url)
// 	var out bytes.Buffer
// 	cmd.Stdout = &out
// 	if err := cmd.Run(); err != nil {
// 		return 0, fmt.Errorf("failed to get video duration: %v", err)
// 	}

// 	durationStr := strings.TrimSpace(out.String())
// 	durationMs, err := strconv.ParseFloat(durationStr, 64)
// 	if err != nil {
// 		return 0, fmt.Errorf("failed to parse video duration: %v", err)
// 	}
// 	durationSeconds := durationMs / 1000 // 毫秒转秒

// 	return int(durationSeconds), nil
// }

func GetVideoDurationUserMediaInfo2(url string) (int, error) {

	avInfo, err := GetAudioVideoInfo(url)
	if err != nil {
		return 0, err
	}
	return avInfo.VideoInfo.Duration, nil

	//return int(durationSeconds), nil
}

// func GetMediaInfo(remoteUrl string) (string, error) {
// 	cmd := exec.Command("mediainfo", "--Inform=General", remoteUrl)
// 	var out bytes.Buffer
// 	cmd.Stdout = &out
// 	if err := cmd.Run(); err != nil {
// 		return "", err
// 	}
// 	//fmt.Println(out.String())
// 	return out.String(), nil
// }

func CombineVideoAndSubtitles(videoFile, subtitleFile, outputFullPath string, videoBitrate int) error {
	// 确保输出目录存在
	//if err := common.EnsureOutputDirectory(outputPath); err != nil {
	//	return "", err
	//}

	//_, extName, fileName := getFileNameDetails(videoFile)

	// 获取视频和音频码率
	//videoBitrate := getVideoBitrate(videoFile)
	//outputFile := fmt.Sprintf("%s/%s%s", outputPath, fileName, extName)

	//ffmpeg -i file.mp4 -vf subtitles=merge.ass -c:v libx264 -b:v 2000000 -preset slow -c:a copy -y
	threadCore := "4"
	if config.GetConfig().MergeThreadCore > 0 {
		threadCore = strconv.FormatInt(int64(config.GetConfig().MergeThreadCore), 10)
	}
	var cmd *exec.Cmd
	if runtime.GOOS == "windows" {
		//无法合成
		cmd = exec.Command("ffmpeg",
			"-i", videoFile,
			"-threads", threadCore,
			"-vf", "\"subtitles=merge.ass\"",
			"-c:v", "libx264",
			"-b:v", strconv.Itoa(videoBitrate),
			"-preset", "slow",
			"-c:a", "copy",
			"-y", outputFullPath)
	} else {
		cmd = exec.Command("ffmpeg",
			"-i", videoFile,
			"-threads", threadCore,
			"-vf", fmt.Sprintf("subtitles=%s", subtitleFile),
			"-c:v", "libx264",
			"-b:v", strconv.Itoa(videoBitrate),
			"-preset", "slow",
			"-c:a", "copy",
			"-y", outputFullPath)
	}
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to merge subtitles, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func ConvertVideoSar(videoFile, outputFullPath, crf, preset, sar string) error {
	threadCore := "4"
	if config.GetConfig().MergeThreadCore > 0 {
		threadCore = strconv.FormatInt(int64(config.GetConfig().MergeThreadCore), 10)
	}
	vec := strings.Split(sar, ":")
	if len(vec) != 2 {
		return fmt.Errorf("invalid sar format: %s", sar)
	}
	sarFmt := "h264_metadata=sample_aspect_ratio=" + vec[0] + "/" + vec[1]
	cmd := exec.Command("ffmpeg",
		"-i", videoFile,
		"-threads", threadCore,
		"-c:v", "libx264",
		"-crf", crf,
		"-preset", preset,
		"-bsf:v", sarFmt,
		"-c:a", "copy",
		"-y", outputFullPath)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(context.TODO(), "ConvertVideoSar, cmdStr: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to ConvertVideoSar, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func ScaleVideo(videoFile, outputFullPath, crf, preset string, width, height int) error {
	threadCore := "16"
	cmd := exec.Command("ffmpeg",
		"-i", videoFile,
		"-threads", threadCore,
		"-c:v", "libx264",
		"-crf", crf,
		"-preset", preset,
		"-vf", fmt.Sprintf("scale=%d:%d", width, height),
		"-c:a", "copy",
		"-y", outputFullPath)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(context.TODO(), "ScaleVideo, cmdStr: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to ScaleVideo, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func CombineVideoAndSubtitlesWithPreset(taskId int64, videoFile, subtitleFile, outputFullPath string, crf, preset string) error {
	// 确保输出目录存在
	//ffmpeg -i file.mp4 -vf subtitles=merge.ass -c:v libx264 -b:v 2000000 -preset slow -c:a copy -y
	threadCore := "4"
	if config.GetConfig().MergeThreadCore > 0 {
		threadCore = strconv.FormatInt(int64(config.GetConfig().MergeThreadCore), 10)
	}
	//var cmd *exec.Cmd
	//ffmpeg -i xxx.mp4 -threads 8 -vf subtitles=merge.ass -c:v libx264 -crf 21 -preset ultrafast -c:a copy -y ultrafast.mp4
	sarFmt := ""
	// if sar != "1:1" {
	// 	vec := strings.Split(sar, ":")
	// 	if len(vec) != 2 {
	// 		return fmt.Errorf("invalid sar format: %s", sar)
	// 	}
	// 	sarFmt = "h264_metadata=sample_aspect_ratio=" + vec[0] + "/" + vec[1]
	// }

	var cmd *exec.Cmd

	//ffmpeg -i xxx.mp4 -threads 8 -c:v libx264 -crf 21 -preset ultrafast -vf subtitles=merge.ass -c:a copy -y ultrafast.mp4
	vf := fmt.Sprintf("subtitles=%s", subtitleFile)
	if len(sarFmt) > 3 {
		cmd = exec.Command("ffmpeg",
			"-i", videoFile,
			"-threads", threadCore,
			"-vf", vf,
			"-c:v", "libx264",
			"-crf", crf,
			"-preset", preset,
			"-bsf:v", sarFmt,
			"-c:a", "copy",
			"-y", outputFullPath)

		cmdStr := strings.Join(cmd.Args, " ")
		g.Log().Infof(context.TODO(), "合成视频, cmdStr: %s", cmdStr)
	} else {
		cmd = exec.Command("ffmpeg",
			"-i", videoFile,
			"-threads", threadCore,
			"-vf", vf,
			"-c:v", "libx264",
			"-crf", crf,
			"-preset", preset,
			"-c:a", "copy",
			"-y", outputFullPath)

		cmdStr := strings.Join(cmd.Args, " ")
		g.Log().Infof(context.TODO(), "合成视频, taskId: %v, cmdStr: %s", taskId, cmdStr)
	}
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to merge subtitles, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func Transform2Mp3(inputFile, outputFile string) error {
	//ffmpeg -i xxx.mp4 -c:a libmp3lame -q:a 4 -y xxx.mp3
	cmd := exec.Command("ffmpeg",
		"-i", inputFile,
		"-c:a", "libmp3lame",
		"-b:a", "320k",
		"-ar", "44100",
		"-y", outputFile)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(context.TODO(), "转换音频, cmdStr: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to Transform2Mp3, error: %v, output: %s", err, stdoutStderr)
	}
	return nil
}

func CombineVideoWithWaterMark(taskId int64, videoFile, outputFullPath, crf, preset string) error {
	threadCore := "4"
	if config.GetConfig().MergeThreadCore > 0 {
		threadCore = strconv.FormatInt(int64(config.GetConfig().MergeThreadCore), 10)
	}

	// [1:v] 表示第二个输入文件（水印图片）的视频流。
	// scale=iw*0.5:-1 将水印图片的宽度缩放到原始宽度的 50%，高度按比例缩放。
	// [wm] 是缩放后的水印图片的标签。
	// [0:v] 表示第一个输入文件（主视频）的视频流。

	avInfo, err := GetAudioVideoInfo(videoFile)
	if err != nil {
		return fmt.Errorf("合成水印时获取媒体信息失败, err: %s", err.Error())
	}

	// sarFmt := "h264_metadata=sample_aspect_ratio=1/1"
	// if sar != "" && sar != "1:1" {
	// 	vec := strings.Split(sar, ":")
	// 	if len(vec) == 2 {
	// 		sarFmt = "h264_metadata=sample_aspect_ratio=" + vec[0] + "/" + vec[1]
	// 	}
	// }
	var winfo *WaterMarkInfo
	var pngCoef float32 = 0.6
	if avInfo.VideoInfo.Width > avInfo.VideoInfo.Height {
		winfo = &wWaterFormat
		pngCoef = 0.3
	} else {
		winfo = &hWaterFormat
	}
	//coef := float32(avInfo.VideoInfo.Width) / 1.0 / float32(winfo.VideoWidth)
	coef := (float32(avInfo.VideoInfo.Width) * pngCoef) / float32(winfo.PngWidth) //根据水印要显示的宽度，计算缩放比例
	rightMargin := int(avInfo.VideoInfo.Width) - int(float32(winfo.MarginRight)*coef) - int(coef*float32(winfo.PngWidth))
	bottomMargin := int(avInfo.VideoInfo.Height) - int(float32(winfo.MarginBottom)*coef) - int(coef*float32(winfo.PngHeight))
	waterFormat := fmt.Sprintf("[1:v]scale=iw*%v:-1[wm];[0:v][wm]overlay=%v:%v", coef, rightMargin, bottomMargin)
	cmd := exec.Command("ffmpeg",
		"-i", videoFile,
		"-i", winfo.PngPath,
		"-threads", threadCore,
		"-filter_complex", waterFormat,
		"-c:v", "libx264",
		"-crf", crf,
		//"-bsf:v", sarFmt,
		"-preset", preset,
		"-c:a", "copy",
		"-y", outputFullPath)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(context.TODO(), "为视频添加水印, taskid: %v, cmdStr: %s", taskId, cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to CombineVideoWithWaterMark, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func TransferVideoResolutionWithPreset(videoFile, outputFullPath, resolutionStr string, crf, preset string, targetFps int, ffmpegThreadNum int) error {
	threadCore := "4"
	if ffmpegThreadNum > 0 {
		threadCore = strconv.FormatInt(int64(ffmpegThreadNum), 10)
	}

	targetFpsStr := "30"
	if targetFps > 0 {
		targetFpsStr = strconv.FormatInt(int64(targetFps), 10)
	}
	//ffmpeg -i xxx.mp4 -threads -s 720*1080 -c:v libx264 -crf 21 -preset ultrafast -c:a copy -y ultrafast.mp4
	cmd := exec.Command("ffmpeg",
		"-i", videoFile,
		"-threads", threadCore,
		"-s", resolutionStr,
		"-c:v", "libx264",
		"-r", targetFpsStr,
		"-crf", crf,
		"-preset", preset,
		"-c:a", "copy",
		"-y", outputFullPath)

	cmdStr := strings.Join(cmd.Args, " ")
	g.Log().Infof(context.TODO(), "转换视频分辨率, cmdStr: %s", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to TransferVideoResolutionWithPreset, error: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

func SplitVideo(inputFile, beginTimeStr, endTimeStr, outputFile string) (string, error) {
	avInfo, err := GetAudioVideoInfo(inputFile)
	if err != nil {
		return "", err
	}
	cmdStr := ""
	if avInfo.VideoInfo.BitRate > 0 {
		cmd := exec.Command(FfmpegPath+"ffmpeg",
			"-i", inputFile,
			"-ss", beginTimeStr,
			"-to", endTimeStr,
			"-c:v", "libx264",
			"-b:v", strconv.FormatInt(int64(avInfo.VideoInfo.BitRate), 10),
			"-c:a", "copy",
			"-y",
			outputFile)
		cmdStr = strings.Join(cmd.Args, " ")
		_, err = cmd.CombinedOutput()
	} else {
		cmd := exec.Command(FfmpegPath+"ffmpeg",
			"-i", inputFile,
			"-ss", beginTimeStr,
			"-to", endTimeStr,
			"-c:v", "libx264",
			"-c:a", "copy",
			"-y",
			outputFile)
		cmdStr = strings.Join(cmd.Args, " ")
		_, err = cmd.CombinedOutput()
	}
	return cmdStr, err
}

func QuickSplitVideo(inputFile, outputPath string, segmentDuration int) error {
	// ffmpeg -i input.mp4 -c copy -map 0 -segment_time 5 -f segment -reset_timestamps 1 output_%03d.mp4
	// 命令解释：
	// -i input.mp4：指定输入文件。
	// -c copy：表示不重新编码，直接复制视频和音频流，这样处理速度会更快，并保持原有质量。如果想重新编码，可以将 copy 替换为需要的编码格式（例如 -c:v libx264）。
	// -map 0：指定要复制所有的流（视频、音频、字幕等）。
	// -segment_time 5：表示每 5 秒分割一个片段，但不会严格按照 5 秒分割，在关键帧（I 帧）处分割。
	// -f segment：指定使用 segment 格式进行分割。
	// -reset_timestamps 1：每个输出文件的时间戳从零开始。
	// output_%03d.mp4：指定输出文件的命名格式，%03d 表示输出文件会按编号命名，例如 output_000.mp4，output_001.mp4 等。
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-c", "copy",
		"-map", "0",
		"-segment_time", strconv.Itoa(segmentDuration),
		"-f", "segment",
		"-reset_timestamps", "1",
		outputPath)
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Println("QuickSplitVideo", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to split video segment: %v\noutput: %s, mp4File: %s", err, stdoutStderr, inputFile)
	}
	return nil
}

func EraseTmcdData(inputFile, outputFile string) error {
	//ffmpeg -i v7.mp4 -c copy -write_tmcd false -y xx.mp4
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-c", "copy",
		"-write_tmcd", "false",
		"-preset", "fast",
		"-y", outputFile)
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Println("EraseTmcdData", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to EraseTmcdData video, err: %s, output: %s, mp4File: %s", err.Error(), stdoutStderr, inputFile)
	}
	return nil
}

func SaveAudioAndVideo(inputFile, outputFile string) error {
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-c:v", "copy",
		"-c:a", "aac",
		"-preset", "fast",
		"-y", outputFile)
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Println("SaveAudioAndVideo", cmdStr)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to SaveAudioAndVideo video, err: %s, output: %s, mp4File: %s", err.Error(), stdoutStderr, inputFile)
	}
	return nil
}

func SplitVideoWithCallback(ctx any, inputFile string, outputPath string, outputPrefix, appUserId string, subtitleId int64,
	videoDuration, segmentDuration int, startIdx int,
	callback func(ctx any, segmentDir, segmentFileName string, segmentNumber int, totalNumber int, elapsed time.Duration) int,
	getFileNameCb func(outputPath, appUserId string, subtitleId int64, segNum int, prefix string) (string, string)) (int, error) {
	if err := common.EnsureOutputDirectory(outputPath); err != nil {
		return 0, err
	}

	// avInfo, err := GetAudioVideoInfo(inputFile)
	// if err != nil {
	// 	return 0, err
	// }
	numSegments := int(videoDuration) / segmentDuration
	if int(videoDuration)%segmentDuration != 0 {
		numSegments++
	}

	avInfo, err := GetAudioVideoInfo(inputFile)
	if err != nil {
		return 0, err
	}
	for i := startIdx; i < numSegments; i++ {
		start := time.Now()

		segmentStart := i * segmentDuration
		//segmentFileName := fmt.Sprintf("%s/%s%03d.mp4", outputPath, outputPrefix, i)
		segmentFileName, segmentDir := getFileNameCb(outputPath, appUserId, subtitleId, i, outputPrefix)
		if len(segmentFileName) < 3 {
			return 0, fmt.Errorf("segmentFileName error, name: %s, outputPath: %s", segmentFileName, outputPath)
		}
		//ffmpeg -i xxx.mp4 -ss 5 -t 10 -c:v libx264 -c:a copy -y output.mp4
		if avInfo.VideoInfo.BitRate > 0 {
			cmd := exec.Command(FfmpegPath+"ffmpeg",
				"-i", inputFile,
				"-ss", strconv.Itoa(segmentStart),
				"-t", strconv.Itoa(segmentDuration),
				"-c:v", "libx264",
				"-b:v", strconv.FormatInt(int64(avInfo.VideoInfo.BitRate), 10),
				"-c:a", "copy",
				"-y", // 添加 -y 参数以覆盖已存在的文件
				segmentFileName)
			stdoutStderr, err := cmd.CombinedOutput()
			if err != nil {
				return i, fmt.Errorf("failed to split video segment: %v\noutput: %s, mp4File: %s", err, stdoutStderr, segmentFileName)
			}
		} else {
			cmd := exec.Command(FfmpegPath+"ffmpeg",
				"-i", inputFile,
				"-ss", strconv.Itoa(segmentStart),
				"-t", strconv.Itoa(segmentDuration),
				"-c:v", "libx264",
				"-c:a", "copy",
				"-y", // 添加 -y 参数以覆盖已存在的文件
				segmentFileName)
			stdoutStderr, err := cmd.CombinedOutput()
			if err != nil {
				return i, fmt.Errorf("failed to split video segment: %v\noutput: %s, mp4File: %s", err, stdoutStderr, segmentFileName)
			}
		}
		elapsed := time.Since(start)
		sret := 0
		if callback != nil {
			sret = callback(ctx, segmentDir, segmentFileName, i, numSegments, elapsed)
			if sret != 0 {
				//不要再处理下去了
				return i, fmt.Errorf("callback return failed, retCode: %d", sret)
			}
		}
	}

	return numSegments, nil
}

func MergeVideosWithFileList(inputListFile string, outputFile string) error {
	// 创建ffmpeg命令
	//ffmpeg -f concat -safe 0 -i concat.txt -c copy -y output.mp4
	cmd := exec.Command(FfmpegPath+"ffmpeg", "-f", "concat",
		"-safe", "0",
		"-i", inputListFile,
		"-c", "copy",
		"-y", outputFile)

	// 打印命令以便调试
	fmt.Println("Running command:", strings.Join(cmd.Args, " "))

	// 执行命令
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to merge videos: %v, output: %s", err.Error(), stdoutStderr)
	}

	return nil
}

func MergeVideosWithFileListFast(inputListFile string, outputFile string) error {
	//ffmpeg -f concat -safe 0 -i concat.txt -c copy -y long_merge_.mp4
	cmd := exec.Command(FfmpegPath+"ffmpeg", "-f", "concat",
		"-safe", "0",
		"-i", inputListFile,
		"-c", "copy",
		"-y", outputFile)

	// 打印命令以便调试
	fmt.Println("Running command:", strings.Join(cmd.Args, " "))

	// 执行命令
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to merge videos: %v, output: %s", err.Error(), stdoutStderr)
	}

	return nil
}

// getAudioParams 获取视频文件中的音频参数
func getAudioParams(inputFile string) (sampleRate, bitRate, channels string, err error) {
	cmd := exec.Command(FfmpegPath+"ffmpeg", "-i", inputFile)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr
	if err := cmd.Run(); err != nil {
		output := stderr.String()
		// 使用正则表达式从输出中提取音频参数信息
		re := regexp.MustCompile(`Audio: .+ (\d+) Hz, (\w+), .+ (\d+) kb/s`)
		matches := re.FindStringSubmatch(output)
		if len(matches) >= 4 {
			sampleRate = matches[1]
			channels = matches[2]
			bitRate = matches[3] + "k"
		} else {
			return "", "", "", fmt.Errorf("failed to get audio params")
		}
	}
	return sampleRate, bitRate, channels, nil
}

// channelsToNumber 将声道描述转换为对应的数字
func channelsToNumber(channels string) string {
	switch channels {
	case "stereo":
		return "2"
	case "mono":
		return "1"
	default:
		return "2" // 默认返回立体声
	}
}

func ExtractAsrAudio(inputFile string, outputFullPath string) (time.Duration, error) {
	start := time.Now()

	// 获取原视频的音频参数
	//_, _, channels, err := getAudioParams(inputFile)
	//numChannels := channelsToNumber(channels)
	// if err != nil {
	// 	avInfo, err := GetAudioVideoInfo(inputFile)
	// 	if err != nil {
	// 		return 0, fmt.Errorf("failed to get audio params: %v", err.Error())
	// 	} else {
	// 		numChannels = strconv.Itoa(avInfo.AudioInfo.Channels)
	// 	}
	// }

	// ffmpeg -i xxx.mp4 -vn -c:a libmp3lame -ar 16000 -ac numChannels -y yyyy.mp3
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-vn",
		"-c:a", "libmp3lame",
		"-ar", "16000",
		"-ac", "1",
		"-y",
		outputFullPath)

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to extract audio: %v\noutput: %s", err, stdoutStderr)
		return 0, errors.New(s)
	}

	elapsed := time.Since(start)
	return elapsed, nil
}

func ExtractAudio2mp3(inputFile string, outputFullPath string) error {

	// ffmpeg -i xxx.mp4 -vn -c:a libmp3lame -y yyyy.mp3
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-vn",
		"-write_tmcd", "false",
		"-c:a", "libmp3lame",
		"-y",
		outputFullPath)

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to extract audio: %v\noutput: %s", err, stdoutStderr)
		return errors.New(s)
	}

	return nil
}

func ExtractAudio2mp3With(inputFile string, outputFullPath string, beginTimeStr, endTimeStr string) error {

	// ffmpeg -i xxx.mp4 -vn -c:a libmp3lame -y yyyy.mp3
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-vn",
		"-write_tmcd", "false",
		"-ss", beginTimeStr,
		"-to", endTimeStr,
		"-c:a", "libmp3lame",
		"-y",
		outputFullPath)

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to extract audio: %v\noutput: %s", err, stdoutStderr)
		return errors.New(s)
	}

	return nil
}

// 优先不重新编码输出音频
func ExtractAudio(inputFile string, outputFullPath string) (time.Duration, string, error) {
	start := time.Now()

	avInfo, err := GetAudioVideoInfo(inputFile)
	if err != nil {
		return 0, "", err
	}
	if avInfo.AudioInfo.CodecName == "aac" {
		realOutputName := outputFullPath + ".aac"
		cmd := exec.Command(FfmpegPath+"ffmpeg",
			"-i", inputFile,
			"-vn",
			"-write_tmcd", "false",
			"-acodec", "copy",
			"-y",
			realOutputName)
		stdoutStderr, err := cmd.CombinedOutput()
		if err != nil {
			s := fmt.Sprintf("failed to extract audio: %v\noutput: %s", err, stdoutStderr)
			return 0, "", errors.New(s)
		}

		elapsed := time.Since(start)
		return elapsed, realOutputName, nil
	}

	sampleRate := ""
	bitRate := ""
	numChannels := ""
	if avInfo.AudioInfo.SampleRate > 0 {
		sampleRate = strconv.Itoa(avInfo.AudioInfo.SampleRate)
	} else {
		return 0, "", fmt.Errorf("音频采样率不正确, file: %s, rate: %v", inputFile, avInfo.AudioInfo.SampleRate)
	}
	//
	if avInfo.AudioInfo.BitRate > 0 {
		bitRate = strconv.Itoa(avInfo.AudioInfo.BitRate)
	} else {
		bitRate = "128000"
	}
	//
	if avInfo.AudioInfo.Channels > 0 {
		numChannels = strconv.Itoa(avInfo.AudioInfo.Channels)
	} else {
		numChannels = "2"
	}
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-vn",
		"-c:a", "libmp3lame",
		"-ar", sampleRate,
		"-b:a", bitRate,
		"-ac", numChannels,
		"-y",
		outputFullPath)

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to extract audio: %v\noutput: %s", err, stdoutStderr)
		return 0, "", errors.New(s)
	}

	elapsed := time.Since(start)
	return elapsed, outputFullPath, nil
}

func MergeAudioAndVideo(audioInput, videoInput, videoOutput string) error {
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", audioInput,
		"-i", videoInput,
		"-c:v", "copy",
		"-c:a", "copy",
		"-y",
		videoOutput)
	//"-c:a", "libmp3lame",
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to merge audio and video: %v\noutput: %s", err, stdoutStderr)
		return errors.New(s)
	}
	return nil
}

func MergeAudioAndVideoWithAac(audioInput, videoInput, videoOutput string) error {
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", audioInput,
		"-i", videoInput,
		"-c:v", "copy",
		"-c:a", "aac",
		"-y",
		videoOutput)
	//"-c:a", "libmp3lame",
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		s := fmt.Sprintf("failed to merge audio and video: %v\noutput: %s", err, stdoutStderr)
		return errors.New(s)
	}
	return nil
}

// 视频去除片头片尾
// headDuration:片头秒，tailDuration:片尾秒,videoDuration：视频秒
func EscapeVideo(inputPath string, outputPath string, headDuration, tailDuration, videoDuration int) (err error) {
	//if err := common.EnsureOutputDirectory(outputPath); err != nil {
	//	return err
	//}
	var splitDuration = videoDuration - tailDuration - headDuration
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputPath,
		"-ss", strconv.Itoa(headDuration),
		"-t", strconv.Itoa(splitDuration),
		"-c:v", "libx264",
		"-c:a", "copy",
		"-y", // 添加 -y 参数以覆盖已存在的文件
		outputPath)
	fmt.Println("Running command:", strings.Join(cmd.Args, " "))
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to escape video err: %v\noutput: %s, mp4File: %s", err, stdoutStderr, outputPath)
	}

	return nil
}

// 转换时间基
func TransformVideoTimeBase(intputFile, outputFile string, timeBase, preset string) error {
	//ffmpeg -i xxx mp4 -time_base 1/16000 -preset ultrafast -crf 21 -fps_mode -y aa.mp4
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", intputFile,
		"-time_base", timeBase,
		"-preset", preset,
		"-crf", "21",
		"-vsync", "2",
		"-y",
		outputFile)
	fmt.Println("Running command:", strings.Join(cmd.Args, " "))
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to TransformVideoTimeBase video err: %v, input: %s, output: %s, mp4File: %s",
			err, intputFile, stdoutStderr, outputFile)
	}

	return nil
}

func RemoveAudioFromVideo(inputFile, outputFile string) error {
	//ffmpeg -i xxx.mp4 -an -write_tmcd false -vcodec copy bbb.mp4
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-an",
		"-write_tmcd", "false",
		"-vcodec", "copy",
		"-y",
		outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("Error running ffmpeg command: %v\nOutput: %s\n", err, output)
		return err
	}
	return nil
}

// func CombineAudioAndVideo(videoFile, audioFile, outputFile string) error {
// 	// 构建 ffmpeg 命令
// 	cmd := exec.Command(FfmpegPath+"ffmpeg",
// 		"-i", videoFile,
// 		"-i", audioFile,
// 		"-c:v", "copy",
// 		"-c:a", "copy", "-strict", "experimental", "-shortest", outputFile)
// 	// 执行命令
// 	output, err := cmd.CombinedOutput()
// 	if err != nil {
// 		fmt.Printf("Error running ffmpeg command: %v\nOutput: %s\n", err, output)
// 		return err
// 	}

// 	return nil
// }

// RemoveVideoStream 保留音频流，剔除视频流，输出为 mp4 格式
func RemoveVideoStream(inputFile, outputFile string) error {
	// ffmpeg 命令：剔除视频流，仅保留音频流
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile, // 输入文件
		"-vn",          // 不处理视频流
		"-c:a", "copy", // 不重新编码音频，直接复制
		"-y",       // 覆盖输出文件
		outputFile, // 输出文件
	)

	// 打印命令以便调试
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Printf("Running command: %s\n", cmdStr)

	// 执行命令
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to remove video stream: %v\noutput: %s", err, stdoutStderr)
	}

	return nil
}

func ExtractAudioSegment(inputFile, outputFile, startTime, endTime string) error {
	startTime = strings.Replace(startTime, ",", ".", 1)
	endTime = strings.Replace(endTime, ",", ".", 1)
	// ffmpeg 命令：截取音频片段
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile, // 输入文件
		"-ss", startTime, // 起始时间
		"-to", endTime, // 结束时间
		"-vn",                // 不处理视频流
		"-c:a", "libmp3lame", // 使用 MP3 编码器
		"-y",       // 覆盖输出文件
		outputFile, // 输出文件
	)

	// 打印命令以便调试
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Printf("Running command: %s\n", cmdStr)

	// 执行命令
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to extract audio segment: %v\noutput: %s\ncmd:%v", err, stdoutStderr, cmdStr)
	}

	return nil
}

// Append10sBlankAudio 通用：在原音频后拼接10秒静音音频，输出新音频文件（支持mp3/aac/wav等常见格式）
func Append10sBlankAudio(inputAudio, outputAudio string) error {
	// 1. 获取原音频参数（采样率、声道），如获取失败则用默认值
	ar := "44100"
	ac := "2"
	ext := strings.ToLower(filepath.Ext(outputAudio))
	acodec := "aac"
	if ext == ".mp3" {
		acodec = "libmp3lame"
	} else if ext == ".wav" {
		acodec = "pcm_s16le"
	}

	// 2. 生成10秒静音音频
	blankAudio := fmt.Sprintf("blank_10s_%s%s", uuid.New().String(), ext)
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-f", "lavfi",
		"-i", fmt.Sprintf("anullsrc=channel_layout=stereo:sample_rate=%s", ar),
		"-t", "10",
		"-c:a", acodec,
		"-ac", ac,
		"-ar", ar,
		"-y", blankAudio,
	)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to generate blank audio: %v, output: %s", err, out)
	}
	defer os.Remove(blankAudio)

	// 3. 写入concat列表
	concatList := fmt.Sprintf("concat_audio_%s.txt", uuid.New().String())
	content := fmt.Sprintf("file '%s'\nfile '%s'\n", inputAudio, blankAudio)
	if err := os.WriteFile(concatList, []byte(content), 0644); err != nil {
		return fmt.Errorf("failed to write concat list: %w", err)
	}
	defer os.Remove(concatList)

	// 4. 拼接音频
	cmd = exec.Command(FfmpegPath+"ffmpeg", "-f", "concat", "-safe", "0", "-i", concatList, "-c", "copy", "-y", outputAudio)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to concat audio: %v, output: %s", err, out)
	}

	return nil
}

// Append10sBlankAudioAndToVideo 先在原音频后拼接10秒静音音频，再转为视频（黑色视频，时长与音频一致）
func Append10sBlankAudioAndToVideo(inputAudio, outputVideo string) error {
	// 1. 拼接10秒静音音频
	mergedAudio := fmt.Sprintf("merged_audio_%s%s", uuid.New().String(), filepath.Ext(inputAudio))
	if err := Append10sBlankAudio(inputAudio, mergedAudio); err != nil {
		return fmt.Errorf("Append10sBlankAudio failed: %w", err)
	}
	defer os.Remove(mergedAudio)

	// 2. 获取拼接后音频时长
	audioDuration, err := GetVideoDuration(mergedAudio)
	if err != nil {
		return fmt.Errorf("failed to get merged audio duration: %v", err)
	}

	// 3. 生成黑色视频（分辨率、帧率可自定义或用默认值）
	width, height, fps := 1280, 720, 25 // 可根据需要调整
	blankVideo := fmt.Sprintf("blank_%ds_%s.mp4", audioDuration, uuid.New().String())
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-f", "lavfi",
		"-i", fmt.Sprintf("color=c=black:s=%dx%d:r=%d", width, height, fps),
		"-t", fmt.Sprintf("%d", audioDuration),
		"-c:v", "libx264",
		"-pix_fmt", "yuv420p",
		"-y", blankVideo,
	)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to generate blank video: %v, output: %s", err, out)
	}
	defer os.Remove(blankVideo)

	// 4. 合成新视频（黑色视频+拼接后的音频）
	cmd = exec.Command(FfmpegPath+"ffmpeg",
		"-i", blankVideo,
		"-i", mergedAudio,
		"-c:v", "copy",
		"-c:a", "aac",
		"-shortest",
		"-y", outputVideo,
	)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to merge blank video and audio: %v, output: %s", err, out)
	}

	return nil
}
func ExtractMp3AndAppendBlankAudioToVideo(inputVideo, outputVideo string) error {
	// 1. 提取 mp3 音频
	mp3File := fmt.Sprintf("audio_%s.mp3", uuid.New().String())
	err := ExtractAudio2mp3(inputVideo, mp3File)
	if err != nil {
		return fmt.Errorf("failed to extract mp3: %w", err)
	}
	defer os.Remove(mp3File)

	// 2. 拼接10秒静音音频并转为黑色视频
	err = Append10sBlankAudioAndToVideo(mp3File, outputVideo)
	if err != nil {
		return fmt.Errorf("Append10sBlankAudioAndToVideo failed: %w", err)
	}

	return nil
}

// TrimLast10Seconds 删除音频或视频文件最后10秒，输出新文件
func TrimLast10Seconds(inputFile, outputFile string) error {
	// 1. 获取总时长（秒）
	duration, err := GetVideoDuration(inputFile)
	if err != nil {
		return fmt.Errorf("failed to get duration: %w", err)
	}
	if duration <= 10 {
		return fmt.Errorf("file duration <= 10s, cannot trim last 10 seconds")
	}

	// 2. 保留前 duration-10 秒
	trimDuration := duration - 10
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-i", inputFile,
		"-t", fmt.Sprintf("%d", trimDuration),
		"-c", "copy",
		"-y", outputFile,
	)
	cmdStr := strings.Join(cmd.Args, " ")
	fmt.Println("TrimLast10Seconds:", cmdStr)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to trim last 10 seconds: %v, output: %s", err, out)
	}
	return nil
}

// Mp3ToMp4 将mp3音频转为mp4视频（黑色视频，时长与音频一致）
func Mp3ToMp4(inputMp3, outputMp4 string) error {
	// 1. 获取音频时长
	duration, err := GetVideoDuration(inputMp3)
	if err != nil {
		return fmt.Errorf("failed to get mp3 duration: %w", err)
	}

	// 2. 生成黑色视频
	width, height, fps := 1280, 720, 25 // 可根据需要调整
	blankVideo := fmt.Sprintf("blank_%ds_%s.mp4", duration, uuid.New().String())
	cmd := exec.Command(FfmpegPath+"ffmpeg",
		"-f", "lavfi",
		"-i", fmt.Sprintf("color=c=black:s=%dx%d:r=%d", width, height, fps),
		"-t", fmt.Sprintf("%d", duration),
		"-c:v", "libx264",
		"-pix_fmt", "yuv420p",
		"-y", blankVideo,
	)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to generate blank video: %v, output: %s", err, out)
	}
	defer os.Remove(blankVideo)

	// 3. 合成黑色视频和mp3为mp4
	cmd = exec.Command(FfmpegPath+"ffmpeg",
		"-i", blankVideo,
		"-i", inputMp3,
		"-c:v", "copy",
		"-c:a", "aac",
		"-shortest",
		"-y", outputMp4,
	)
	if out, err := cmd.CombinedOutput(); err != nil {
		return fmt.Errorf("failed to merge mp3 and video: %v, output: %s", err, out)
	}

	return nil
}
