package id_generator

import (
	"context"
	"encoding/binary"
	"fmt"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/id_generator"

	"github.com/google/uuid"
)

var (
	idGenerator *id_generator.IDGenerator
)

func InitIdGenerator(ctx context.Context, addr string) error {

	idGenerator = id_generator.InitIDGenerator(ctx, addr)
	if idGenerator == nil {
		return fmt.Errorf("failed to initialize id generator")
	}

	return nil
}

func GenerateId() (int64, error) {
	if idGenerator == nil {
		return 0, fmt.Errorf("id generator is not initialized")
	}
	uuid, err := idGenerator.GenerateID()
	if err != nil {
		return 0, fmt.Errorf("failed to generate id: %v", err)
	}

	return int64(uuid), nil
}

func GenerateIdNotStrict() int64 {
	_uuid := uuid.New()
	randNum := int64(binary.BigEndian.Uint64(_uuid[:8]))
	if randNum < 0 {
		randNum = -randNum
	}
	return randNum
}
