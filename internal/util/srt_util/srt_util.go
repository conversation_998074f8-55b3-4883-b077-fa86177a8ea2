package srt_util

import (
	"fmt"
	"math"
	"strconv"
	"strings"
)

func SrtStringTime2MsDot(str string) (int64, error) {
	nstr := strings.Replace(str, ".", ",", 1)
	return SrtStringTime2Ms(nstr)
}

func SrtStringTime2MsFloat(str string) (float64, error) {
	ms, err := SrtStringTime2Ms(str)
	if err != nil {
		return 0, err
	}
	return float64(ms) / 1e3, nil
}

func SrtTimeMs2String(t int64) string {
	hour := t / (60 * 60 * 1000)
	t = t - hour*(60*60*1000)

	minute := t / (60 * 1000)
	t = t - minute*(60*1000)

	second := t / 1000
	t = t - second*1000

	mill := t

	return fmt.Sprintf("%02d:%02d:%02d,%03d", hour, minute, second, mill)
}
func SrtStringTime2Ms(str string) (int64, error) {
	vecs := strings.Split(str, ",")
	if len(vecs) != 2 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	heads := strings.Split(vecs[0], ":")
	if len(heads) != 3 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	hour, err := timeString2Int(heads[0])
	if err != nil {
		return 0, err
	}
	minute, err := timeString2Int(heads[1])
	if err != nil {
		return 0, err
	}
	second, err := timeString2Int(heads[2])
	if err != nil {
		return 0, err
	}
	mill, err := timeString2Int(vecs[1])
	if err != nil {
		return 0, err
	}
	if hour < 0 || minute < 0 || second < 0 || mill < 0 {
		return 0, fmt.Errorf("format error: %s", str)
	}
	return hour*60*60*1000 + minute*60*1000 + second*1000 + mill, nil
}
func SrtTimeMs2StringAss(t int64) string {
	hour := t / (60 * 60 * 1000)
	t = t % (60 * 60 * 1000)

	minute := t / (60 * 1000)
	t = t % (60 * 1000)

	second := t / 1000
	t = t % 1000

	// 直接截断到 10ms 精度
	mill := t / 10 // ASS 用 1/100 秒，所以直接取整

	return fmt.Sprintf("%02d:%02d:%02d.%02d", hour, minute, second, mill)
}
func timeString2Int(str string) (int64, error) {
	num, err := strconv.ParseInt(str, 10, 64)
	return num, err
}

func SrtTimeMs2StringDot(t int64) string {
	hour := t / (60 * 60 * 1000)
	t = t - hour*(60*60*1000)

	minute := t / (60 * 1000)
	t = t - minute*(60*1000)

	second := t / 1000
	t = t - second*1000

	mill := t

	return fmt.Sprintf("%02d:%02d:%02d.%03d", hour, minute, second, mill)
}

// 毫秒到逗号字幕时间
func Ms2CommaStringTime(t int64) string {
	return strings.Replace(SrtTimeMs2StringDot(t), ",", ".", 1)
}

// SRTTimeStrToFloatSeconds 将 00:00:03,217 格式时间转为秒级浮点时间戳
func SRTTimeStrToFloatSeconds(srtTime string) (float64, error) {
	parts := strings.Split(srtTime, ":")
	if len(parts) != 3 {
		return 0, fmt.Errorf("invalid format")
	}
	h, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, err
	}
	m, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, err
	}
	secParts := strings.Split(parts[2], ",")
	if len(secParts) != 2 {
		return 0, fmt.Errorf("invalid format")
	}
	s, err := strconv.Atoi(secParts[0])
	if err != nil {
		return 0, err
	}
	ms, err := strconv.Atoi(secParts[1])
	if err != nil {
		return 0, err
	}
	return float64(h*3600+m*60+s) + float64(ms)/1000.0, nil
}

// FloatSecondsToSRTTimeStr 将秒级浮点时间戳转为 00:00:03,217 格式
func FloatSecondsToSRTTimeStr(seconds float64) string {
	h := int(seconds) / 3600
	m := (int(seconds) % 3600) / 60
	s := int(seconds) % 60
	ms := int(math.Round((seconds - float64(int(seconds))) * 1000))
	return fmt.Sprintf("%02d:%02d:%02d,%03d", h, m, s, ms)
}

// SplitOriginByTextMiddleTime 根据 before/after 字符串长度比例，返回拆分的时间戳
func SplitOriginByTextMiddleTime(before, after string, startTime, endTime float64) (middleStart float64) {
	if startTime >= endTime {
		return 0
	}
	beforeLen := len([]rune(before))
	afterLen := len([]rune(after))
	totalLen := beforeLen + afterLen

	duration := endTime - startTime
	middleStart = startTime + duration*float64(beforeLen)/float64(totalLen)
	return
}
