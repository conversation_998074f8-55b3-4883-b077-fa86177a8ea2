package obs

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/consts"
	"context"
	"fmt"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/oss_util"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

var (
	obsOnce sync.Once

	obsClient *utils.ObjectStorage
	vendorMap = map[string]utils.ObjecetStorageType{
		"huawei":  utils.StorageTypeObs,
		"ali":     utils.StorageTypeOss,
		"tencent": utils.StorageTypeCos,
	}
)

func GetOsClient() *utils.ObjectStorage {
	obsOnce.Do(func() {
		// cfg := config.GetConfig()
		vendor, exists := vendorMap[config.GetConfig().Obs.Vendor]
		if !exists {
			panic(fmt.Sprintf("unsupport vendor:%s", config.GetConfig().Obs.Vendor))
		}
		var err error
		obsClient, err = utils.NewObjectStorage(&utils.StorageParameter{
			AccessKey:                config.GetConfig().Obs.AccessKey,
			SecretKey:                config.GetConfig().Obs.SecretKey,
			CosUrl:                   config.GetConfig().Obs.CosUrl,
			CdnName:                  config.GetConfig().Obs.CdnName,
			BucketName:               config.GetConfig().Obs.BucketName,
			EndPoint:                 config.GetConfig().Obs.EndPoint,
			PublicEndPoint:           config.GetConfig().Obs.PublicEndPoint,
			OssArn:                   "xxxxxxx",
			PushObjectCacheAccessKey: config.GetConfig().Obs.PushObjectCacheAccessKey,
			PushObjectCacheSecretKey: config.GetConfig().Obs.PushObjectCacheSecretKey,
		}, vendor)
		if err != nil {
			panic(err)
		}
	})

	return obsClient
}

func PushObjectsCache(ctx context.Context, fileUrls []string) {
	concurrent.GoSafe(func() {
		taskId, reqId, err := GetOsClient().PushObjectCache(fileUrls)
		g.Log().Infof(ctx, "[PushObjectCache] Pushed objects to cache, taskId: %s, reqId: %s, fileUrls: %s, err: %+v", taskId, reqId, strings.Join(fileUrls, ","), err)
		if err != nil {
			g.Log().Errorf(ctx, "[PushObjectCache] Failed to push objects to cache: %v", err)
		}
	})
}

// GetCdnUrlByObjectName 通过对象名称返回CDN地址，如果已经是完整URL则直接返回
func GetCdnUrlByObjectName(objectName string) string {
	// 如果已经是完整的URL，直接返回
	if strings.HasPrefix(objectName, "http://") || strings.HasPrefix(objectName, "https://") {
		return objectName
	}

	// 如果是对象名称，构建CDN URL
	return fmt.Sprintf("https://%s/%s", config.GetConfig().Obs.CdnName, objectName)
}

// GetObjectNameByHttpsUrl 通过 HTTP/HTTPS URL 获取对象名称
// 支持 http 和 https 协议，自动提取路径作为对象名称
func GetObjectNameByHttpsUrl(urlStr string) (string, error) {
	// 输入验证
	if urlStr == "" {
		return "", fmt.Errorf("URL cannot be empty")
	}

	// 解析URL
	parsedUrl, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL '%s': %w", urlStr, err)
	}

	// 验证协议
	if parsedUrl.Scheme != "http" && parsedUrl.Scheme != "https" {
		return "", fmt.Errorf("unsupported protocol '%s', only http and https are supported", parsedUrl.Scheme)
	}

	// 验证主机名
	if parsedUrl.Host == "" {
		return "", fmt.Errorf("URL must contain a valid host")
	}

	// 获取路径并去掉开头的斜杠
	objectName := strings.TrimPrefix(parsedUrl.Path, "/")
	if objectName == "" {
		return "", fmt.Errorf("URL path is empty, cannot extract object name from '%s'", urlStr)
	}

	return objectName, nil
}

func ReplaceUrlToCdn(remoteUrl string) string {
	// 如果已经是CDN地址，直接返回
	if strings.Contains(remoteUrl, config.GetConfig().Obs.CdnName) {
		return remoteUrl
	}

	// 解析URL
	parsedUrl, err := url.Parse(remoteUrl)
	if err != nil {
		return remoteUrl // 解析失败返回原URL
	}

	// 构建新的CDN URL，保留查询参数
	cdnUrl := fmt.Sprintf("https://%s%s", config.GetConfig().Obs.CdnName, parsedUrl.Path)
	if parsedUrl.RawQuery != "" {
		cdnUrl += "?" + parsedUrl.RawQuery
	}
	return cdnUrl
}

func ReplaceUrlToPublicEndpoint(remoteUrl string) string {
	// 如果已经是CDN地址，直接返回
	if strings.Contains(remoteUrl, config.GetConfig().Obs.PublicEndPoint) {
		return remoteUrl
	}

	// 解析URL
	parsedUrl, err := url.Parse(remoteUrl)
	if err != nil {
		return remoteUrl // 解析失败返回原URL
	}

	// 构建新的CDN URL
	return fmt.Sprintf("https://%s%s", config.GetConfig().Obs.PublicEndPoint, parsedUrl.Path)
}

func GeneratePublicOBSUrl(module consts.ObsMode, group, ext string) string {
	return GeneratePublicOBSUrlPubOrPri(module, group, ext, "public")
}

func GeneratePublicOBSUrlPubOrPri(module consts.ObsMode, group, ext, pubOrPri string) string {
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)
	cfg := config.GetConfig()
	// allvoice/{环境}/public/{模块名}/{group}/{年月日}/{文件名}.{文件后缀}
	return fmt.Sprintf("%s/%s/%s/%s/%s/%s/%s", cfg.Obs.ObjectDir, cfg.Env, pubOrPri, module, group, time.Now().Format("20060102"), fileName)
}

func PreSignUrl(cdnUrl string, expires int32) (string, error) {
	// 判断是否是 http 开头
	if !strings.HasPrefix(cdnUrl, "http://") && !strings.HasPrefix(cdnUrl, "https://") {
		cdnUrl = GetCdnUrlByObjectName(cdnUrl)
	}
	httpsUrl, err := GetObjectNameByHttpsUrl(cdnUrl)
	if err != nil {
		return "", err
	}
	if expires <= 0 {
		expires = 3600
	}
	return GetOsClient().Presign(httpsUrl, oss_util.WithExpires(expires))
}
