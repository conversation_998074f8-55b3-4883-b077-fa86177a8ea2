package ffmpeg

import (
	"fmt"
	"os"
	"os/exec"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 测试辅助函数：创建临时测试视频文件
func createTestVideoFile(filename string, hasAudio bool) error {
	// 创建一个简单的测试视频文件
	var cmd *exec.Cmd
	if hasAudio {
		// 创建带音频的测试视频（1秒，黑色画面，440Hz音频）
		cmd = exec.Command("ffmpeg",
			"-f", "lavfi",
			"-i", "color=c=black:s=320x240:d=1",
			"-f", "lavfi",
			"-i", "sine=frequency=440:duration=1",
			"-c:v", "libx264",
			"-c:a", "aac",
			"-shortest",
			"-y", filename)
	} else {
		// 创建无音频的测试视频（1秒，黑色画面）
		cmd = exec.Command("ffmpeg",
			"-f", "lavfi",
			"-i", "color=c=black:s=320x240:d=1",
			"-c:v", "libx264",
			"-y", filename)
	}

	return cmd.Run()
}

// 测试辅助函数：创建临时测试音频文件
func createTestAudioFile(filename string, durationSec float64) error {
	// 创建一个简单的测试音频文件（440Hz正弦波）
	cmd := exec.Command("ffmpeg",
		"-f", "lavfi",
		"-i", fmt.Sprintf("sine=frequency=440:duration=%.3f", durationSec),
		"-c:a", "aac",
		"-y", filename)

	return cmd.Run()
}

// 测试辅助函数：检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// 测试辅助函数：清理测试文件
func cleanupTestFiles(files ...string) {
	for _, file := range files {
		os.Remove(file)
	}
}

func TestMergeVideosByConcat(t *testing.T) {
	Convey("MergeVideosByConcat 函数测试", t, func() {

		Convey("边界情况测试", func() {

			Convey("空输入文件列表", func() {
				output := "empty_output.mp4"
				defer cleanupTestFiles(output)

				// 执行测试
				inputFiles := []string{}
				err := MergeVideosByConcat(inputFiles, output)

				// 验证结果
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "输入文件列表为空")
				So(fileExists(output), ShouldBeFalse)
			})
		})

		Convey("不存在的输入文件", func() {
			output := "nonexistent_output.mp4"
			defer cleanupTestFiles(output)

			// 执行测试
			inputFiles := []string{"nonexistent_file.mp4"}
			err := MergeVideosByConcat(inputFiles, output)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(fileExists(output), ShouldBeFalse)
		})
	})

	Convey("错误处理测试", func() {

		Convey("输出文件路径无效", func() {
			// 准备测试文件
			video := "test_error_video.mp4"
			invalidOutput := "/invalid/path/that/does/not/exist/output.mp4"

			// 创建测试视频文件
			err := createTestVideoFile(video, true)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video)

			// 执行测试
			inputFiles := []string{video}
			err = MergeVideosByConcat(inputFiles, invalidOutput)

			// 验证结果
			So(err, ShouldNotBeNil)
			So(fileExists(invalidOutput), ShouldBeFalse)
		})
	})

}

// TestHasAudioStream 测试音频流检测函数
func TestHasAudioStream(t *testing.T) {
	Convey("HasAudioStream 函数测试", t, func() {

		Convey("检测有音频的视频文件", func() {
			video := "test_has_audio.mp4"

			// 创建带音频的测试视频文件
			err := createTestVideoFile(video, true)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video)

			// 执行测试
			hasAudio, err := HasAudioStream(video)

			// 验证结果
			So(err, ShouldBeNil)
			So(hasAudio, ShouldBeTrue)
		})

		Convey("检测无音频的视频文件", func() {
			video := "test_no_audio.mp4"

			// 创建无音频的测试视频文件
			err := createTestVideoFile(video, false)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video)

			// 执行测试
			hasAudio, err := HasAudioStream(video)

			// 验证结果
			So(err, ShouldBeNil)
			So(hasAudio, ShouldBeFalse)
		})

		Convey("检测不存在的文件", func() {
			// 执行测试
			hasAudio, err := HasAudioStream("nonexistent_file.mp4")

			// 验证结果 - 函数设计为不存在文件时返回false, nil
			So(err, ShouldBeNil)
			So(hasAudio, ShouldBeFalse)
		})
	})
}

// TestProbeVideoInfo 测试视频信息探测函数
func TestProbeVideoInfo(t *testing.T) {
	Convey("ProbeVideoInfo 函数测试", t, func() {

		Convey("探测有音频视频文件的信息", func() {
			video := "test_probe_with_audio.mp4"

			// 创建带音频的测试视频文件
			err := createTestVideoFile(video, true)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video)

			// 执行测试
			info, err := ProbeVideoInfo(video)

			// 验证结果
			So(err, ShouldBeNil)
			So(info, ShouldNotBeNil)
			So(info.Width, ShouldEqual, 320)
			So(info.Height, ShouldEqual, 240)
			So(info.HasAudio, ShouldBeTrue)
			So(info.DurationSec, ShouldBeGreaterThan, 0)
			So(info.InputFile, ShouldEqual, video)
		})

		Convey("探测无音频视频文件的信息", func() {
			video := "test_probe_no_audio.mp4"

			// 创建无音频的测试视频文件
			err := createTestVideoFile(video, false)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video)

			// 执行测试
			info, err := ProbeVideoInfo(video)

			// 验证结果
			So(err, ShouldBeNil)
			So(info, ShouldNotBeNil)
			So(info.Width, ShouldEqual, 320)
			So(info.Height, ShouldEqual, 240)
			So(info.HasAudio, ShouldBeFalse)
			So(info.DurationSec, ShouldBeGreaterThan, 0)
			So(info.InputFile, ShouldEqual, video)
		})

		Convey("探测不存在文件的信息", func() {
			// 执行测试
			info, err := ProbeVideoInfo("nonexistent_file.mp4")

			// 验证结果
			So(err, ShouldNotBeNil)
			So(info, ShouldBeNil)
		})
	})
}

// TestProcessAudioDuration 测试音频时长处理函数
func TestProcessAudioDuration(t *testing.T) {
	Convey("ProcessAudioDuration 函数测试", t, func() {

		Convey("音频时长等于目标时长（直接复制）", func() {
			inputAudio := "test_audio_equal.aac"
			outputAudio := "test_output_equal.aac"
			targetDuration := 3.0

			// 创建3秒的测试音频文件
			err := createTestAudioFile(inputAudio, targetDuration)
			if err != nil {
				SkipConvey("跳过测试：无法创建测试音频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(inputAudio, outputAudio)

			// 执行测试
			err = ProcessAudioDuration(inputAudio, targetDuration, outputAudio)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputAudio), ShouldBeTrue)

			// 验证输出文件时长
			duration, err := ProbeDuration(outputAudio)
			So(err, ShouldBeNil)
			So(duration, ShouldAlmostEqual, targetDuration, 0.2) // 允许0.2秒误差
		})

		Convey("音频时长大于目标时长（需要裁剪）", func() {
			inputAudio := "test_audio_trim.aac"
			outputAudio := "test_output_trim.aac"
			originalDuration := 5.0
			targetDuration := 3.0

			// 创建5秒的测试音频文件
			err := createTestAudioFile(inputAudio, originalDuration)
			if err != nil {
				SkipConvey("跳过测试：无法创建测试音频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(inputAudio, outputAudio)

			// 执行测试
			err = ProcessAudioDuration(inputAudio, targetDuration, outputAudio)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputAudio), ShouldBeTrue)

			// 验证输出文件时长应该被裁剪到目标时长
			duration, err := ProbeDuration(outputAudio)
			So(err, ShouldBeNil)
			So(duration, ShouldAlmostEqual, targetDuration, 0.2) // 允许0.2秒误差
			So(duration, ShouldBeLessThan, originalDuration)
		})

		Convey("音频时长小于目标时长（需要循环）", func() {
			inputAudio := "test_audio_loop.aac"
			outputAudio := "test_output_loop.aac"
			originalDuration := 2.0
			targetDuration := 7.0

			// 创建2秒的测试音频文件
			err := createTestAudioFile(inputAudio, originalDuration)
			if err != nil {
				SkipConvey("跳过测试：无法创建测试音频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(inputAudio, outputAudio)

			// 执行测试
			err = ProcessAudioDuration(inputAudio, targetDuration, outputAudio)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputAudio), ShouldBeTrue)

			// 验证输出文件时长应该被扩展到目标时长
			duration, err := ProbeDuration(outputAudio)
			So(err, ShouldBeNil)
			So(duration, ShouldAlmostEqual, targetDuration, 0.2) // 允许0.2秒误差
			So(duration, ShouldBeGreaterThan, originalDuration)
		})

		Convey("使用真实音频文件测试", func() {
			realAudioFile := "/Users/<USER>/Music/吉森信-ふるさとの匂い (故乡的味道).mp3"
			outputAudio := "test_real_audio_output.mp3"
			targetDuration := 30.0 // 30秒

			// 检查真实音频文件是否存在
			if !fileExists(realAudioFile) {
				SkipConvey("跳过测试：真实音频文件不存在")
				return
			}

			defer cleanupTestFiles(outputAudio)

			// 获取原始音频时长
			originalDuration, err := ProbeDuration(realAudioFile)
			if err != nil {
				SkipConvey("跳过测试：无法获取原始音频时长")
				return
			}

			// 执行测试
			err = ProcessAudioDuration(realAudioFile, targetDuration, outputAudio)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputAudio), ShouldBeTrue)

			// 验证输出文件时长
			duration, err := ProbeDuration(outputAudio)
			So(err, ShouldBeNil)
			So(duration, ShouldAlmostEqual, targetDuration, 0.5) // 允许0.5秒误差

			// 打印一些调试信息
			fmt.Printf("原始时长: %.2f秒, 目标时长: %.2f秒, 输出时长: %.2f秒\n",
				originalDuration, targetDuration, duration)

			// 根据原始时长判断处理类型
			if originalDuration > targetDuration {
				fmt.Println("处理类型: 裁剪")
			} else if originalDuration < targetDuration {
				fmt.Println("处理类型: 循环")
			} else {
				fmt.Println("处理类型: 复制")
			}
		})

		Convey("错误处理测试", func() {
			Convey("输入文件不存在", func() {
				nonexistentFile := "nonexistent_audio.mp3"
				outputAudio := "test_error_output.mp3"
				targetDuration := 10.0

				defer cleanupTestFiles(outputAudio)

				// 执行测试
				err := ProcessAudioDuration(nonexistentFile, targetDuration, outputAudio)

				// 验证结果
				So(err, ShouldNotBeNil)
				So(fileExists(outputAudio), ShouldBeFalse)
			})

			Convey("无效的目标时长", func() {
				inputAudio := "test_audio_invalid_duration.aac"
				outputAudio := "test_output_invalid_duration.aac"
				invalidDuration := -1.0

				// 创建测试音频文件
				err := createTestAudioFile(inputAudio, 3.0)
				if err != nil {
					SkipConvey("跳过测试：无法创建测试音频文件")
					return
				}

				defer cleanupTestFiles(inputAudio, outputAudio)

				// 执行测试
				err = ProcessAudioDuration(inputAudio, invalidDuration, outputAudio)

				// 验证结果 - 应该处理负数时长的情况
				So(err, ShouldNotBeNil)
				So(fileExists(outputAudio), ShouldBeFalse)
			})
		})
	})
}

// 基准测试
func BenchmarkMergeVideosByConcat(b *testing.B) {
	// 准备测试文件
	video1 := "bench_video1.mp4"
	video2 := "bench_video2.mp4"

	// 创建测试视频文件
	err1 := createTestVideoFile(video1, true)
	err2 := createTestVideoFile(video2, true)

	if err1 != nil || err2 != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video1, video2)

	inputFiles := []string{video1, video2}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		output := fmt.Sprintf("bench_output_%d.mp4", i)
		err := MergeVideosByConcat(inputFiles, output)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
		os.Remove(output) // 清理输出文件
	}
}

func BenchmarkHasAudioStream(b *testing.B) {
	// 准备测试文件
	video := "bench_audio_test.mp4"

	// 创建测试视频文件
	err := createTestVideoFile(video, true)

	if err != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := HasAudioStream(video)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
	}
}

// 集成测试示例
func TestMergeVideosByConcatIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	Convey("MergeVideosByConcat 集成测试", t, func() {

		Convey("大文件合并测试", func() {
			// 这个测试需要更长的视频文件来测试实际场景
			video1 := "integration_video1.mp4"
			video2 := "integration_video2.mp4"
			output := "integration_output.mp4"

			// 创建较长的测试视频文件（5秒）
			cmd1 := exec.Command("ffmpeg",
				"-f", "lavfi",
				"-i", "color=c=red:s=640x480:d=5",
				"-f", "lavfi",
				"-i", "sine=frequency=440:duration=5",
				"-c:v", "libx264",
				"-c:a", "aac",
				"-shortest",
				"-y", video1)

			cmd2 := exec.Command("ffmpeg",
				"-f", "lavfi",
				"-i", "color=c=blue:s=640x480:d=5",
				"-f", "lavfi",
				"-i", "sine=frequency=880:duration=5",
				"-c:v", "libx264",
				"-c:a", "aac",
				"-shortest",
				"-y", video2)

			err1 := cmd1.Run()
			err2 := cmd2.Run()

			if err1 != nil || err2 != nil {
				SkipConvey("跳过集成测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video1, video2, output)

			// 执行合并
			inputFiles := []string{video1, video2}
			err := MergeVideosByConcat(inputFiles, output)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(output), ShouldBeTrue)

			// 验证输出文件的时长应该约为10秒（两个5秒视频的合并）
			duration, err := ProbeDuration(output)
			So(err, ShouldBeNil)
			So(duration, ShouldBeGreaterThan, 9.0) // 允许一些误差
			So(duration, ShouldBeLessThan, 11.0)   // 允许一些误差
		})
	})
}

// TestExtractNonBlackFrame 测试提取非黑帧封面函数
func TestExtractNonBlackFrame(t *testing.T) {
	Convey("ExtractNonBlackFrame 函数测试", t, func() {

		Convey("使用真实视频文件提取非黑帧", func() {
			videoUrl := "https://cdn-allvoice-down-cn-testing.funnycp.com/allvoice/test/public/video_commentary/merge/20250829/4a3ac6a1-6772-488f-919a-49ff93e2b9e6.mp4"
			outputImage := "test_non_black_frame.jpg"

			defer cleanupTestFiles(outputImage)

			// 执行测试
			err := ExtractNonBlackFrame(videoUrl, outputImage)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputImage), ShouldBeTrue)

			// 验证输出文件是有效的图片文件（检查文件大小）
			stat, err := os.Stat(outputImage)
			So(err, ShouldBeNil)
			So(stat.Size(), ShouldBeGreaterThan, 0)
		})

		Convey("使用本地测试视频文件", func() {
			video := "test_extract_frame.mp4"
			outputImage := "test_local_frame.jpg"

			// 创建带音频的测试视频文件
			err := createTestVideoFile(video, true)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件，可能缺少ffmpeg")
				return
			}

			defer cleanupTestFiles(video, outputImage)

			// 执行测试
			err = ExtractNonBlackFrame(video, outputImage)

			// 验证结果
			So(err, ShouldBeNil)
			So(fileExists(outputImage), ShouldBeTrue)

			// 验证输出文件是有效的图片文件
			stat, err := os.Stat(outputImage)
			So(err, ShouldBeNil)
			So(stat.Size(), ShouldBeGreaterThan, 0)
		})

		Convey("错误处理测试", func() {

			Convey("输入视频文件不存在", func() {
				nonexistentVideo := "nonexistent_video.mp4"
				outputImage := "test_error_frame.jpg"

				defer cleanupTestFiles(outputImage)

				// 执行测试
				err := ExtractNonBlackFrame(nonexistentVideo, outputImage)

				// 验证结果
				So(err, ShouldNotBeNil)
				So(fileExists(outputImage), ShouldBeFalse)
			})

			Convey("输出路径无效", func() {
				video := "test_invalid_output.mp4"
				invalidOutput := "/invalid/path/that/does/not/exist/frame.jpg"

				// 创建测试视频文件
				err := createTestVideoFile(video, true)

				// 如果无法创建测试文件，跳过测试
				if err != nil {
					SkipConvey("跳过测试：无法创建测试视频文件")
					return
				}

				defer cleanupTestFiles(video)

				// 执行测试
				err = ExtractNonBlackFrame(video, invalidOutput)

				// 验证结果
				So(err, ShouldNotBeNil)
				So(fileExists(invalidOutput), ShouldBeFalse)
			})
		})

		Convey("不同输出格式测试", func() {
			video := "test_format_video.mp4"

			// 创建测试视频文件
			err := createTestVideoFile(video, true)

			// 如果无法创建测试文件，跳过测试
			if err != nil {
				SkipConvey("跳过测试：无法创建测试视频文件")
				return
			}

			defer cleanupTestFiles(video)

			testFormats := []string{".jpg", ".png", ".bmp"}

			for _, format := range testFormats {
				Convey(fmt.Sprintf("输出格式 %s", format), func() {
					outputImage := fmt.Sprintf("test_frame%s", format)
					defer cleanupTestFiles(outputImage)

					// 执行测试
					err := ExtractNonBlackFrame(video, outputImage)

					// 验证结果
					So(err, ShouldBeNil)
					So(fileExists(outputImage), ShouldBeTrue)

					// 验证文件大小
					stat, err := os.Stat(outputImage)
					So(err, ShouldBeNil)
					So(stat.Size(), ShouldBeGreaterThan, 0)
				})
			}
		})
	})
}

// BenchmarkExtractNonBlackFrame 基准测试
func BenchmarkExtractNonBlackFrame(b *testing.B) {
	// 准备测试文件
	video := "bench_extract_frame.mp4"

	// 创建测试视频文件
	err := createTestVideoFile(video, true)

	if err != nil {
		b.Skip("跳过基准测试：无法创建测试视频文件，可能缺少ffmpeg")
	}

	defer cleanupTestFiles(video)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		output := fmt.Sprintf("bench_frame_%d.jpg", i)
		err := ExtractNonBlackFrame(video, output)
		if err != nil {
			b.Fatalf("基准测试失败: %v", err)
		}
		os.Remove(output) // 清理输出文件
	}
}
