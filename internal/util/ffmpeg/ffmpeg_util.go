package ffmpeg

import (
	"business-workflow/internal/common/config"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/common"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
)

type VideoInfo struct {
	Width       int
	Height      int
	VideoCodec  string
	AudioCodec  string
	HasAudio    bool
	DurationSec float64
	InputFile   string
}

// AudioInfo 描述音频文件的关键信息
type AudioInfo struct {
	SampleRate  int     // 采样率（Hz）
	Channels    int     // 声道数
	AudioCodec  string  // 编码格式，如 "aac"、"mp3"、"pcm_s16le"
	BitRateKbps int     // 比特率（kbps，可选）
	DurationSec float64 // 时长（秒）
	InputFile   string  // 源文件路径
}

func ProbeVideoInfo(inputFile string) (*VideoInfo, error) {
	width, height, err := probeResolution(inputFile)
	if err != nil {
		return nil, err
	}

	videoCodec, err := probeCodec(inputFile, "v:0")
	if err != nil {
		return nil, err
	}

	audioCodec, err := probeCodec(inputFile, "a:0")
	if err != nil {
		audioCodec = "" // 没有音频流
	}

	hasAudio, _ := HasAudioStream(inputFile)

	duration, err := ProbeDuration(inputFile)
	if err != nil {
		return nil, err
	}

	return &VideoInfo{
		Width:       width,
		Height:      height,
		VideoCodec:  videoCodec,
		AudioCodec:  audioCodec,
		HasAudio:    hasAudio,
		DurationSec: duration,
		InputFile:   inputFile,
	}, nil
}

// ProbeAudioInfo 利用 ffprobe 获取音频文件信息
func ProbeAudioInfo(inputFile string) (*AudioInfo, error) {
	// 编码格式
	audioCodec, err := probeCodec(inputFile, "a:0")
	if err != nil {
		return nil, fmt.Errorf("probe codec: %w", err)
	}

	// 采样率
	sampleRate, err := probeSampleRate(inputFile)
	if err != nil {
		return nil, fmt.Errorf("probe sample rate: %w", err)
	}

	// 声道数
	channels, err := probeChannels(inputFile)
	if err != nil {
		return nil, fmt.Errorf("probe channels: %w", err)
	}

	// 时长
	duration, err := ProbeDuration(inputFile)
	if err != nil {
		return nil, fmt.Errorf("probe duration: %w", err)
	}

	// 比特率（可选）
	bitRateKbps, _ := probeBitRateKbps(inputFile) // 若失败可忽略

	return &AudioInfo{
		SampleRate:  sampleRate,
		Channels:    channels,
		AudioCodec:  audioCodec,
		BitRateKbps: bitRateKbps,
		DurationSec: duration,
		InputFile:   inputFile,
	}, nil
}

// 采样率
func probeSampleRate(file string) (int, error) {
	out, err := exec.Command("ffprobe", "-v", "error",
		"-select_streams", "a:0",
		"-show_entries", "stream=sample_rate",
		"-of", "default=noprint_wrappers=1:nokey=1",
		file).Output()
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(strings.TrimSpace(string(out)))
}

// 声道数
func probeChannels(file string) (int, error) {
	out, err := exec.Command("ffprobe", "-v", "error",
		"-select_streams", "a:0",
		"-show_entries", "stream=channels",
		"-of", "default=noprint_wrappers=1:nokey=1",
		file).Output()
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(strings.TrimSpace(string(out)))
}

// 比特率（可选）
func probeBitRateKbps(file string) (int, error) {
	out, err := exec.Command("ffprobe", "-v", "error",
		"-select_streams", "a:0",
		"-show_entries", "format=bit_rate",
		"-of", "default=noprint_wrappers=1:nokey=1",
		file).Output()
	if err != nil {
		return 0, err
	}
	bps, err := strconv.Atoi(strings.TrimSpace(string(out)))
	if err != nil || bps == 0 {
		return 0, err
	}
	return bps / 1000, nil
}

func ProbeDuration(inputFile string) (float64, error) {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=nw=1:nk=1",
		inputFile,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return 0, err
	}

	return strconv.ParseFloat(strings.TrimSpace(out.String()), 64)
}

func getFileSizeMB(filePath string) (float64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	sizeBytes := info.Size()
	return float64(sizeBytes) / (1024.0 * 1024.0), nil
}

type ProbeResolutionInfo struct {
	Streams []struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	} `json:"streams"`
}

func probeResolution(inputFile string) (int, int, error) {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height",
		"-of", "json",
		inputFile,
	)
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return 0, 0, err
	}
	res := &ProbeResolutionInfo{}
	err = json.Unmarshal(out.Bytes(), res)
	if err != nil {
		return 0, 0, err
	}
	if res.Streams == nil || len(res.Streams) == 0 {
		return 0, 0, fmt.Errorf("no video stream found in %s", inputFile)
	}

	width := res.Streams[0].Width
	height := res.Streams[0].Height
	return width, height, nil
}

func probeCodec(inputFile, streamSelector string) (string, error) {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-select_streams", streamSelector,
		"-show_entries", "stream=codec_name",
		"-of", "default=nw=1:nk=1",
		inputFile,
	)
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(out.String()), nil
}

func HasAudioStream(inputFile string) (bool, error) {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-select_streams", "a:0",
		"-show_entries", "stream=codec_type",
		"-of", "default=nw=1:nk=1",
		inputFile,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return false, nil // 没有音频也算正常
	}

	output := strings.TrimSpace(out.String())
	if output == "audio" {
		return true, nil
	}
	return false, nil
}

// MergeVideosByConcat 采用拼接方式合并视频(前提 视频分辨率 宽高比一致)
func MergeVideosByConcat(inputFiles []string, outputFile string) error {
	// 判断是否存在音频 没有加一个 静音音频 保障能合成成功

	if len(inputFiles) == 0 {
		return fmt.Errorf("输入文件列表为空")
	}
	// 判断 outputFile 所在目录是否存在创建
	if err := common.EnsureOutputDirectory(filepath.Dir(outputFile)); err != nil {
		return err
	}

	var processedFiles []string
	var tempFiles []string

	// 逐个检查文件，给没有音频的添加静音音频
	for i, file := range inputFiles {
		hasAudio, _ := HasAudioStream(file)
		if hasAudio {
			// 有音频，直接使用原文件
			processedFiles = append(processedFiles, file)
		} else {
			// 没有音频，创建带静音音频的临时文件
			tempFile := fmt.Sprintf("%s/temp_with_audio_%d_%s.mp4", config.GetConfig().LocalPath, i, uuid.New().String())
			tempFiles = append(tempFiles, tempFile)

			cmd := exec.Command("ffmpeg",
				"-i", file,
				"-f", "lavfi",
				"-i", "anullsrc=channel_layout=stereo:sample_rate=44100",
				"-c:v", "copy",
				"-c:a", "aac",
				"-shortest",
				"-y", tempFile)

			if err := cmd.Run(); err != nil {
				// 清理已创建的临时文件
				for _, tf := range tempFiles {
					os.Remove(tf)
				}
				return fmt.Errorf("添加静音音频失败: %v", err)
			}

			processedFiles = append(processedFiles, tempFile)
		}
	}

	// 清理临时文件
	defer func() {
		for _, tf := range tempFiles {
			os.Remove(tf)
		}
	}()

	// 创建文件列表
	listFile := fmt.Sprintf("concat_list_%s.txt", uuid.New().String())
	defer os.Remove(listFile)

	var content strings.Builder
	for _, file := range processedFiles {
		content.WriteString(fmt.Sprintf("file '%s'\n", file))
	}

	err := os.WriteFile(listFile, []byte(content.String()), 0644)
	if err != nil {
		return fmt.Errorf("创建文件列表失败: %w", err)
	}

	// 现在所有文件都有音频了，直接合并
	cmd := exec.Command("ffmpeg", "-f", "concat",
		"-safe", "0",
		"-i", listFile,
		"-c", "copy",
		"-y", outputFile)
	g.Log().Infof(context.Background(), "MergeVideosByConcat, cmd: %v", cmd.Args)
	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("合并视频失败: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

// CutVideo 裁剪视频，保留音频
// inputPath: 原视频路径
// startTime: 开始时间 (格式 "00:00:10.111")
// endTime:   结束时间 (格式 "00:00:20.123")
// outputPath: 输出视频路径
func CutVideo(inputPath, startTime, endTime, outputPath string) error {
	if err := common.EnsureOutputDirectory(filepath.Dir(outputPath)); err != nil {
		return err
	}
	// ffmpeg 命令
	cmd := exec.Command(
		"ffmpeg",
		"-y",
		"-i", inputPath,
		"-ss", startTime,
		"-to", endTime,
		"-c:v", "libx264", // 重新编码视频，保证关键帧正常
		"-c:a", "copy", // 音频直接拷贝
		outputPath,
	)
	g.Log().Infof(context.Background(), "CutVideo, cmd: %v", cmd.Args)
	// 捕获执行过程中的输出（调试用）
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("ffmpeg 执行失败: %v, 输出: %s", err, string(output))
	}
	return nil
}

// ProcessAudioDuration 处理音频文件的时长
// inputFile: 输入音频文件路径
// targetDurationSec: 目标时长（秒）
// outputFile: 输出音频文件路径
// 逻辑：
//   - 如果音频时长大于目标时长，则裁剪到目标时长
//   - 如果音频时长小于目标时长，则循环播放到目标时长
//   - 如果音频时长等于目标时长，则直接复制
func ProcessAudioDuration(inputFile string, targetDurationSec float64, outputFile string) error {
	// 验证输入参数
	if targetDurationSec <= 0 {
		return fmt.Errorf("目标时长必须大于0，当前值: %.3f", targetDurationSec)
	}

	// 确保输出目录存在
	if err := common.EnsureOutputDirectory(filepath.Dir(outputFile)); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 获取输入音频的时长
	currentDuration, err := ProbeDuration(inputFile)
	if err != nil {
		return fmt.Errorf("获取音频时长失败: %w", err)
	}

	// 时长差异的容忍度（0.05）
	const tolerance = 0.05

	// 判断当前时长与目标时长的关系
	if abs(currentDuration-targetDurationSec) <= tolerance {
		// 时长相等（在容忍度范围内），直接复制文件
		return copyAudioFile(inputFile, outputFile)
	} else if currentDuration > targetDurationSec {
		// 当前时长大于目标时长，需要裁剪
		return trimAudio(inputFile, targetDurationSec, outputFile)
	} else {
		// 当前时长小于目标时长，需要循环播放
		return loopAudio(inputFile, currentDuration, targetDurationSec, outputFile)
	}
}

// copyAudioFile 复制音频文件
func copyAudioFile(inputFile, outputFile string) error {
	cmd := exec.Command("ffmpeg",
		"-y",            // 覆盖输出文件
		"-i", inputFile, // 输入文件
		"-c", "copy", // 不重新编码
		outputFile,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("复制音频文件失败: %v, 输出: %s", err, string(output))
	}
	return nil
}

// trimAudio 裁剪音频到指定时长
func trimAudio(inputFile string, targetDurationSec float64, outputFile string) error {
	cmd := exec.Command("ffmpeg",
		"-y",            // 覆盖输出文件
		"-i", inputFile, // 输入文件
		"-t", fmt.Sprintf("%.3f", targetDurationSec), // 目标时长
		"-vn",          // 忽略视频流（包括封面图片）
		"-c:a", "copy", // 只复制音频流
		outputFile,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("裁剪音频失败: %v, 输出: %s", err, string(output))
	}
	return nil
}

// loopAudio 循环播放音频到指定时长
func loopAudio(inputFile string, currentDuration, targetDurationSec float64, outputFile string) error {
	// 计算需要循环的次数
	loopCount := int(targetDurationSec/currentDuration) + 1

	// 获取输入文件的扩展名，用于临时文件
	inputExt := filepath.Ext(inputFile)
	if inputExt == "" {
		inputExt = ".mp3" // 如果没有扩展名，使用默认
	}

	// 创建临时的合并音频文件
	tempConcatFile := fmt.Sprintf("%s/temp_concat_%s%s", getTempDir(), uuid.New().String(), inputExt)
	defer os.Remove(tempConcatFile)

	// 先合并多个音频文件
	err := concatAudioFiles(inputFile, loopCount, tempConcatFile)
	if err != nil {
		return fmt.Errorf("合并音频文件失败: %w", err)
	}

	// 再裁剪到目标时长
	return trimAudio(tempConcatFile, targetDurationSec, outputFile)
}

// concatAudioFiles 合并多个相同的音频文件
func concatAudioFiles(inputFile string, repeatCount int, outputFile string) error {
	// 创建文件列表
	listFile := fmt.Sprintf("%s/concat_list_%s.txt", getTempDir(), uuid.New().String())
	defer os.Remove(listFile)

	// 生成文件列表内容，使用绝对路径
	absInputFile, err := filepath.Abs(inputFile)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}

	var content strings.Builder
	for i := 0; i < repeatCount; i++ {
		content.WriteString(fmt.Sprintf("file '%s'\n", absInputFile))
	}

	// 写入文件列表
	err = os.WriteFile(listFile, []byte(content.String()), 0644)
	if err != nil {
		return fmt.Errorf("创建文件列表失败: %w", err)
	}

	// 使用 concat demuxer 合并文件
	cmd := exec.Command("ffmpeg",
		"-y",           // 覆盖输出文件
		"-f", "concat", // 使用concat格式
		"-safe", "0", // 允许不安全的文件名
		"-i", listFile, // 输入文件列表
		"-vn",          // 忽略视频流（包括封面图片）
		"-c:a", "copy", // 只复制音频流
		outputFile,
	)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("合并音频文件失败: %v, 输出: %s", err, string(output))
	}
	return nil
}

// getTempDir 获取临时目录路径
func getTempDir() string {
	cfg := config.GetConfig()
	if cfg != nil && cfg.LocalPath != "" {
		return cfg.LocalPath
	}
	// 如果配置为空，使用系统临时目录
	return os.TempDir()
}

// abs 返回浮点数的绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// MergeVideosByTranscode 直接转码合并视频（一步完成）
func MergeVideosByTranscode(inputFiles []string, outputFile string, width, height int, crf int) error {
	if len(inputFiles) == 0 {
		return fmt.Errorf("输入文件列表为空")
	}

	if err := common.EnsureOutputDirectory(filepath.Dir(outputFile)); err != nil {
		return err
	}

	// 参数校验
	if width <= 0 || height <= 0 {
		return fmt.Errorf("分辨率参数无效，宽和高必须大于0: %d x %d", width, height)
	}
	if crf < 0 || crf > 51 {
		return fmt.Errorf("CRF参数无效，范围0-51: %d", crf)
	}

	// 构建ffmpeg命令
	args := []string{"-y"} // 覆盖输出文件

	// 添加所有输入文件
	for _, file := range inputFiles {
		args = append(args, "-i", file)
	}

	// 构建filter_complex
	var filterParts []string

	// 处理视频流
	for i := 0; i < len(inputFiles); i++ {
		filterParts = append(filterParts, fmt.Sprintf("[%d:v]scale=%d:%d,setsar=1[v%d]", i, width, height, i))
	}

	// 处理音频流
	for i := 0; i < len(inputFiles); i++ {
		filterParts = append(filterParts, fmt.Sprintf("[%d:a]aresample=44100,aformat=sample_fmts=fltp:channel_layouts=stereo[a%d]", i, i))
	}

	// 构建concat输入
	var concatInputs string
	for i := 0; i < len(inputFiles); i++ {
		concatInputs += fmt.Sprintf("[v%d][a%d]", i, i)
	}

	// 合并filter_complex
	filterComplex := strings.Join(filterParts, ";") +
		fmt.Sprintf(";%sconcat=n=%d:v=1:a=1[outv][outa]", concatInputs, len(inputFiles))

	// 添加filter_complex和输出参数
	args = append(args,
		"-filter_complex", filterComplex,
		"-map", "[outv]",
		"-map", "[outa]",
		"-c:v", "libx264",
		"-preset", "ultrafast",
		"-crf", strconv.Itoa(crf),
		"-c:a", "aac",
		"-ar", "44100",
		"-ac", "2",
		outputFile)

	cmd := exec.Command("ffmpeg", args...)
	g.Log().Infof(context.Background(), "MergeVideosByTranscode, cmd: %v", cmd.Args)

	stdoutStderr, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("直接转码合并失败: %v, output: %s", err, stdoutStderr)
	}

	return nil
}

// ExtractNonBlackFrame 提取视频的第一帧作为封面
// 智能选择提取位置：对于长视频从第1秒开始，对于短视频从开头开始
func ExtractNonBlackFrame(videoPath, outputImagePath string) error {

	cmd := exec.Command("ffmpeg",
		"-y", // 覆盖输出
		"-ss", "2",
		"-i", videoPath, // 输入视频
		"-vframes", "1", // 只导出1帧
		"-update", "1", // 允许覆盖单个图片文件
		outputImagePath, // 输出图片
	)

	g.Log().Infof(context.Background(), "ExtractNonBlackFrame args: %v", cmd.Args)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("提取非黑帧封面失败: %v, 输出: %s", err, string(output))
	}
	return nil
}
