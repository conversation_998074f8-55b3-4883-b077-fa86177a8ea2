package font_util

import (
	"testing"
)

// TestExampleUsage 测试示例代码
func TestExampleUsage(t *testing.T) {
	// 这个测试主要是确保示例代码能正常运行
	// 在实际环境中，ExampleUsage() 会打印输出到控制台
	
	// 获取字体管理器
	fontManager := GetFontManager()

	// 测试基本功能
	width, fontPath, err := fontManager.MeasureTextWidth("en", "Arial", 14, "Hello World", false, false)
	if err != nil {
		t.Fatalf("测量文本宽度失败: %v", err)
	}

	if width <= 0 {
		t.Errorf("文本宽度应该大于0，实际: %.2f", width)
	}

	t.Logf("测量成功: %.2f 像素, 字体路径: %s", width, fontPath)

	// 测试粗体
	boldWidth, boldFontPath, err := fontManager.MeasureTextWidth("en", "Arial", 14, "Hello World", true, false)
	if err != nil {
		t.Fatalf("测量粗体文本宽度失败: %v", err)
	}

	if boldWidth <= 0 {
		t.<PERSON><PERSON><PERSON>("粗体文本宽度应该大于0，实际: %.2f", boldWidth)
	}

	// 粗体通常比普通字体宽一些
	if boldWidth <= width {
		t.Logf("注意: 粗体宽度(%.2f)没有比普通字体(%.2f)宽，这可能是正常的", boldWidth, width)
	}

	t.Logf("粗体测量成功: %.2f 像素, 字体路径: %s", boldWidth, boldFontPath)

	// 测试斜体
	italicWidth, italicFontPath, err := fontManager.MeasureTextWidth("en", "Arial", 14, "Hello World", false, true)
	if err != nil {
		t.Fatalf("测量斜体文本宽度失败: %v", err)
	}

	if italicWidth <= 0 {
		t.Errorf("斜体文本宽度应该大于0，实际: %.2f", italicWidth)
	}

	t.Logf("斜体测量成功: %.2f 像素, 字体路径: %s", italicWidth, italicFontPath)

	// 测试中文
	chineseWidth, chineseFontPath, err := fontManager.MeasureTextWidth("zh", "PingFang SC", 16, "你好世界", false, false)
	if err != nil {
		t.Fatalf("测量中文文本宽度失败: %v", err)
	}

	if chineseWidth <= 0 {
		t.Errorf("中文文本宽度应该大于0，实际: %.2f", chineseWidth)
	}

	t.Logf("中文测量成功: %.2f 像素, 字体路径: %s", chineseWidth, chineseFontPath)
}

// TestNewAPIFeatures 测试新API的特性
func TestNewAPIFeatures(t *testing.T) {
	fontManager := GetFontManager()

	// 测试不存在的字体（应该回退到默认字体）
	width, fontPath, err := fontManager.MeasureTextWidth("en", "NonExistentFont", 14, "Test", false, false)
	if err != nil {
		t.Fatalf("即使字体不存在，也应该能够回退到默认字体: %v", err)
	}

	if width <= 0 {
		t.Errorf("回退字体的宽度应该大于0，实际: %.2f", width)
	}

	t.Logf("字体回退功能正常: %.2f 像素, 实际字体路径: %s", width, fontPath)

	// 测试缓存功能
	// 第一次调用
	width1, fontPath1, err1 := fontManager.MeasureTextWidth("en", "Arial", 12, "Cache Test", false, false)
	if err1 != nil {
		t.Fatalf("第一次调用失败: %v", err1)
	}

	// 第二次调用（应该使用缓存）
	width2, fontPath2, err2 := fontManager.MeasureTextWidth("en", "Arial", 12, "Cache Test", false, false)
	if err2 != nil {
		t.Fatalf("第二次调用失败: %v", err2)
	}

	// 宽度应该完全相同
	if width1 != width2 {
		t.Errorf("缓存功能异常，两次调用结果不同: %.2f vs %.2f", width1, width2)
	}

	// 字体路径也应该相同
	if fontPath1 != fontPath2 {
		t.Errorf("缓存功能异常，两次调用字体路径不同: %s vs %s", fontPath1, fontPath2)
	}

	t.Logf("缓存功能正常: %.2f 像素, 字体路径: %s", width1, fontPath1)

	// 测试不同样式的缓存
	normalWidth, normalPath, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Style Test", false, false)
	boldWidth, boldPath, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Style Test", true, false)
	italicWidth, italicPath, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Style Test", false, true)
	boldItalicWidth, boldItalicPath, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Style Test", true, true)
	
	t.Logf("样式测试结果:")
	t.Logf("  普通: %.2f 像素, 字体: %s", normalWidth, normalPath)
	t.Logf("  粗体: %.2f 像素, 字体: %s", boldWidth, boldPath)
	t.Logf("  斜体: %.2f 像素, 字体: %s", italicWidth, italicPath)
	t.Logf("  粗斜体: %.2f 像素, 字体: %s", boldItalicWidth, boldItalicPath)
	
	// 所有宽度都应该大于0
	if normalWidth <= 0 || boldWidth <= 0 || italicWidth <= 0 || boldItalicWidth <= 0 {
		t.Errorf("所有样式的宽度都应该大于0")
	}
}
