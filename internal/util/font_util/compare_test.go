package font_util

import (
	"fmt"
	"os"
	"testing"

	"github.com/go-text/typesetting/di"
	"github.com/go-text/typesetting/language"
	"github.com/go-text/typesetting/shaping"
	"golang.org/x/image/math/fixed"
)

// measureTextWidthWhole 整体测量文本宽度（原来的方法）
func measureTextWidthWhole(fm *FontManager, lang, fontName string, fontSize float64, text string) (float64, error) {
	if fontName == "" {
		return 0, fmt.Errorf("字体名称不能为空")
	}
	if fontSize <= 0 {
		return 0, fmt.Errorf("字体大小必须大于0，当前值: %f", fontSize)
	}
	if text == "" {
		return 0, nil
	}

	face, _, err := fm.getFontByNameAndStyle(fontName, false, false)
	if err != nil {
		return 0, err
	}

	// 整体文本的 shaping 输入
	textRunes := []rune(text)
	input := shaping.Input{
		Text:      textRunes,
		RunStart:  0,
		RunEnd:    len(textRunes),
		Direction: di.DirectionLTR,
		Face:      face,
		Size:      fixed.I(int(fontSize)),
		Script:    fm.getScript(lang),
		Language:  language.NewLanguage(lang),
	}

	// 执行整体文本整形
	shaper := &shaping.HarfbuzzShaper{}
	output := shaper.Shape(input)

	// 计算总宽度
	var totalWidth float64
	for _, glyph := range output.Glyphs {
		totalWidth += float64(glyph.XAdvance) / 64.0
	}

	return totalWidth, nil
}

// TestMeasureTextWidth_CharByChar_vs_Whole 测试逐字符测量 vs 整体测量
func TestMeasureTextWidth_CharByChar_vs_Whole(t *testing.T) {
	// 查找可用的字体文件
	fontPaths := []string{
		"/Users/<USER>/GolandProjects/video_translate/fonts/HarmonyOS_Sans_SC_Regular.ttf",
		"/System/Library/Fonts/PingFang.ttc",
		"/System/Library/Fonts/Helvetica.ttc",
		"/System/Library/Fonts/Arial.ttf",
	}

	var validFontPath string
	for _, path := range fontPaths {
		if _, err := os.Stat(path); err == nil {
			validFontPath = path
			t.Logf("使用字体: %s", path)
			break
		}
	}

	if validFontPath == "" {
		t.Skip("没有找到可用的字体文件")
		return
	}

	// 获取字体管理器
	fontManager := GetFontManager()

	// 测试用例
	testCases := []struct {
		name     string
		lang     string
		fontSize float64
		text     string
	}{
		// 英文测试
		{"英文单词", "en", 14, "Hello"},
		{"英文句子", "en", 14, "Hello World"},
		{"英文长句", "en", 14, "The quick brown fox jumps over the lazy dog"},

		// 中文测试
		{"中文单字", "zh", 16, "你"},
		{"中文词语", "zh", 16, "你好"},
		{"中文句子", "zh", 16, "你好世界"},
		{"中文长句", "zh", 16, "这是一段中文测试文本"},

		// 日文测试
		{"日文平假名", "ja", 16, "こんにちは"},
		{"日文汉字", "ja", 16, "世界"},
		{"日文混合", "ja", 16, "こんにちは世界"},

		// 韩文测试
		{"韩文", "ko", 16, "안녕하세요"},
		{"韩文长句", "ko", 16, "안녕하세요 세계"},

		// 混合语言测试
		{"中英混合", "zh", 14, "Hello你好"},
		{"中英混合长", "zh", 14, "Hello 你好 World 世界"},
		{"多语言混合", "zh", 14, "Hello こんにちは 你好 안녕하세요"},

		// 特殊字符测试
		{"数字", "en", 12, "1234567890"},
		{"标点符号", "en", 12, "!@#$%^&*()"},
		{"特殊符号", "en", 12, "[]{}|;':\",./<>?"},

		// 不同字号测试
		{"小字号", "en", 8, "Test"},
		{"中字号", "en", 16, "Test"},
		{"大字号", "en", 24, "Test"},
		{"超大字号", "en", 48, "Test"},

		// 空格和特殊字符
		{"单个空格", "en", 14, " "},
		{"多个空格", "en", 14, "   "},
		{"带空格的文本", "en", 14, "Hello World Test"},
		{"制表符", "en", 14, "\t"},

		// Unicode 字符
		{"Emoji", "en", 16, "🌟🎉🚀"},
		{"Unicode符号", "en", 16, "©®™"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 逐字符测量（当前实现）
			charByCharWidth, fontPath, err1 := fontManager.MeasureTextWidth(tc.lang, "Arial", tc.fontSize, tc.text, false, false)
			if err1 != nil {
				t.Fatalf("逐字符测量失败: %v", err1)
			}

			// 整体测量（原来的方法）
			wholeWidth, err2 := measureTextWidthWhole(fontManager, tc.lang, "Arial", tc.fontSize, tc.text)
			if err2 != nil {
				t.Fatalf("整体测量失败: %v", err2)
			}

			// 记录使用的字体路径
			t.Logf("使用字体路径: %s", fontPath)

			// 计算差异
			diff := charByCharWidth - wholeWidth
			diffPercent := 0.0
			if wholeWidth != 0 {
				diffPercent = (diff / wholeWidth) * 100
			}

			// 输出结果
			t.Logf("文本: '%s'", tc.text)
			t.Logf("逐字符: %.2f 像素", charByCharWidth)
			t.Logf("整体测量: %.2f 像素", wholeWidth)
			t.Logf("差异: %.2f 像素 (%.2f%%)", diff, diffPercent)

			// 判断差异是否在可接受范围内
			tolerance := 5.0 // 5% 容差
			if tc.text == "" {
				// 空文本应该完全相等
				if charByCharWidth != wholeWidth {
					t.Errorf("空文本宽度应该相等: 逐字符=%.2f, 整体=%.2f", charByCharWidth, wholeWidth)
				}
			} else if len([]rune(tc.text)) == 1 {
				// 单个字符应该完全相等或非常接近
				if diffPercent > 1.0 {
					t.Errorf("单字符差异过大: %.2f%%", diffPercent)
				}
			} else {
				// 多字符文本允许一定差异（因为连字、字距调整等）
				if diffPercent > tolerance {
					t.Errorf("差异超出容差范围: %.2f%% > %.2f%%", diffPercent, tolerance)
				}
			}

			// 记录是否有显著差异
			if diffPercent > 1.0 {
				t.Logf("⚠️  显著差异: %.2f%%", diffPercent)
			} else {
				t.Logf("✅ 差异很小: %.2f%%", diffPercent)
			}
		})
	}
}

// BenchmarkMeasureTextWidth_CharByChar_vs_Whole 性能对比基准测试
func BenchmarkMeasureTextWidth_CharByChar_vs_Whole(b *testing.B) {
	// 查找可用的字体文件
	fontPaths := []string{
		"/Users/<USER>/GolandProjects/video_translate/fonts/HarmonyOS_Sans_SC_Regular.ttf",
		"/System/Library/Fonts/Helvetica.ttc",
		"/System/Library/Fonts/Arial.ttf",
	}

	var validFontPath string
	for _, path := range fontPaths {
		if _, err := os.Stat(path); err == nil {
			validFontPath = path
			break
		}
	}

	if validFontPath == "" {
		b.Skip("没有找到可用的字体文件")
		return
	}

	// 获取字体管理器
	fontManager := GetFontManager()

	testText := "Hello 你好 World 世界"

	b.Run("CharByChar", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, _, err := fontManager.MeasureTextWidth("zh", "Arial", 14, testText, false, false)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("Whole", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := measureTextWidthWhole(fontManager, "zh", "Arial", 14, testText)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
