package font_util

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"testing"
)

// TestFontManager_MeasureTextWidth 测试 FontManager 的面向对象方法
func TestFontManager_MeasureTextWidth(t *testing.T) {
	// 获取字体管理器单例实例
	fontManager := GetFontManager()

	// 测试用例
	tests := []struct {
		name     string
		lang     string
		fontName string
		fontSize float64
		text     string
		isBold   bool
		isItaly  bool
		wantErr  bool
		checkFn  func(t *testing.T, width float64) // 自定义检查函数
	}{
		{
			name:     "基础英文测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "Hello World",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("英文文本宽度应该大于0，实际: %.2f", width)
				}
				if width > 200 {
					t.<PERSON><PERSON><PERSON>("英文文本宽度异常过大: %.2f", width)
				}
			},
		},
		{
			name:     "基础中文测试",
			lang:     "zh",
			fontName: "PingFang SC",
			fontSize: 16,
			text:     "你好世界",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("中文文本宽度应该大于0，实际: %.2f", width)
				}
				// 中文字符通常比英文字符宽
				if width < 30 {
					t.Errorf("中文文本宽度可能过小: %.2f", width)
				}
			},
		},
		{
			name:     "中英混合文本",
			lang:     "zh",
			fontName: "Arial",
			fontSize: 14,
			text:     "Hello 你好 World 世界",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("混合文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "粗体英文测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "Hello World",
			isBold:   true,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("粗体英文文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "斜体英文测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "Hello World",
			isBold:   false,
			isItaly:  true,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("斜体英文文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "粗斜体英文测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "Hello World",
			isBold:   true,
			isItaly:  true,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("粗斜体英文文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "空文本",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width != 0 {
					t.Errorf("空文本宽度应该为0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "特殊字符",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "!@#$%^&*()_+-=[]{}|;':\",./<>?",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("特殊字符宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "数字测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 12,
			text:     "1234567890",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("数字文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "多语言测试-日语",
			lang:     "ja",
			fontName: "PingFang SC",
			fontSize: 16,
			text:     "こんにちは世界",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("日语文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "多语言测试-韩语",
			lang:     "ko",
			fontName: "PingFang SC",
			fontSize: 16,
			text:     "안녕하세요 세계",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("韩语文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "大字号测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 48,
			text:     "BIG TEXT",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("大字号文本宽度应该大于0，实际: %.2f", width)
				}
				// 大字号应该比小字号宽很多
				if width < 100 {
					t.Errorf("48号字体宽度可能过小: %.2f", width)
				}
			},
		},
		{
			name:     "小字号测试",
			lang:     "en",
			fontName: "Arial",
			fontSize: 8,
			text:     "small text",
			isBold:   false,
			isItaly:  false,
			wantErr:  false,
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("小字号文本宽度应该大于0，实际: %.2f", width)
				}
			},
		},
		// 错误情况测试
		{
			name:     "不存在的字体",
			lang:     "en",
			fontName: "ThisFontDoesNotExistAnywhere12345",
			fontSize: 12,
			text:     "test",
			isBold:   false,
			isItaly:  false,
			wantErr:  false, // 改为 false，因为系统会找到备用字体
			checkFn: func(t *testing.T, width float64) {
				if width <= 0 {
					t.Errorf("即使字体不存在，也应该找到备用字体并返回宽度 > 0，实际: %.2f", width)
				}
			},
		},
		{
			name:     "空字体名称",
			lang:     "en",
			fontName: "",
			fontSize: 12,
			text:     "test",
			isBold:   false,
			isItaly:  false,
			wantErr:  true,
			checkFn:  nil,
		},
		{
			name:     "负数字体大小",
			lang:     "en",
			fontName: "Arial",
			fontSize: -12,
			text:     "test",
			isBold:   false,
			isItaly:  false,
			wantErr:  true,
			checkFn:  nil,
		},
		{
			name:     "零字体大小",
			lang:     "en",
			fontName: "Arial",
			fontSize: 0,
			text:     "test",
			isBold:   false,
			isItaly:  false,
			wantErr:  true,
			checkFn:  nil,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			width, fontPath, err := fontManager.MeasureTextWidth(tt.lang, tt.fontName, tt.fontSize, tt.text, tt.isBold, tt.isItaly)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("FontManager.MeasureTextWidth() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果不期望错误，执行自定义检查
			if !tt.wantErr && tt.checkFn != nil {
				tt.checkFn(t, width)
			}

			// 记录结果用于调试
			if !tt.wantErr {
				t.Logf("FontManager.MeasureTextWidth(%q, %q, %.0f, %q, %v, %v) = %.2f 像素, 字体路径: %s",
					tt.lang, tt.fontName, tt.fontSize, tt.text, tt.isBold, tt.isItaly, width, fontPath)
			}
		})
	}
}

// BenchmarkMeasureTextWidth_English 英文文本基准测试
func BenchmarkMeasureTextWidth_English(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	// 预热
	fontManager.MeasureTextWidth("en", "Arial", 14, "Hello World", false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := fontManager.MeasureTextWidth("en", "Arial", 14, "Hello World", false, false)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMeasureTextWidth_Chinese 中文文本基准测试
func BenchmarkMeasureTextWidth_Chinese(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	// 预热
	fontManager.MeasureTextWidth("zh", "PingFang SC", 16, "你好世界", false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := fontManager.MeasureTextWidth("zh", "PingFang SC", 16, "你好世界", false, false)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMeasureTextWidth_Mixed 中英混合文本基准测试
func BenchmarkMeasureTextWidth_Mixed(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	// 预热
	fontManager.MeasureTextWidth("zh", "PingFang SC", 14, "Hello 你好 World 世界", false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := fontManager.MeasureTextWidth("zh", "PingFang SC", 14, "Hello 你好 World 世界", false, false)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMeasureTextWidth_LongText 长文本基准测试
func BenchmarkMeasureTextWidth_LongText(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	// 长文本
	longText := "这是一段很长的中文测试文本，用来测试字体宽度计算的性能。This is a long English text for testing font width calculation performance. 这段文本包含了中英文混合的内容，可以更好地测试在实际使用场景中的性能表现。The text contains mixed Chinese and English content to better test performance in real-world scenarios."

	// 预热
	fontManager.MeasureTextWidth("zh", "PingFang SC", 14, longText, false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := fontManager.MeasureTextWidth("zh", "PingFang SC", 14, longText, false, false)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkMeasureTextWidth_DifferentSizes 不同字号基准测试
func BenchmarkMeasureTextWidth_DifferentSizes(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	sizes := []float64{8, 12, 16, 20, 24, 32, 48}
	text := "Test 测试"

	for _, size := range sizes {
		b.Run(fmt.Sprintf("Size%.0f", size), func(b *testing.B) {
			// 预热
			fontManager.MeasureTextWidth("zh", "PingFang SC", size, text, false, false)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, _, err := fontManager.MeasureTextWidth("zh", "PingFang SC", size, text, false, false)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	}
}

// BenchmarkMeasureTextWidth_MultipleLanguages 多语言基准测试
func BenchmarkMeasureTextWidth_MultipleLanguages(b *testing.B) {
	// 获取字体管理器
	fontManager := GetFontManager()

	testCases := []struct {
		lang string
		text string
	}{
		{"en", "Hello World"},
		{"zh", "你好世界"},
		{"ja", "こんにちは"},
		{"ko", "안녕하세요"},
		{"fr", "Bonjour"},
		{"de", "Hallo Welt"},
		{"es", "Hola Mundo"},
		{"ru", "Привет мир"},
		{"ar", "مرحبا بالعالم"},
	}

	for _, tc := range testCases {
		b.Run(tc.lang, func(b *testing.B) {
			// 预热
			fontManager.MeasureTextWidth(tc.lang, "Arial", 14, tc.text, false, false)

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, _, err := fontManager.MeasureTextWidth(tc.lang, "Arial", 14, tc.text, false, false)
				if err != nil {
					b.Fatal(err)
				}
			}
		})
	}
}

// BenchmarkMeasureTextWidth_GlobalFunction 全局函数基准测试
func BenchmarkMeasureTextWidth_GlobalFunction(b *testing.B) {
	// 获取字体管理器
	mgr := GetFontManager()

	// 预热
	mgr.MeasureTextWidth("zh", "PingFang SC", 14, "Hello 你好", false, false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := mgr.MeasureTextWidth("zh", "PingFang SC", 14, "Hello 你好", false, false)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkMeasureTextWidth_GlobalFunction2(b *testing.B) {
	// 获取字体管理器
	mgr := GetFontManager()

	text := "我可以帮你把这个 freetype-go 版本扩展成**读取 ASS 样式（字号、ScaleX、Spacing、Bold、Italic）**的测宽工具，这样你可以直接用在字幕自动换行。你要我直接把这个功能加上吗？这样就能直接测出和字幕里一样的宽度。"
	in := ""
	for _, r := range text {
		in = in + string(r)
		width, fontPath, err := mgr.MeasureTextWidth("zh", "PingFang SC", 14, in, false, false)
		if err != nil {
			return
		}
		g.Log().Infof(context.Background(), "字符: %s, 宽度: %f, 字体: %s", in, width, fontPath)
	}

}
