package font_util

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"
	"sync"
)

var (
	fontCache  = make(map[string]string)
	cacheMutex sync.RWMutex
)

// genCacheKey 生成缓存键
func genCacheKey(family string, bold, italic bool) string {
	style := "Regular"
	if bold && italic {
		style = "Bold Italic"
	} else if bold {
		style = "Bold"
	} else if italic {
		style = "Italic"
	}
	return fmt.Sprintf("%s|%s", family, style)
}

// fontExists 用 fc-list 判断字体族是否存在
func fontExists(family string) bool {
	cmd := exec.Command("fc-list", ":family="+family)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		return false
	}
	return strings.TrimSpace(out.String()) != ""
}

// FindFontPath 根据字体名和样式(Bold/Italic)返回字体文件路径，带缓存 + 严格检查
func FindFontPath(family string, bold, italic bool) (string, error) {
	key := genCacheKey(family, bold, italic)

	// 1. 查缓存
	cacheMutex.RLock()
	if path, ok := fontCache[key]; ok {
		cacheMutex.RUnlock()
		return path, nil
	}
	cacheMutex.RUnlock()

	// 2. 检查字体族是否存在
	if !fontExists(family) {
		return "", fmt.Errorf("font family %q not found on system", family)
	}

	// 3. 构造查询
	style := strings.Split(key, "|")[1]
	query := fmt.Sprintf("%s:style=%s", family, style)

	// 4. 调用 fc-match
	cmd := exec.Command("fc-match", "-v", query)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("fc-match failed: %w", err)
	}

	// 5. 解析输出
	var path string
	lines := strings.Split(out.String(), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "file:") {
			parts := strings.Split(line, "\"")
			if len(parts) >= 2 {
				path = parts[1]
				break
			}
		}
	}
	if path == "" {
		return "", fmt.Errorf("font file not found for %s (%s)", family, style)
	}

	// 6. 写缓存
	cacheMutex.Lock()
	fontCache[key] = path
	cacheMutex.Unlock()

	return path, nil
}
