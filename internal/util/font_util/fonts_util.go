package font_util

import (
	"bytes"
	"fmt"
	"os"
	"slices"
	"sync"

	"github.com/go-text/typesetting/di"
	"github.com/go-text/typesetting/font"
	"github.com/go-text/typesetting/language"
	"github.com/go-text/typesetting/shaping"
	"golang.org/x/image/math/fixed"
)

// 拉丁文字系统
var latinLanguages = []string{
	"en", "fr", "de", "vi", "id", "pt", "it", "es", "nl", "tr", "ms",
	"fil", "pl", "sv", "ro", "cs", "fi", "hr", "da", "hu", "no", "sk",
	"af", "az", "ca", "et", "gl", "is", "kn", "kk", "lv", "lt",
	"mi", "sl", "sw", "tl", "cy",
}

// FontManager 字体管理器（单例模式）
type FontManager struct {
	fontCache    map[string]*font.Face // 字体名称 -> 字体面
	fontPaths    map[string]string     // 字体名称 -> 字体文件路径
	fontMutex    sync.RWMutex
	langToScript map[string]language.Script
	initialized  bool
}

// 全局字体管理器实例
var (
	fontManager *FontManager
	once        sync.Once
)

// GetFontManager 获取字体管理器单例实例
// 自动完成初始化，无需额外调用 NewFontManager
func GetFontManager() *FontManager {
	once.Do(func() {
		fontManager = &FontManager{
			fontCache:    make(map[string]*font.Face),
			fontPaths:    make(map[string]string),
			langToScript: make(map[string]language.Script),
			initialized:  true, // 直接标记为已初始化
		}
		fontManager.initLanguageMapping()
	})
	return fontManager
}

// MeasureTextWidth 计算给定字体+字号下的文本宽度（像素）
// lang: 语言代码
// fontName: 字体名称
// fontSize: 字体大小（像素）
// text: 要测量的文本
// isBold: 是否粗体
// isItaly: 是否斜体
// 返回: 文本宽度（像素）、实际使用的字体路径和错误信息
func (fm *FontManager) MeasureTextWidth(lang, fontName string, fontSize float64, text string, isBold, isItaly bool) (float64, string, error) {
	// 输入验证
	if fontName == "" {
		return 0, "", fmt.Errorf("字体名称不能为空")
	}
	if fontSize <= 0 {
		return 0, "", fmt.Errorf("字体大小必须大于0，当前值: %f", fontSize)
	}
	if text == "" {
		return 0, "", nil // 空文本宽度为0
	}

	// 从缓存获取字体，如果没有则通过 FindFontPath 动态加载
	face, fontPath, err := fm.getFontByNameAndStyle(fontName, isBold, isItaly)
	if err != nil {
		return 0, "", err
	}

	// 创建 shaping 输入
	textRunes := []rune(text)
	input := shaping.Input{
		Text:      textRunes,
		RunStart:  0,
		RunEnd:    len(textRunes),
		Direction: di.DirectionLTR,
		Face:      face,
		Size:      fixed.I(int(fontSize)), // 转换为 fixed.Int26_6
		Script:    fm.getScript(lang),     // 根据语言选择合适的书写系统
		Language:  language.NewLanguage(lang),
	}

	// 执行文本整形
	shaper := &shaping.HarfbuzzShaper{}
	output := shaper.Shape(input)

	// 计算总宽度
	totalWidth := float64(0)
	for _, glyph := range output.Glyphs {
		totalWidth += float64(glyph.XAdvance) / 64.0 // 转换为像素
	}

	return totalWidth, fontPath, nil
}

// initLanguageMapping 初始化语言到书写系统的映射
func (fm *FontManager) initLanguageMapping() {
	// 汉字系统
	hanLanguages := []string{"zh", "zh-cn", "zh-tw", "zh-hk", "zh-sg", "yue"}
	for _, lang := range hanLanguages {
		fm.langToScript[lang] = language.Han
	}

	// 日文
	fm.langToScript["ja"] = language.Hiragana

	// 韩文
	fm.langToScript["ko"] = language.Hangul

	// 阿拉伯文字系统
	arabicLanguages := []string{"ar", "fa", "ur"}
	for _, lang := range arabicLanguages {
		fm.langToScript[lang] = language.Arabic
	}

	// 泰文
	fm.langToScript["th"] = language.Thai

	// 天城文字系统
	devanagariLanguages := []string{"hi", "ta", "ne", "mr"}
	for _, lang := range devanagariLanguages {
		fm.langToScript[lang] = language.Devanagari
	}

	// 西里尔文字系统
	cyrillicLanguages := []string{"ru", "bg", "uk", "be", "bs", "sr", "mk"}
	for _, lang := range cyrillicLanguages {
		fm.langToScript[lang] = language.Cyrillic
	}

	// 希腊文
	fm.langToScript["el"] = language.Greek

	// 亚美尼亚文
	fm.langToScript["hy"] = language.Armenian

	// 格鲁吉亚文
	fm.langToScript["ka"] = language.Georgian

	// 希伯来文
	fm.langToScript["he"] = language.Hebrew

	for _, lang := range latinLanguages {
		fm.langToScript[lang] = language.Latin
	}
}

// ClearCache 清空字体缓存（可选的维护方法）
func (fm *FontManager) ClearCache() {
	fm.fontMutex.Lock()
	defer fm.fontMutex.Unlock()

	fm.fontCache = make(map[string]*font.Face)
	fm.fontPaths = make(map[string]string)
}

// GetScript 根据语言代码获取对应的书写系统
func (fm *FontManager) getScript(lang string) language.Script {
	if script, exists := fm.langToScript[lang]; exists {
		return script
	}
	// 找不到对应的书写系统时使用拉丁文字系统
	return language.Latin
}

// getFontByNameAndStyle 根据字体名称和样式获取字体面
// 返回: 字体面、字体路径和错误信息
func (fm *FontManager) getFontByNameAndStyle(fontName string, isBold, isItaly bool) (*font.Face, string, error) {
	if !fm.initialized {
		return nil, "", fmt.Errorf("字体管理器未初始化，请先调用 GetFontManager() 方法")
	}

	// 生成缓存键，包含字体名称和样式信息
	cacheKey := genCacheKey(fontName, isBold, isItaly)

	// 先尝试从缓存获取
	fm.fontMutex.RLock()
	if face, exists := fm.fontCache[cacheKey]; exists {
		fontPath := fm.fontPaths[cacheKey]
		fm.fontMutex.RUnlock()
		return face, fontPath, nil
	}
	fm.fontMutex.RUnlock()

	// 缓存中没有，需要动态加载
	fontPath, err := FindFontPath(fontName, isBold, isItaly)
	if err != nil {
		return nil, "", fmt.Errorf("查找字体路径失败: %v", err)
	}

	// 加载字体
	face, err := fm.loadFontFromPath(fontPath)
	if err != nil {
		return nil, "", fmt.Errorf("加载字体失败: %v", err)
	}

	// 缓存字体
	fm.fontMutex.Lock()
	fm.fontCache[cacheKey] = face
	fm.fontPaths[cacheKey] = fontPath
	fm.fontMutex.Unlock()

	return face, fontPath, nil
}

// generateCacheKey 生成缓存键
func (fm *FontManager) generateCacheKey(fontName string, isBold, isItaly bool) string {
	style := "Regular"
	if isBold && isItaly {
		style = "Bold Italic"
	} else if isBold {
		style = "Bold"
	} else if isItaly {
		style = "Italic"
	}
	return fmt.Sprintf("%s|%s", fontName, style)
}

// getAvailableFonts 获取已缓存的字体列表（用于调试）
func (fm *FontManager) getAvailableFonts() []string {
	fm.fontMutex.RLock()
	defer fm.fontMutex.RUnlock()

	fonts := make([]string, 0, len(fm.fontCache))
	for fontName := range fm.fontCache {
		fonts = append(fonts, fontName)
	}
	return fonts
}

// loadFontFromPath 从文件路径加载字体
func (fm *FontManager) loadFontFromPath(fontPath string) (*font.Face, error) {
	// 读取并解析字体文件
	fontData, err := os.ReadFile(fontPath)
	if err != nil {
		return nil, fmt.Errorf("读取字体失败: %v", err)
	}

	// 创建 Resource 接口实现
	resource := bytes.NewReader(fontData)

	// 尝试解析字体，先尝试 TTF，如果失败则尝试 TTC
	parsedFont, err := font.ParseTTF(resource)
	if err != nil {
		// 如果是 TTC 文件，尝试解析为 TTC 并取第一个字体
		resource.Seek(0, 0) // 重置读取位置
		faces, err2 := font.ParseTTC(resource)
		if err2 != nil {
			return nil, fmt.Errorf("解析字体失败: TTF错误=%v, TTC错误=%v", err, err2)
		}
		if len(faces) == 0 {
			return nil, fmt.Errorf("TTC 文件中没有找到字体")
		}
		parsedFont = faces[0] // 使用第一个字体
	}

	return parsedFont, nil
}

func IsLatinLang(lang string) bool {
	return slices.Contains(latinLanguages, lang)
}
