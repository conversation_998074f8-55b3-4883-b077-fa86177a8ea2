package font_util

import (
	"fmt"
)

// ExampleUsage 展示新的字体管理器API的使用方法
func ExampleUsage() {
	// 1. 获取字体管理器单例实例（自动初始化）
	fontManager := GetFontManager()

	// 2. 测量不同样式的文本宽度
	testCases := []struct {
		lang     string
		fontName string
		fontSize float64
		text     string
		isBold   bool
		isItaly  bool
		desc     string
	}{
		{"en", "Arial", 14, "Hello World", false, false, "普通英文"},
		{"en", "Arial", 14, "Hello World", true, false, "粗体英文"},
		{"en", "Arial", 14, "Hello World", false, true, "斜体英文"},
		{"en", "Arial", 14, "Hello World", true, true, "粗斜体英文"},
		{"zh", "PingFang SC", 16, "你好世界", false, false, "普通中文"},
		{"zh", "PingFang SC", 16, "你好世界", true, false, "粗体中文"},
		{"zh", "PingFang SC", 16, "你好世界", false, true, "斜体中文"},
		{"zh", "PingFang SC", 16, "你好世界", true, true, "粗斜体中文"},
		{"zh", "Arial", 14, "Hello 你好 World 世界", false, false, "中英混合"},
		{"zh", "Arial", 14, "Hello 你好 World 世界", true, false, "粗体中英混合"},
	}

	fmt.Println("=== 字体宽度测量示例 ===")
	for _, tc := range testCases {
		width, fontPath, err := fontManager.MeasureTextWidth(tc.lang, tc.fontName, tc.fontSize, tc.text, tc.isBold, tc.isItaly)
		if err != nil {
			fmt.Printf("❌ %s: 错误 - %v\n", tc.desc, err)
			continue
		}

		styleDesc := "普通"
		if tc.isBold && tc.isItaly {
			styleDesc = "粗斜体"
		} else if tc.isBold {
			styleDesc = "粗体"
		} else if tc.isItaly {
			styleDesc = "斜体"
		}

		fmt.Printf("✅ %s (%s): %.2f 像素\n", tc.desc, styleDesc, width)
		fmt.Printf("   字体: %s, 字号: %.0f, 文本: '%s'\n", tc.fontName, tc.fontSize, tc.text)
		fmt.Printf("   实际字体路径: %s\n", fontPath)
		fmt.Println()
	}

	// 3. 展示字体缓存功能
	fmt.Println("=== 字体缓存测试 ===")
	
	// 第一次调用（会加载字体）
	start := fmt.Sprintf("第一次调用")
	width1, fontPath1, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Test", false, false)
	fmt.Printf("%s: %.2f 像素, 字体路径: %s\n", start, width1, fontPath1)

	// 第二次调用（使用缓存）
	start = fmt.Sprintf("第二次调用（缓存）")
	width2, fontPath2, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Test", false, false)
	fmt.Printf("%s: %.2f 像素, 字体路径: %s\n", start, width2, fontPath2)

	// 不同样式（会加载新的字体变体）
	start = fmt.Sprintf("粗体版本")
	width3, fontPath3, _ := fontManager.MeasureTextWidth("en", "Arial", 12, "Test", true, false)
	fmt.Printf("%s: %.2f 像素, 字体路径: %s\n", start, width3, fontPath3)

	fmt.Printf("普通版本和粗体版本宽度差异: %.2f 像素\n", width3-width1)
	fmt.Printf("字体路径是否相同: %v\n", fontPath1 == fontPath3)
}

// CompareWithOldAPI 对比新旧API的差异
func CompareWithOldAPI() {
	fmt.Println("=== 新旧API对比 ===")
	
	// 新API - 无需预先准备字体路径
	fmt.Println("新API使用方式:")
	fmt.Println("1. fontManager := GetFontManager()  // 获取单例实例，自动初始化")
	fmt.Println("2. width, fontPath, err := fontManager.MeasureTextWidth(lang, fontName, fontSize, text, isBold, isItaly)")
	fmt.Println()

	fmt.Println("优势:")
	fmt.Println("✅ 无需手动管理字体文件路径")
	fmt.Println("✅ 自动通过 FindFontPath 查找系统字体")
	fmt.Println("✅ 支持粗体和斜体样式")
	fmt.Println("✅ 智能字体缓存，包含样式信息")
	fmt.Println("✅ 自动回退到系统默认字体")
	fmt.Println("✅ 单例模式，无需重复初始化")
	fmt.Println("✅ 返回实际使用的字体路径，便于调试")
	fmt.Println()
	
	fmt.Println("旧API使用方式:")
	fmt.Println("1. fonts := map[string]string{\"fontName\": \"/path/to/font.ttf\"}")
	fmt.Println("2. fontManager, err := NewFontManager(fonts)")
	fmt.Println("3. width, err := fontManager.MeasureTextWidth(lang, fontName, fontSize, text)  // 无字体路径返回")
	fmt.Println()
	
	fmt.Println("限制:")
	fmt.Println("❌ 需要手动指定字体文件路径")
	fmt.Println("❌ 不支持字体样式（粗体/斜体）")
	fmt.Println("❌ 字体路径硬编码，不够灵活")
	fmt.Println("❌ 无法自动处理字体不存在的情况")
	fmt.Println("❌ 不返回实际使用的字体路径")
}
