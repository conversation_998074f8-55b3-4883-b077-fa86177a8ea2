package commentary_svc

import (
	"business-workflow/internal/entity/bo"
	"context"
)

// ICommentarySubtitleItemService 解说字幕项服务接口
type ICommentarySubtitleItemService interface {
	// MergeSubtitle 合并字幕片段
	MergeSubtitle(ctx context.Context, req *bo.MergeSubtitleReqBO) (*bo.MergeSubtitleResBO, error)

	// SplitSubtitle 拆分字幕片段
	SplitSubtitle(ctx context.Context, req *bo.SplitSubtitleReqBO) (*bo.SplitSubtitleResBO, error)

	// DeleteSubtitleItem 删除字幕项
	DeleteSubtitleItem(ctx context.Context, subTaskId int64, subtitleItemId int64) error

	// ModifySubtitle 修改字幕
	ModifySubtitle(ctx context.Context, req *bo.ModifySubtitleReqBO) (*bo.ModifySubtitleResBO, error)

	// AddSubtitle 添加字幕
	AddSubtitle(ctx context.Context, req *bo.AddSubtitleReqBO) (*bo.AddSubtitleResBO, error)

	// TextTranslate 文本翻译
	TextTranslate(ctx context.Context, subTaskId int64, subtitmeItemId int64, customPrompt string, translateBack bool) error

	// GenerateVoice 生成配音
	GenerateVoice(ctx context.Context, subTaskId int64, subtitmeItemId int64) error

	// GetBatchSubtitleItems 获取批量配音状态
	GetBatchSubtitleItems(ctx context.Context, subTaskId int64, subtitleItemIds []int64) ([]*bo.CommentarySubtitleItemBO, error)

	// ModifySubtitleSpeed 修改片段语速
	ModifySubtitleSpeed(ctx context.Context, req *bo.ModifySubtitleSpeedReqBO) (*bo.ModifySubtitleSpeedResBO, error)

	// ModifySubtitleVolume 修改片段音量
	ModifySubtitleVolume(ctx context.Context, req *bo.ModifySubtitleVolumeReqBO) (*bo.ModifySubtitleVolumeResBO, error)
}
