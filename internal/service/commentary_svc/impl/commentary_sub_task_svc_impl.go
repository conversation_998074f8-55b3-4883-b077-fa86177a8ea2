package impl

import (
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/service/commentary_svc"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/concurrent"
	"gorm.io/gorm"
)

type CommentarySubTaskServiceImpl struct {
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo
}

func NewCommentarySubTaskServiceImpl(commentarySubTaskRepo commentary_repo.ICommentarySubTaskRepo) commentary_svc.ICommentarySubTaskService {
	return &CommentarySubTaskServiceImpl{
		commentarySubTaskRepo:      commentarySubTaskRepo,
		commentarySubtitleItemRepo: commentary_repo_impl.NewCommentarySubtitleItemRepoImpl(),
	}
}

// CreateSubTasks 批量创建子任务
func (s *CommentarySubTaskServiceImpl) CreateSubTasks(ctx context.Context, subTasks []*bo.CommentarySubTaskBO, tx *gorm.DB) error {
	if len(subTasks) == 0 {
		return fmt.Errorf("子任务列表不能为空")
	}

	// 转换 BO 为 DO
	subTasksDO := make([]*do.CommentarySubTask, len(subTasks))
	for i, subTaskBO := range subTasks {
		subTasksDO[i] = conv.CommentarySubTaskBOToDO(subTaskBO)
	}

	if err := s.commentarySubTaskRepo.CreateSubTasks(ctx, subTasksDO, tx); err != nil {
		g.Log().Errorf(ctx, "批量创建子任务失败: %v", err)
		return fmt.Errorf("批量创建子任务失败: %w", err)
	}

	g.Log().Infof(ctx, "成功批量创建子任务, 数量: %d", len(subTasks))
	return nil
}

// GetSubTasksByMainTaskId 根据主任务ID获取子任务列表
func (s *CommentarySubTaskServiceImpl) GetSubTasksByMainTaskId(ctx context.Context, mainTaskId int64) ([]*bo.CommentarySubTaskBO, error) {
	if mainTaskId <= 0 {
		return nil, fmt.Errorf("主任务ID无效")
	}

	subTasksDO, err := s.commentarySubTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		return nil, fmt.Errorf("查询子任务列表失败: %w", err)
	}

	// 转换 DO 为 BO
	subTasksBOs := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
	for i, subTaskDO := range subTasksDO {
		subTasksBOs[i] = conv.CommentarySubTaskDOToBO(subTaskDO)
	}

	return subTasksBOs, nil
}

// GetSubTaskById 根据ID获取子任务详情
func (s *CommentarySubTaskServiceImpl) GetSubTaskById(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error) {
	if id <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	subTaskDO, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDO == nil {
		return nil, fmt.Errorf("子任务不存在")
	}

	// 转换 DO 为 BO
	subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)

	return subTaskBO, nil
}

func (s *CommentarySubTaskServiceImpl) GetSubTaskListByIds(ctx context.Context, ids []int64) ([]*bo.CommentarySubTaskBO, error) {
	if len(ids) <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 获取子任务详情
	subTaskDOs, err := s.commentarySubTaskRepo.GetSubTasksByIds(ctx, ids)
	if err != nil {
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDOs == nil {
		return nil, fmt.Errorf("子任务不存在")
	}
	res := []*bo.CommentarySubTaskBO{}
	for _, subTaskDO := range subTaskDOs {
		subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)
		res = append(res, subTaskBO)
	}

	return res, nil
}

// GetSubTaskWithSubtitles 根据ID获取子任务详情及字幕项列表
func (s *CommentarySubTaskServiceImpl) GetSubTaskWithSubtitles(ctx context.Context, id int64) (*bo.CommentarySubTaskBO, error) {

	// 获取子任务详情
	subTaskDO, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	if subTaskDO == nil {
		return nil, fmt.Errorf("子任务不存在")
	}

	subTaskBO := conv.CommentarySubTaskDOToBO(subTaskDO)
	// 获取字幕项列表
	subtitleItems, err := s.commentarySubtitleItemRepo.GetSubtitleItemsBySubTaskId(ctx, subTaskDO.Id)
	if err != nil {
		g.Log().Errorf(ctx, "查询字幕项列表失败, subTaskId: %d, err: %v", subTaskDO.Id, err)
		return nil, fmt.Errorf("查询字幕项列表失败: %w", err)
	}
	for _, item := range subtitleItems {
		subTaskBO.SubtitleItemList = append(subTaskBO.SubtitleItemList, conv.CommentarySubtitleItemDOToBO(item))
	}

	return subTaskBO, nil
}

// UpdateSubTaskStatus 更新子任务状态
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskStatus(ctx context.Context, req *bo.UpdateSubTaskStatusReqBO) (*bo.UpdateSubTaskStatusResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if err := s.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, req.SubTaskId, req.Status, req.ErrMsg); err != nil {
		return nil, fmt.Errorf("更新子任务状态失败: %w", err)
	}

	g.Log().Infof(ctx, "更新子任务状态成功, ID: %d, 状态: %d", req.SubTaskId, req.Status)
	return &bo.UpdateSubTaskStatusResBO{Success: true}, nil
}

// DeleteSubTask 删除子任务
func (s *CommentarySubTaskServiceImpl) DeleteSubTask(ctx context.Context, req *bo.DeleteSubTaskReqBO) (*bo.DeleteSubTaskResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if err := s.commentarySubTaskRepo.DeleteSubTask(ctx, req.SubTaskId); err != nil {
		return nil, fmt.Errorf("删除子任务失败: %w", err)
	}

	g.Log().Infof(ctx, "删除子任务成功, ID: %d", req.SubTaskId)
	return &bo.DeleteSubTaskResBO{Success: true}, nil
}

func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgm(ctx context.Context, req *bo.UpdateSubTaskBgmReqBO) (*bo.UpdateSubTaskBgmResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if req.BgmUrl == "" {
		return nil, fmt.Errorf("BGM URL不能为空")
	}

	url := req.BgmUrl
	// 是否是 http 开头
	if !strings.HasPrefix(req.BgmUrl, "http") {
		url = obs.GetCdnUrlByObjectName(req.BgmUrl)
	}
	// 调用 repository 更新BGM URL
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgm(ctx, req.SubTaskId, url)
	if err != nil {
		return nil, fmt.Errorf("更新子任务BGM失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或BGM URL未发生变化")
	}
	concurrent.GoSafe(func() {
		err := s.MarkNeedMerge(ctx, req.SubTaskId)
		if err != nil {
			return
		}
	})

	g.Log().Infof(ctx, "更新子任务BGM成功, ID: %d, BGM URL: %s, 影响行数: %d", req.SubTaskId, req.BgmUrl, rowsAffected)
	return &bo.UpdateSubTaskBgmResBO{Success: true}, nil
}

// UpdateSubTaskEraseMode 更新子任务擦除模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskEraseMode(ctx context.Context, req *bo.UpdateSubTaskEraseModeReqBO) (*bo.UpdateSubTaskEraseModeResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 调用 repository 更新擦除模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskEraseMode(ctx, req.SubTaskId, req.EraseMode, req.EraseEdition)
	if err != nil {
		return nil, fmt.Errorf("更新子任务擦除模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或擦除模式未发生变化")
	}

	g.Log().Infof(ctx, "更新子任务擦除模式成功, ID: %d, 擦除模式: %d, 擦除版本: %d, 影响行数: %d", req.SubTaskId, req.EraseMode, req.EraseEdition, rowsAffected)
	return &bo.UpdateSubTaskEraseModeResBO{Success: true}, nil
}

// UpdateSubTaskBgmMode 更新子任务BGM模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgmMode(ctx context.Context, req *bo.UpdateSubTaskBgmModeReqBO) (*bo.UpdateSubTaskBgmModeResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	// 调用 repository 更新BGM模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgmMode(ctx, req.SubTaskId, req.BgmMode)
	if err != nil {
		return nil, fmt.Errorf("更新子任务BGM模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或BGM模式未发生变化")
	}
	err = s.MarkNeedMerge(ctx, req.SubTaskId)

	g.Log().Infof(ctx, "更新子任务BGM模式成功, ID: %d, BGM模式: %d, 影响行数: %d", req.SubTaskId, req.BgmMode, rowsAffected)
	return &bo.UpdateSubTaskBgmModeResBO{Success: true}, nil
}

// UpdateSubTaskOcrRectInfo 更新子任务OCR区域信息
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskOcrRectInfo(ctx context.Context, req *bo.UpdateSubTaskOcrRectInfoReqBO) (*bo.UpdateSubTaskOcrRectInfoResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.SubTaskId <= 0 {
		return nil, fmt.Errorf("子任务ID无效")
	}

	if req.OcrRectInfo == nil {
		return nil, fmt.Errorf("OCR区域信息不能为空")
	}

	// 调用 repository 更新OCR区域信息
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskOcrRectInfo(ctx, req.SubTaskId, req.OcrRectInfo)
	if err != nil {
		return nil, fmt.Errorf("更新子任务OCR区域信息失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("子任务不存在或OCR区域信息未发生变化")
	}

	g.Log().Infof(ctx, "更新子任务OCR区域信息成功, ID: %d, 影响行数: %d", req.SubTaskId, rowsAffected)
	return &bo.UpdateSubTaskOcrRectInfoResBO{Success: true}, nil
}

// UpdateSubTaskBgmModeByMainTaskId 根据主任务ID更新所有子任务的BGM模式
func (s *CommentarySubTaskServiceImpl) UpdateSubTaskBgmModeByMainTaskId(ctx context.Context, req *bo.UpdateSubTaskBgmModeByMainTaskIdReqBO) (*bo.UpdateSubTaskBgmModeByMainTaskIdResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.MainTaskId <= 0 {
		return nil, fmt.Errorf("主任务ID无效")
	}

	// 调用 repository 根据主任务ID更新BGM模式
	rowsAffected, err := s.commentarySubTaskRepo.UpdateSubTaskBgmModeByMainTaskId(ctx, req.MainTaskId, req.BgmMode)
	if err != nil {
		return nil, fmt.Errorf("根据主任务ID更新子任务BGM模式失败: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("没有找到相关子任务或BGM模式未发生变化")
	}

	g.Log().Infof(ctx, "根据主任务ID更新子任务BGM模式成功, 主任务ID: %d, BGM模式: %d, 影响行数: %d", req.MainTaskId, req.BgmMode, rowsAffected)
	return &bo.UpdateSubTaskBgmModeByMainTaskIdResBO{Success: true}, nil
}

func (s *CommentarySubTaskServiceImpl) MarkNeedMerge(ctx context.Context, id int64) error {
	err := s.commentarySubTaskRepo.UpdateSubTaskFields(ctx, id, map[string]interface{}{"need_merge": 1})
	if err != nil {
		g.Log().Errorf(ctx, "mark need merge failed, err: %v", err)
		return err
	}
	return nil
}

func (s *CommentarySubTaskServiceImpl) SaveGlobalSubtitleStyle(ctx context.Context, req *bo.SaveGlobalSubtitleStyleReqBO) (*bo.SaveGlobalSubtitleStyleResBO, error) {
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle start, req: %+v", req)

	// 获取子任务信息
	subTask, err := s.commentarySubTaskRepo.GetSubTaskById(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, GetSubTaskById error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}
	if subTask == nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, subTask not found, req: %v", req)
		return nil, uerrors.ErrSubtitleItemNoExist
	}
	subTask.CustomSubtitleStyle = req.CustomSubtitleStyle
	// 保存到数据库
	err = s.commentarySubTaskRepo.UpdateSubTaskWithSelectFields(ctx, subTask, []string{"custom_subtitle_style"})
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, UpdateSubTaskFields error, err: %v, req: %v", err, req)
		return nil, uerrors.ErrBusy
	}

	// 在事务中获取要修改的字幕项
	itemIds := make([]int64, 0, len(req.Subtitles))

	for _, val := range req.Subtitles {
		itemIds = append(itemIds, val.ItemId)
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle GetTranslateSubtitleItemByIds start")
	subtitleItemList, err := s.commentarySubtitleItemRepo.GetSubtitleItemsByIdsWithSelectFields(ctx, req.SubTaskId, itemIds, []string{"id", "sub_item_list"})
	if err != nil {
		g.Log().Errorf(ctx, "GetTranslateSubtitleItemByIds failed in transaction, err: %v, req: %+v", err, req)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle GetTranslateSubtitleItemByIds  end subtitleItemList len: %d", len(subtitleItemList))
	itemEntityMap := make(map[int64]*do.CommentarySubtitleItem)
	for _, item := range subtitleItemList {
		itemEntityMap[item.Id] = item
	}
	modifyItemList := make([]*do.CommentarySubtitleItem, 0, 0)
	for _, itemBO := range req.Subtitles {
		itemEntity := itemEntityMap[itemBO.ItemId]
		if itemEntity == nil {
			g.Log().Errorf(ctx, "itemEntity is nil,itemBO.ItemId:%d", itemBO.ItemId)
			continue
		}
		modifyItemList = append(modifyItemList, &do.CommentarySubtitleItem{
			Id:          itemEntity.Id,
			SubItemList: itemBO.SubItemList,
		})
	}

	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle UpdateTranslateSubtitleItemsWithCase start")
	err = s.commentarySubtitleItemRepo.UpdateSubtitleItemsWithCase(ctx, modifyItemList, []string{"sub_item_list"}, 1000)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateTranslateSubtitleItemsWithCase failed in transaction, err: %v, req: %+v", err, req)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle UpdateTranslateSubtitleItemsWithCase end")

	// 标记子任务需要重新合成
	err = s.MarkNeedMerge(ctx, req.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, MarkNeedMerge error, err: %v, subTaskId: %v", err, req.SubTaskId)
		// 这里不返回错误，因为主要操作已经成功，只是标记合成失败
	}

	g.Log().Infof(ctx, "SaveGlobalSubtitleStyle success, subTaskId: %v", req.SubTaskId)

	// 构建响应结果
	resp := &bo.SaveGlobalSubtitleStyleResBO{}
	return resp, nil
}
