package impl

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/repo/commentary_repo"
	"business-workflow/internal/service/commentary_svc"
	"business-workflow/internal/temporal"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/samber/lo"
	"go.temporal.io/sdk/client"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniBalance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	omniTenant "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_tenant"
	"gorm.io/gorm"
)

type CommentaryMainTaskServiceImpl struct {
	commentaryMainTaskRepo     commentary_repo.ICommentaryMainTaskRepo
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo
	commentarySourceDetailRepo commentary_repo.ICommentarySourceDetailRepo
}

func NewCommentaryMainTaskServiceImpl(commentaryMainTaskRepo commentary_repo.ICommentaryMainTaskRepo, commentarySubTaskRepo commentary_repo.ICommentarySubTaskRepo, commentarySubtitleItemRepo commentary_repo.ICommentarySubtitleItemRepo, commentarySourceDetailRepo commentary_repo.ICommentarySourceDetailRepo) commentary_svc.ICommentaryMainTaskService {
	return &CommentaryMainTaskServiceImpl{
		commentaryMainTaskRepo:     commentaryMainTaskRepo,
		commentarySubTaskRepo:      commentarySubTaskRepo,
		commentarySubtitleItemRepo: commentarySubtitleItemRepo,
		commentarySourceDetailRepo: commentarySourceDetailRepo,
	}
}

// CreateTask 创建解说主任务
func (s *CommentaryMainTaskServiceImpl) CreateTask(ctx context.Context, req *bo.CommentaryMainTaskCreateReqBO) (*bo.CommentaryMainTaskCreateResBO, error) {
	g.Log().Infof(ctx, "CreateTask: %+v", req)
	// 权益检查：判断高光解说或高光剪辑任务是否可以执行
	if !s.checkTenantRights(ctx, req) {
		g.Log().Errorf(ctx, "权益不足，tenant_id: %d, app_id: %d, task_type: %d, biz_mode: %d", req.TenantId, req.AppId, req.TaskType, req.BizMode)
		return nil, fmt.Errorf("权益不足")
	}

	g.Log().Debugf(ctx, "test: %+v", req)
	// 获取租户ID和请求ID
	tenantId := req.TenantId
	if tenantId == 0 {
		return nil, fmt.Errorf("租户ID不能为空")
	}

	requestId, ok := ctx.Value(trace.ReqId).(string)
	if !ok || requestId == "" {
		requestId = uuid.NewString()
	}

	// 直接 BO 转 DO
	taskDO, err := conv.CommentaryMainTaskCreateReqToDO(req, tenantId, requestId)
	if err != nil {
		g.Log().Errorf(ctx, "转换DO模型失败: req:%+v err:%v", req, err)
		return nil, fmt.Errorf("转换DO模型失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", req)

	// 在事务开始前预先创建资源详情和子任务
	sourceDetails, err := s.createSourceDetails(ctx, taskDO, req)
	if err != nil {
		g.Log().Errorf(ctx, "创建资源详情失败: %v", err)
		return nil, fmt.Errorf("创建资源详情失败: %w", err)
	}
	// 获取全部时长
	err = s.calculateSourceDetailDuration(ctx, sourceDetails)
	if err != nil {
		g.Log().Errorf(ctx, "获取素材总时长失败: %v", err)
		return nil, err
	}
	g.Log().Debugf(ctx, "test: %+v", req)

	subTasks, err := s.buildSubTasks(ctx, taskDO, req)
	if err != nil {
		g.Log().Errorf(ctx, "创建子任务失败: %v", err)
		return nil, fmt.Errorf("创建子任务失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", req)
	// 在事务开始前检查余额是否充足
	err = s.checkBalanceBeforeTransaction(ctx, req, sourceDetails, subTasks)
	if err != nil {
		g.Log().Errorf(ctx, "余额检查失败: %v", err)
		return nil, fmt.Errorf("余额检查失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", req)

	// 使用事务处理主任务和子任务的创建
	var resultTaskId int64
	err = db.GetDB().Debug().Transaction(func(tx *gorm.DB) error {
		// 在事务中创建主任务
		if err := s.commentaryMainTaskRepo.CreateTask(ctx, taskDO, tx); err != nil {
			g.Log().Errorf(ctx, "创建解说主任务失败: %v", err)
			return fmt.Errorf("创建任务失败: %w", err)
		}

		// 在事务中批量创建资源详情
		if len(sourceDetails) > 0 {
			if err := s.commentarySourceDetailRepo.CreateSourceDetails(ctx, sourceDetails, tx); err != nil {
				g.Log().Errorf(ctx, "批量创建资源详情失败: %v", err)
				return fmt.Errorf("批量创建资源详情失败: %w", err)
			}
		}

		// 在事务中批量创建子任务
		if len(subTasks) > 0 {
			if err := s.commentarySubTaskRepo.CreateSubTasks(ctx, subTasks, tx); err != nil {
				g.Log().Errorf(ctx, "批量创建子任务失败: %v", err)
				return fmt.Errorf("批量创建子任务失败: %w", err)
			}
		}

		// 记录成功信息
		g.Log().Infof(ctx, "成功创建解说主任务, ID: %d, 名称: %s, 子任务数量: %d, 资源详情数量: %d", taskDO.Id, taskDO.Name, len(subTasks), len(sourceDetails))
		resultTaskId = taskDO.Id
		return nil
	})

	if err != nil {
		// 事务失败时执行退款
		g.Log().Errorf(ctx, "事务执行失败，开始执行退款操作: %v", err)
		err := s.commentaryMainTaskRepo.UpdateTaskStatus(ctx, taskDO.Id, consts.MainTaskStatusFailed, "启动工作流失败")
		if err != nil {
			g.Log().Errorf(ctx, "更新任务状态失败: %v", err)
			return nil, err
		}
		return nil, err
	}
	// 提交到工作流

	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v_commentary_main_task_workflow", taskDO.Id),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	taskBO := conv.CommentaryMainTaskDOToBO(taskDO)
	taskBO.RequestID = ctx.Value(trace.ReqId).(string)

	subTaskBO := lo.Map(subTasks, func(subTask *do.CommentarySubTask, _ int) *bo.CommentarySubTaskBO {
		return conv.CommentarySubTaskDOToBO(subTask)
	})
	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(ctx, workflowOptions, "CommentaryWorkflow", taskBO, subTaskBO)
	if err != nil {
		g.Log().Errorf(ctx, "启动工作流失败: %v", err)
		// 工作流启动失败时，需要退款
		// 修改状态 为失败
		err := s.commentaryMainTaskRepo.UpdateTaskStatus(ctx, taskDO.Id, consts.MainTaskStatusFailed, "启动工作流失败")
		if err != nil {
			g.Log().Errorf(ctx, "更新任务状态失败: %v", err)
			return nil, err
		}
		return nil, err
	}
	g.Log().Infof(ctx, "启动工作流成功, run_id: %+v, workflow_id:%v", workflowRun.GetRunID(), workflowRun.GetID())
	// 修改主任务执行中
	err = s.commentaryMainTaskRepo.UpdateTaskStatus(ctx, taskDO.Id, consts.MainTaskStatusProcessing, "")
	if err != nil {
		g.Log().Errorf(ctx, "更新任务状态失败: %v", err)
		return nil, err
	}

	return &bo.CommentaryMainTaskCreateResBO{
		Id: resultTaskId,
	}, nil
}

// calculateSourceDetailDuration 并发计算资源详情时长（3个协程）
func (s *CommentaryMainTaskServiceImpl) calculateSourceDetailDuration(ctx context.Context, sourceDetails []*do.CommentarySourceDetail) error {
	// 筛选需要处理的资源
	var needProcess []*do.CommentarySourceDetail
	for _, detail := range sourceDetails {
		if detail.Duration > 0 {
			continue
		}
		if detail.SourceFileType == consts.SourceFileTypeImage {
			detail.Duration = 0
			continue
		}
		needProcess = append(needProcess, detail)
	}

	if len(needProcess) == 0 {
		return nil
	}

	// 3个协程并发处理
	var wg sync.WaitGroup
	errChan := make(chan error, len(needProcess))
	semaphore := make(chan struct{}, 3) // 限制并发数为3

	for _, detail := range needProcess {
		wg.Add(1)
		go func(d *do.CommentarySourceDetail) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			err := s.processSingleDuration(ctx, d)
			if err != nil {
				errChan <- err
			}
		}(detail)
	}

	// 等待所有协程完成
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// processSingleDuration 处理单个资源的时长
func (s *CommentaryMainTaskServiceImpl) processSingleDuration(ctx context.Context, detail *do.CommentarySourceDetail) error {
	objectName, err := obs.GetObjectNameByHttpsUrl(detail.SourceUrl)
	if err != nil {
		return err
	}

	presign, err := obs.GetOsClient().Presign(objectName)
	if err != nil {
		return err
	}

	endpointUrl := obs.ReplaceUrlToPublicEndpoint(presign)
	duration, err := ffmpeg.ProbeDuration(endpointUrl)
	if err != nil {
		return err
	}

	detail.Duration = duration
	return nil
}

// buildSubTasks 创建子任务
func (s *CommentaryMainTaskServiceImpl) buildSubTasks(ctx context.Context, mainTask *do.CommentaryMainTask, req *bo.CommentaryMainTaskCreateReqBO) ([]*do.CommentarySubTask, error) {
	targetNumber := req.TargetNumber
	if targetNumber <= 0 {
		return []*do.CommentarySubTask{}, nil
	}

	subTasks := make([]*do.CommentarySubTask, 0, targetNumber)
	now := time.Now()

	// 创建默认字幕样式
	defaultSubtitleStyle := s.createDefaultSubtitleStyle(mainTask.TargetResolution)

	for i := 0; i < targetNumber; i++ {
		// 生成子任务ID
		subTaskId, err := id_generator.GenerateId()
		if err != nil {
			return nil, fmt.Errorf("生成子任务ID失败: %w", err)
		}

		// 选择音色ID（如果有多个音色，循环使用）
		var voiceId int64
		if len(req.VoiceIDList) > 0 {
			voiceId = req.VoiceIDList[i%len(req.VoiceIDList)]
		}

		// 选择背景音乐URL（如果有多个BGM，随机选择一个）
		var bgmUrl string
		if len(req.BgmSourceList) > 0 {
			// 转换 BO 为 VO 以调用现有方法
			voBgmList := make([]*vo.MainTaskCommonSourceVO, len(req.BgmSourceList))
			for j, bgm := range req.BgmSourceList {
				voBgmList[j] = &vo.MainTaskCommonSourceVO{
					Name:           bgm.Name,
					SourceUrl:      bgm.SourceUrl,
					SourceFileType: bgm.SourceFileType,
					FromType:       bgm.FromType,
				}
			}
			bgmUrl = s.selectBgmUrl(voBgmList)
		}

		// 选择高光剧集URLs（如果有高光剧集源，随机选择一组）
		var highlightEpisodesUrls []string
		if len(req.HighlightEpisodesSourceList) > 0 {
			// 转换 BO 为 VO 以调用现有方法
			voHighlightList := make([]*vo.HighlightEpisodesSource, len(req.HighlightEpisodesSourceList))
			for j, highlight := range req.HighlightEpisodesSourceList {
				voHighlightList[j] = &vo.HighlightEpisodesSource{
					SourceList: make([]*vo.MainTaskCommonSourceVO, len(highlight.SourceList)),
				}
				for k, source := range highlight.SourceList {
					voHighlightList[j].SourceList[k] = &vo.MainTaskCommonSourceVO{
						Name:           source.Name,
						SourceUrl:      source.SourceUrl,
						SourceFileType: source.SourceFileType,
						FromType:       source.FromType,
					}
				}
			}
			highlightEpisodesUrls = s.selectHighlightEpisodesUrls(voHighlightList)
		}

		// 创建子任务
		subTask := &do.CommentarySubTask{
			Id:                    subTaskId,
			MainTaskId:            mainTask.Id,
			Name:                  fmt.Sprintf("%s_%d", mainTask.Name, i+1),
			SpeakerId:             1,
			VoiceId:               voiceId,
			TenantId:              mainTask.TenantId,
			Status:                consts.SubTaskStatusPending, // 初始状态：待执行
			AuditStatus:           consts.AuditStatusPending,   // 初始审核状态：待审核
			ErrCode:               0,
			ErrMsg:                "",
			EraseMode:             mainTask.EraseMode,
			EraseEdition:          mainTask.EraseEdition,
			OcrRectInfo:           mainTask.OcrRectInfo,
			VideoDuration:         float64(req.TargetDurationRangeType),
			BgmMode:               consts.BgmModeOn,
			SubtitleMode:          mainTask.SubtitleMode,
			NoSubtitleVideoUrl:    "",
			NoSubtitleVideoHlsUrl: "",
			MergedVideoUrl:        "",
			MergedVideoHlsUrl:     "",
			Size:                  0,
			Resolution:            mainTask.TargetResolution,
			CoverUrl:              "",
			CustomSubtitleStyle:   defaultSubtitleStyle,
			BgmUrl:                bgmUrl,
			CommentaryAgentRes:    nil,
			MaterialHighlightUrl:  "",
			HighlightEpisodesUrls: highlightEpisodesUrls,
			EndTagUrl:             "",
			SubtitleFileUrl:       "",
			NeedMerge:             1, // 默认需要合成
			SourceLangId:          "zh",
			TargetLangId:          mainTask.TargetLangId,
			CreatedAt:             now,
			UpdatedAt:             now,
		}

		subTasks = append(subTasks, subTask)
	}

	return subTasks, nil
}

// createDefaultSubtitleStyle 创建默认字幕样式
func (s *CommentaryMainTaskServiceImpl) createDefaultSubtitleStyle(resolution string) *common.CustomSubtitleStyle {
	// 解析分辨率，设置合适的参数
	width, height, fontSize, marginV, marginH := s.calculateSubtitleParams(resolution)

	return &common.CustomSubtitleStyle{
		Alignment:       2,                 // 居中对齐
		BackColour:      "&H00000000",      // 背景颜色
		Bold:            0,                 // 非粗体
		BorderStyle:     1,                 // 边框样式
		Encoding:        1,                 // 编码
		FontName:        "Alibaba Sans JP", // 字体名称
		FontSize:        fontSize,          // 根据分辨率计算的字体大小
		Italic:          0,                 // 非斜体
		MarginL:         marginH,           // 根据宽度计算的左边距
		MarginR:         marginH,           // 根据宽度计算的右边距
		MarginV:         marginV,           // 根据高度计算的垂直边距
		Outline:         0,                 // 轮廓
		OutlineColour:   "&H00000000",      // 轮廓颜色
		PrimaryColor:    "&H00FFFFFF",      // 主要颜色（白色）
		ScaleX:          100,               // X轴缩放
		ScaleY:          100,               // Y轴缩放
		SecondaryColour: "&H00000000",      // 次要颜色
		Shadow:          0,                 // 阴影
		Spacing:         0,                 // 间距
		Strikeout:       0,                 // 删除线
		Underline:       0,                 // 下划线
		Height:          height,            // 视频高度
		Width:           width,             // 视频宽度
		RowsLimit:       2,                 // 行数限制
		Rect: &common.Rect{
			X1: 0,      // 左上角X坐标
			Y1: 0,      // 左上角Y坐标
			X2: width,  // 右下角X坐标
			Y2: height, // 右下角Y坐标
		},
	}
}

// calculateSubtitleParams 根据分辨率计算字幕参数
func (s *CommentaryMainTaskServiceImpl) calculateSubtitleParams(resolution string) (width, height, fontSize, marginV, marginH int32) {
	// 设置默认值
	width, height = 720, 1280
	fontSize = 53
	marginV = 256
	marginH = 36

	// 根据分辨率调整参数
	switch resolution {
	case "720x1280":
		width, height = 720, 1280
		fontSize = 53
		marginV = 256
		marginH = 36
	case "1280x720":
		width, height = 1280, 720
		fontSize = 48
		marginV = 60
		marginH = 64
	}

	if marginV > height/3 {
		marginV = height / 3
	}
	if marginH > width/10 {
		marginH = width / 10
	}

	return width, height, fontSize, marginV, marginH
}

// selectBgmUrl 从背景音乐列表中选择一个URL
func (s *CommentaryMainTaskServiceImpl) selectBgmUrl(bgmSourceList []*vo.MainTaskCommonSourceVO) string {
	if len(bgmSourceList) == 0 {
		return ""
	}

	// 随机选择一个背景音乐
	rand.Seed(time.Now().UnixNano())
	selectedBgm := bgmSourceList[rand.Intn(len(bgmSourceList))]
	return selectedBgm.SourceUrl
}

// selectHighlightEpisodesUrls 从高光剧集源列表中选择一组URLs
func (s *CommentaryMainTaskServiceImpl) selectHighlightEpisodesUrls(highlightEpisodesSourceList []*vo.HighlightEpisodesSource) []string {
	if len(highlightEpisodesSourceList) == 0 {
		return []string{}
	}

	// 随机选择一个高光剧集源
	rand.Seed(time.Now().UnixNano())
	selectedSource := highlightEpisodesSourceList[rand.Intn(len(highlightEpisodesSourceList))]

	// 提取该源中的所有URL
	urls := make([]string, 0, len(selectedSource.SourceList))
	for _, source := range selectedSource.SourceList {
		if source.SourceUrl != "" {
			urls = append(urls, source.SourceUrl)
		}
	}

	return urls
}

// 工具函数：将任意类型 slice 转为 []interface{}
func toInterfaceSlice[T any](s []T) []interface{} {
	result := make([]interface{}, len(s))
	for i, v := range s {
		result[i] = v
	}
	return result
}

// GetCommentaryTaskHistory 获取解说任务信息
func (s *CommentaryMainTaskServiceImpl) GetCommentaryTaskHistory(ctx context.Context, req *bo.GetCommentaryTaskHistoryReqBO) ([]*bo.CommentaryTaskHistoryBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// 获取主任务信息
	filter := map[string][]interface{}{
		"tenant_id":      {req.TenantId},
		"biz_mode":       toInterfaceSlice(req.BizModeList),
		"target_lang_id": toInterfaceSlice(req.TargetLangIdList),
		"erase_edition":  toInterfaceSlice(req.EraseEditionList),
		"aspect_ratio":   toInterfaceSlice(req.AspectRatioList),
	}

	mainTaskDOs, err := s.commentaryMainTaskRepo.GetTasksByCond(ctx, filter, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("查询主任务失败: %w", err)
	}

	ret := []*bo.CommentaryTaskHistoryBO{}
	for _, mainTaskDO := range mainTaskDOs {
		// 获取子任务列表
		subTasksDO, err := s.commentarySubTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskDO.Id)
		if err != nil {
			return nil, fmt.Errorf("查询子任务失败: %w", err)
		}

		// 转换为 BO
		mainTaskBO := conv.CommentaryMainTaskDOToBO(mainTaskDO)
		subTasksBOs := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
		for i, subTaskDO := range subTasksDO {
			subTasksBOs[i] = conv.CommentarySubTaskDOToBO(subTaskDO)
		}
		ret = append(ret, &bo.CommentaryTaskHistoryBO{
			MainTask: mainTaskBO,
			SubTasks: subTasksBOs,
		})
	}

	return ret, nil
}

// GetTaskById 根据ID获取任务详情
func (s *CommentaryMainTaskServiceImpl) GetTaskById(ctx context.Context, id int64) (*bo.CommentaryMainTaskDetailBO, error) {
	if id <= 0 {
		return nil, fmt.Errorf("任务ID无效")
	}

	taskDO, err := s.commentaryMainTaskRepo.GetTaskById(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询任务失败: %w", err)
	}

	if taskDO == nil {
		return nil, fmt.Errorf("任务不存在")
	}

	// 获取子任务列表
	subTasksDO, err := s.commentarySubTaskRepo.GetSubTasksByMainTaskId(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询子任务失败: %w", err)
	}

	// 转换为 BO
	mainTaskBO := conv.CommentaryMainTaskDOToBO(taskDO)
	subTasksBOs := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
	for i, subTaskDO := range subTasksDO {
		subTasksBOs[i] = conv.CommentarySubTaskDOToBO(subTaskDO)
	}

	// 构建详情 BO
	detailBO := &bo.CommentaryMainTaskDetailBO{
		Id:                      mainTaskBO.Id,
		AppId:                   mainTaskBO.AppId,
		ApiKey:                  mainTaskBO.ApiKey,
		Name:                    mainTaskBO.Name,
		TenantId:                mainTaskBO.TenantId,
		TaskType:                mainTaskBO.TaskType,
		BizMode:                 mainTaskBO.BizMode,
		BgmMode:                 int(mainTaskBO.BgmMode),
		SubtitleMode:            mainTaskBO.SubtitleMode,
		EraseMode:               mainTaskBO.EraseMode,
		EraseEdition:            mainTaskBO.EraseEdition,
		OcrRectInfo:             mainTaskBO.OcrRectInfo,
		TargetLangId:            mainTaskBO.TargetLangId,
		TargetResolution:        mainTaskBO.TargetResolution,
		AspectRatio:             mainTaskBO.AspectRatio,
		TargetDurationRangeType: mainTaskBO.TargetDurationRangeType,
		TargetDurationRangeStr:  mainTaskBO.TargetDurationRangeStr,
		Status:                  mainTaskBO.Status,
		ErrMsg:                  mainTaskBO.ErrMsg,
		PayOrderId:              mainTaskBO.PayOrderId,
		TargetNumber:            mainTaskBO.TargetNumber,
		ClipMergeVideoUrl:       mainTaskBO.ClipMergeVideoUrl,
		VoiceIDList:             mainTaskBO.VoiceIDList,
		DubbingType:             mainTaskBO.DubbingType,
		RequestID:               mainTaskBO.RequestID,
		CreatedAt:               &mainTaskBO.CreatedAt,
		UpdatedAt:               &mainTaskBO.UpdatedAt,
		SubTasks:                subTasksBOs,
	}

	return detailBO, nil
}

// UpdateTaskStatus 更新任务状态
func (s *CommentaryMainTaskServiceImpl) UpdateTaskStatus(ctx context.Context, req *bo.UpdateTaskStatusReqBO) (*bo.UpdateTaskStatusResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if req.Id <= 0 {
		return nil, fmt.Errorf("任务ID无效")
	}

	if err := s.commentaryMainTaskRepo.UpdateTaskStatus(ctx, req.Id, consts.CommentaryMainTaskStatus(req.Status), req.ErrMsg); err != nil {
		return nil, fmt.Errorf("更新任务状态失败: %w", err)
	}

	g.Log().Infof(ctx, "更新任务状态成功, ID: %d, 状态: %d", req.Id, req.Status)
	return &bo.UpdateTaskStatusResBO{Success: true}, nil
}

// DeleteTask 删除任务
func (s *CommentaryMainTaskServiceImpl) DeleteTask(ctx context.Context, id int64) error {
	if id <= 0 {
		return fmt.Errorf("任务ID无效")
	}

	if err := s.commentaryMainTaskRepo.DeleteTask(ctx, id); err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	g.Log().Infof(ctx, "删除任务成功, ID: %d", id)
	return nil
}

// createSourceDetails 创建资源详情
func (s *CommentaryMainTaskServiceImpl) createSourceDetails(ctx context.Context, mainTask *do.CommentaryMainTask, req *bo.CommentaryMainTaskCreateReqBO) ([]*do.CommentarySourceDetail, error) {
	var sourceDetails []*do.CommentarySourceDetail
	now := time.Now()

	// 处理素材源列表
	if len(req.MaterialSourceList) > 0 {
		for _, material := range req.MaterialSourceList {
			// 生成资源详情ID
			sourceDetailId, err := id_generator.GenerateId()
			if err != nil {
				return nil, fmt.Errorf("生成资源详情ID失败: %w", err)
			}

			// 根据 from_type 设置审核状态
			auditStatus := consts.AuditStatusPending                      // 默认待审核
			if material.FromType == consts.SourceFromTypeVideoTranslate { // 视频翻译
				auditStatus = consts.AuditStatusSuccess // 审核通过
			}
			sourceDetail := &do.CommentarySourceDetail{
				Id:             sourceDetailId,
				MainTaskId:     mainTask.Id,
				Name:           material.Name,
				TenantId:       mainTask.TenantId,
				BizType:        consts.SourceBizTypeCommentary, // 解说类型
				SourceFileType: material.SourceFileType,
				GroupId:        0, // 分组ID，用于区分不同的资源组
				SourceUrl:      obs.GetCdnUrlByObjectName(material.SourceUrl),
				AuditStatus:    auditStatus,
				CreatedAt:      now,
				UpdatedAt:      now,
				Duration:       10,
			}
			sourceDetails = append(sourceDetails, sourceDetail)
		}
	}

	// 处理高光剧集源列表
	if len(req.HighlightEpisodesSourceList) > 0 {
		for groupIdx, highlightGroup := range req.HighlightEpisodesSourceList {
			for _, source := range highlightGroup.SourceList {
				// 生成资源详情ID
				sourceDetailId, err := id_generator.GenerateId()
				if err != nil {
					return nil, fmt.Errorf("生成资源详情ID失败: %w", err)
				}

				// 根据 from_type 设置审核状态
				auditStatus := consts.AuditStatusPending                    // 默认待审核
				if source.FromType == consts.SourceFromTypeVideoTranslate { // 视频翻译
					auditStatus = consts.AuditStatusSuccess // 审核通过
				}

				sourceDetail := &do.CommentarySourceDetail{
					Id:             sourceDetailId,
					MainTaskId:     mainTask.Id,
					Name:           source.Name,
					TenantId:       mainTask.TenantId,
					BizType:        consts.SourceBizTypeHighlight, // 高光类型
					SourceFileType: source.SourceFileType,
					GroupId:        int64(groupIdx + 1), // 分组ID
					SourceUrl:      obs.GetCdnUrlByObjectName(source.SourceUrl),
					AuditStatus:    auditStatus,
					CreatedAt:      now,
					UpdatedAt:      now,
					Duration:       10,
				}
				sourceDetails = append(sourceDetails, sourceDetail)
			}
		}
	}

	// 处理结尾标签源列表
	if len(req.EndTagSourceList) > 0 {
		for _, endTag := range req.EndTagSourceList {
			// 生成资源详情ID
			sourceDetailId, err := id_generator.GenerateId()
			if err != nil {
				return nil, fmt.Errorf("生成资源详情ID失败: %w", err)
			}

			// 根据 from_type 设置审核状态
			auditStatus := consts.AuditStatusPending                    // 默认待审核
			if endTag.FromType == consts.SourceFromTypeVideoTranslate { // 视频翻译
				auditStatus = consts.AuditStatusSuccess // 审核通过
			}

			sourceDetail := &do.CommentarySourceDetail{
				Id:             sourceDetailId,
				MainTaskId:     mainTask.Id,
				Name:           endTag.Name,
				TenantId:       mainTask.TenantId,
				BizType:        consts.SourceBizTypeFallback, // 落版类型
				SourceFileType: endTag.SourceFileType,
				GroupId:        0, // 结尾标签源不需要分组ID
				SourceUrl:      obs.GetCdnUrlByObjectName(endTag.SourceUrl),
				AuditStatus:    auditStatus,
				CreatedAt:      now,
				UpdatedAt:      now,
			}
			sourceDetails = append(sourceDetails, sourceDetail)
		}
	}

	// 处理背景音源列表
	if len(req.BgmSourceList) > 0 {
		for _, bgm := range req.BgmSourceList {
			// 生成资源详情ID
			sourceDetailId, err := id_generator.GenerateId()
			if err != nil {
				return nil, fmt.Errorf("生成资源详情ID失败: %w", err)
			}

			// 根据 from_type 设置审核状态
			auditStatus := consts.AuditStatusPending // 默认待审核

			sourceDetail := &do.CommentarySourceDetail{
				Id:             sourceDetailId,
				MainTaskId:     mainTask.Id,
				Name:           bgm.Name,
				TenantId:       mainTask.TenantId,
				BizType:        consts.SourceBizTypeCommentary, // 解说类型（BGM属于解说的一部分）
				SourceFileType: bgm.SourceFileType,
				GroupId:        0, // 背景音源不需要分组ID
				SourceUrl:      obs.GetCdnUrlByObjectName(bgm.SourceUrl),
				AuditStatus:    auditStatus,
				CreatedAt:      now,
				UpdatedAt:      now,
			}
			sourceDetails = append(sourceDetails, sourceDetail)
		}
	}

	return sourceDetails, nil
}

// checkTenantRights 检查租户权益
func (s *CommentaryMainTaskServiceImpl) checkTenantRights(ctx context.Context, req *bo.CommentaryMainTaskCreateReqBO) bool {
	// 通过omni-tenant服务获取租户权益信息
	tenantClient := omni_engine.GetOmniTenantClient()
	tenantBonusReq := &omniTenant.GetTenantBonusReq{
		AppId:    req.AppId,
		TenantId: req.TenantId,
	}

	tenantBonusResp, err := tenantClient.GetTenantBonus(ctx, tenantBonusReq)
	if err != nil {
		g.Log().Errorf(ctx, "获取租户权益信息失败: %v", err)
		return false
	}

	if tenantBonusResp.TenantBonus == nil {
		g.Log().Errorf(ctx, "租户权益信息为空")
		return false
	}

	// 根据任务类型检查相应权益
	switch req.TaskType {
	case consts.TaskTypeHighlight: // 高光任务 - 检查高光剪辑权益
		if tenantBonusResp.TenantBonus.GetEnableHighlightEditingRight() != 1 {
			g.Log().Warningf(ctx, "租户没有高光剪辑权益，tenant_id: %d, app_id: %d", req.TenantId, req.AppId)
			return false
		}
		g.Log().Infof(ctx, "高光剪辑权益检查通过，tenant_id: %d", req.TenantId)
	case consts.TaskTypeCommentary: // 解说任务 - 检查高光解说权益
		if tenantBonusResp.TenantBonus.GetEnableHighlightCommentaryRight() != 1 {
			g.Log().Warningf(ctx, "租户没有高光解说权益，tenant_id: %d, app_id: %d", req.TenantId, req.AppId)
			return false
		}
		g.Log().Infof(ctx, "高光解说权益检查通过，tenant_id: %d", req.TenantId)
	default:
		// 未知任务类型
		g.Log().Errorf(ctx, "未知的任务类型: %d", req.TaskType)
		return false
	}

	g.Log().Infof(ctx, "权益检查通过，任务类型: %d, 业务模式: %d", req.TaskType, req.BizMode)
	return true
}

// checkBalanceBeforeTransaction 在事务开始前检查余额是否充足
func (s *CommentaryMainTaskServiceImpl) checkBalanceBeforeTransaction(ctx context.Context, req *bo.CommentaryMainTaskCreateReqBO, sourceDetails []*do.CommentarySourceDetail, subTasks []*do.CommentarySubTask) error {
	g.Log().Debugf(ctx, "test: %+v", req)
	// 1. 获取价格预估
	estimatedCost, err := s.getEstimatedCost(ctx, req, sourceDetails, subTasks)
	if err != nil {
		g.Log().Errorf(ctx, "获取价格预估失败: %v", err)
		return fmt.Errorf("获取价格预估失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", estimatedCost)

	// 2. 获取租户余额
	balance, err := s.getTenantBalance(ctx, req.AppId, req.TenantId)
	if err != nil {
		g.Log().Errorf(ctx, "获取租户余额失败: %v", err)
		return fmt.Errorf("获取租户余额失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", balance)

	// 3. 检查余额是否充足
	if balance < estimatedCost {
		g.Log().Errorf(ctx, "余额不足，当前余额: %d, 预估费用: %d, tenant_id: %d, app_id: %d", balance, estimatedCost, req.TenantId, req.AppId)
		return fmt.Errorf("余额不足，当前余额: %d, 预估费用: %d", balance, estimatedCost)
	}
	g.Log().Infof(ctx, "余额检查通过，当前余额: %d, 预估费用: %d, tenant_id: %d, app_id: %d", balance, estimatedCost, req.TenantId, req.AppId)
	return nil
}

// getEstimatedCost 获取视频解说费用预估
func (s *CommentaryMainTaskServiceImpl) getEstimatedCost(ctx context.Context, req *bo.CommentaryMainTaskCreateReqBO, sourceDetails []*do.CommentarySourceDetail, subTasks []*do.CommentarySubTask) (int64, error) {
	// 计算原素材总时长
	var originalDuration int64
	for _, detail := range sourceDetails {
		originalDuration += int64(detail.Duration)
	}
	g.Log().Debugf(ctx, "test: %+v", originalDuration)

	// 计算预期片段时长
	var expectedSegmentDurations []int64
	for _, subTask := range subTasks {
		expectedSegmentDurations = append(expectedSegmentDurations, int64(subTask.VideoDuration))
	}
	g.Log().Debugf(ctx, "test: %+v", expectedSegmentDurations)

	// 确定业务类型（与扣款逻辑保持一致）
	OpenapiBusinessType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	if req.TaskType == consts.TaskTypeCommentary {
		OpenapiBusinessType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	}

	// 构建价格预估请求
	estimateReq := &omniBalance.AVoiceVideoCommentaryEstimateCostRequest{
		AppId:                    req.AppId,
		TenantId:                 req.TenantId,
		OpenapiBusinessType:      int32(OpenapiBusinessType), // 业务场景类型
		OriginalDuration:         originalDuration,
		ExpectedSegmentDurations: expectedSegmentDurations,
		EraseType:                int32(req.EraseEdition), // 擦除版本
		EraseMode:                int32(req.EraseMode),    // 擦除模式
	}
	g.Log().Debugf(ctx, "test: %+v", estimateReq)

	// 调用价格预估接口
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	estimateResp, err := balanceClient.AVoiceVideoCommentaryEstimateCost(ctx, estimateReq)
	if err != nil {
		g.Log().Errorf(ctx, "调用价格预估接口失败: %v", err)
		return 0, fmt.Errorf("调用价格预估接口失败: %w", err)
	}
	g.Log().Debugf(ctx, "test: %+v", estimateResp)

	g.Log().Infof(ctx, "价格预估成功，总预估费用: %d, 各片段预估费用: %v", estimateResp.TotalEstimatedCost, estimateResp.SegmentEstimatedCosts)
	return estimateResp.TotalEstimatedCost, nil
}

// getTenantBalance 获取租户余额
func (s *CommentaryMainTaskServiceImpl) getTenantBalance(ctx context.Context, appId, tenantId int64) (int64, error) {
	// 构建余额查询请求
	balanceReq := &omniBalance.AVoiceGetTenantBalanceRequest{
		AppId:       appId,
		TenantId:    tenantId,
		BalanceType: 1, // 假设1为视频解说业务的余额类型，具体值需要根据业务确认
	}

	// 调用余额查询接口
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	balanceResp, err := balanceClient.AVoiceGetTenantBalance(ctx, balanceReq)
	if err != nil {
		g.Log().Errorf(ctx, "调用余额查询接口失败: %v", err)
		return 0, fmt.Errorf("调用余额查询接口失败: %w", err)
	}

	// 计算总余额
	var totalBalance int64
	for _, balance := range balanceResp.TenantBalanceList {
		totalBalance += balance.Balance
	}

	g.Log().Infof(ctx, "余额查询成功，总余额: %d, 余额详情: %v", totalBalance, balanceResp.TenantBalanceList)
	return totalBalance, nil
}

// BatchGetCommentaryTasksByOrderIds 根据订单ID批量获取解说任务
func (s *CommentaryMainTaskServiceImpl) BatchGetCommentaryTasksByOrderIds(ctx context.Context, req *bo.BatchGetCommentaryTasksByOrderIdsReqBO) (*bo.BatchGetCommentaryTasksByOrderIdsResBO, error) {
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	if len(req.OrderIds) == 0 {
		return &bo.BatchGetCommentaryTasksByOrderIdsResBO{
			Tasks: []*bo.CommentaryTaskDetailsBO{},
		}, nil
	}

	// 根据订单ID批量查询主任务
	mainTaskDOs, err := s.commentaryMainTaskRepo.GetTasksByOrderIds(ctx, req.OrderIds)
	if err != nil {
		return nil, fmt.Errorf("查询主任务失败: %w", err)
	}

	// 构建任务详情列表
	taskDetailsList := make([]*bo.CommentaryTaskDetailsBO, 0, len(mainTaskDOs))
	for _, mainTaskDO := range mainTaskDOs {
		// 获取子任务列表
		subTasksDO, err := s.commentarySubTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskDO.Id)
		if err != nil {
			g.Log().Errorf(ctx, "查询子任务失败: %v", err)
			continue
		}

		// 转换为 BO
		mainTaskBO := conv.CommentaryMainTaskDOToBO(mainTaskDO)
		subTasksBOs := make([]*bo.CommentarySubTaskBO, len(subTasksDO))
		for i, subTaskDO := range subTasksDO {
			subTasksBOs[i] = conv.CommentarySubTaskDOToBO(subTaskDO)
		}

		// 构建任务详情
		taskDetails := &bo.CommentaryTaskDetailsBO{
			OrderId:    mainTaskBO.PayOrderId,
			MainTask:   mainTaskBO,
			SubTasks:   subTasksBOs,
			TaskStatus: string(mainTaskBO.Status),
		}

		taskDetailsList = append(taskDetailsList, taskDetails)
	}

	return &bo.BatchGetCommentaryTasksByOrderIdsResBO{
		Tasks: taskDetailsList,
	}, nil
}
