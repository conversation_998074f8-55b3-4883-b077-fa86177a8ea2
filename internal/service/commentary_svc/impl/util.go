package impl

import (
	"business-workflow/internal/common/redis"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/dist_lock"
)

func lockEditSubtitle(ctx context.Context, taskId int64) (*dist_lock.RenewalLock, *uerrors.CommentaryErr) {
	uuidValue := uuid.New().String()
	lock := dist_lock.NewRenewalLock(60, 10, fmt.Sprintf(consts.LockKeyEditSubtitleLock, taskId), uuidValue, redis.GetClient())
	lockErr := lock.Lock(60 * 1000)
	// 加锁失败则返回
	if lockErr != nil {
		g.Log().Infof(ctx, "get edit subtitle lock fail, task is editing, taskId:%d", taskId)
		return nil, uerrors.ErrBusy
	}
	g.Log().Infof(ctx, "get edit subtitle lock success, taskId:%d", taskId)
	return lock, nil
}
