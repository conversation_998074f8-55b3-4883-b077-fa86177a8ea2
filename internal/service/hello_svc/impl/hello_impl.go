package impl

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/repo/hello_repo"
	"business-workflow/internal/service/hello_svc"
	"business-workflow/internal/temporal"
	"business-workflow/internal/util/id_generator"
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"go.temporal.io/sdk/client"
)

type HelloSvcImpl struct {
	helloRepo hello_repo.IHelloRepo
	rdb       *redis.Client
}

func NewHelloSvcImpl(helloRepo hello_repo.IHelloRepo, rdb *redis.Client) hello_svc.IHelloSvc {
	return &HelloSvcImpl{helloRepo: helloRepo, rdb: rdb}
}

func (impl *HelloSvcImpl) Hello(ctx context.Context, bo *bo.HelloBO) error {
	return impl.helloRepo.Hello(ctx, conv.HelloBO2DO(bo))
}

func (impl *HelloSvcImpl) Dubbing(ctx context.Context, dubbingBO *bo.DubbingBO) (*bo.DubbingBORet, error) {
	reqId, ok := ctx.Value(trace.ReqId).(string)
	if !ok {
		reqId = uuid.NewString()
	}
	// 启动工作流
	mainTask := &bo.CommentaryMainTaskBO{
		Id:           id_generator.GenerateIdNotStrict(),
		TargetNumber: dubbingBO.Concurrency,
		RequestID:    reqId,
	}

	// 创建子任务列表
	subTaskList := []*bo.CommentarySubTaskBO{
		{
			Id: id_generator.GenerateIdNotStrict(),
		},
		{
			Id: id_generator.GenerateIdNotStrict(),
		},
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:        fmt.Sprintf("%v", mainTask.Id),
		TaskQueue: config.GetConfig().Temporal.Queue,
	}
	g.Log().Infof(ctx, "start workflow, task: %+v", mainTask)
	workflowRun, err := temporal.TemporalClient.ExecuteWorkflow(ctx, workflowOptions, "CommentaryWorkflow", mainTask, subTaskList)
	if err != nil {
		return nil, err
	}

	ret := &bo.DubbingBORet{
		RunID:      workflowRun.GetID(),
		WorkflowID: workflowRun.GetRunID(),
	}
	return ret, nil
}
