package impl

import (
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/mocks/mock_hello_repo"
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"
)

func Test_Hello(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockHelloRepo := mock_hello_repo.NewMockIHelloRepo(ctrl)
	svc := NewHelloSvcImpl(mockHelloRepo, nil)
	Convey("test hello", t, func() {
		mockHelloRepo.EXPECT().Hello(gomock.Any(), gomock.Any()).Return(nil)
		err := svc.Hello(context.Background(), &bo.HelloBO{})
		So(err, ShouldBeNil)
	})
}
