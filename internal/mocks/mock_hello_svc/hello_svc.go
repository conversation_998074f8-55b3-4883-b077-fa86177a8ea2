// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/service/hello_svc/hello_svc.go

// Package mock_hello_svc is a generated GoMock package.
package mock_hello_svc

import (
	context "context"
	bo "business-workflow/internal/entity/bo"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIHelloSvc is a mock of IHelloSvc interface.
type MockIHelloSvc struct {
	ctrl     *gomock.Controller
	recorder *MockIHelloSvcMockRecorder
}

// MockIHelloSvcMockRecorder is the mock recorder for MockIHelloSvc.
type MockIHelloSvcMockRecorder struct {
	mock *MockIHelloSvc
}

// NewMockIHelloSvc creates a new mock instance.
func NewMockIHelloSvc(ctrl *gomock.Controller) *MockIHelloSvc {
	mock := &MockIHelloSvc{ctrl: ctrl}
	mock.recorder = &MockIHelloSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHelloSvc) EXPECT() *MockIHelloSvcMockRecorder {
	return m.recorder
}

// Hello mocks base method.
func (m *MockIHelloSvc) Hello(ctx context.Context, bo *bo.HelloBO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Hello", ctx, bo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Hello indicates an expected call of Hello.
func (mr *MockIHelloSvcMockRecorder) Hello(ctx, bo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Hello", reflect.TypeOf((*MockIHelloSvc)(nil).Hello), ctx, bo)
}
