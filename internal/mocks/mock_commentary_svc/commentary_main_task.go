// Code generated by MockGen. DO NOT EDIT.
// Source: internal/service/commentary_svc/icommentary_main_task.go

// Package mock_commentary_svc is a generated GoMock package.
package mock_commentary_svc

import (
	vo "business-workflow/internal/entity/vo"
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockICommentaryMainTaskService is a mock of ICommentaryMainTaskService interface.
type MockICommentaryMainTaskService struct {
	ctrl     *gomock.Controller
	recorder *MockICommentaryMainTaskServiceMockRecorder
}

// MockICommentaryMainTaskServiceMockRecorder is the mock recorder for MockICommentaryMainTaskService.
type MockICommentaryMainTaskServiceMockRecorder struct {
	mock *MockICommentaryMainTaskService
}

// NewMockICommentaryMainTaskService creates a new mock instance.
func NewMockICommentaryMainTaskService(ctrl *gomock.Controller) *MockICommentaryMainTaskService {
	mock := &MockICommentaryMainTaskService{ctrl: ctrl}
	mock.recorder = &MockICommentaryMainTaskServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICommentaryMainTaskService) EXPECT() *MockICommentaryMainTaskServiceMockRecorder {
	return m.recorder
}

// CreateTask mocks base method.
func (m *MockICommentaryMainTaskService) CreateTask(ctx context.Context, req *vo.CommentaryMainTaskCreateReq) (*vo.CommentaryMainTaskCreateRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, req)
	ret0, _ := ret[0].(*vo.CommentaryMainTaskCreateRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockICommentaryMainTaskServiceMockRecorder) CreateTask(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockICommentaryMainTaskService)(nil).CreateTask), ctx, req)
}

// DeleteTask mocks base method.
func (m *MockICommentaryMainTaskService) DeleteTask(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTask", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTask indicates an expected call of DeleteTask.
func (mr *MockICommentaryMainTaskServiceMockRecorder) DeleteTask(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTask", reflect.TypeOf((*MockICommentaryMainTaskService)(nil).DeleteTask), ctx, id)
}

// GetCommentaryTaskHistory mocks base method.
func (m *MockICommentaryMainTaskService) GetCommentaryTaskHistory(ctx context.Context, req *vo.GetCommentaryTaskHistoryReq) (*vo.GetCommentaryTaskInfoRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommentaryTaskHistory", ctx, req)
	ret0, _ := ret[0].(*vo.GetCommentaryTaskInfoRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCommentaryTaskHistory indicates an expected call of GetCommentaryTaskHistory.
func (mr *MockICommentaryMainTaskServiceMockRecorder) GetCommentaryTaskHistory(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommentaryTaskHistory", reflect.TypeOf((*MockICommentaryMainTaskService)(nil).GetCommentaryTaskHistory), ctx, req)
}

// GetTaskById mocks base method.
func (m *MockICommentaryMainTaskService) GetTaskById(ctx context.Context, id int64) (*vo.CommentaryMainTaskVO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskById", ctx, id)
	ret0, _ := ret[0].(*vo.CommentaryMainTaskVO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskById indicates an expected call of GetTaskById.
func (mr *MockICommentaryMainTaskServiceMockRecorder) GetTaskById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskById", reflect.TypeOf((*MockICommentaryMainTaskService)(nil).GetTaskById), ctx, id)
}

// UpdateTaskStatus mocks base method.
func (m *MockICommentaryMainTaskService) UpdateTaskStatus(ctx context.Context, id int64, status int, errMsg string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatus", ctx, id, status, errMsg)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatus indicates an expected call of UpdateTaskStatus.
func (mr *MockICommentaryMainTaskServiceMockRecorder) UpdateTaskStatus(ctx, id, status, errMsg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatus", reflect.TypeOf((*MockICommentaryMainTaskService)(nil).UpdateTaskStatus), ctx, id, status, errMsg)
}
