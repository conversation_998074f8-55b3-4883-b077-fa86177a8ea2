// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/repo/hello_repo/hello_repo.go

// Package mock_hello_repo is a generated GoMock package.
package mock_hello_repo

import (
	context "context"
	do "business-workflow/internal/entity/do"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIHelloRepo is a mock of IHelloRepo interface.
type MockIHelloRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIHelloRepoMockRecorder
}

// MockIHelloRepoMockRecorder is the mock recorder for MockIHelloRepo.
type MockIHelloRepoMockRecorder struct {
	mock *MockIHelloRepo
}

// NewMockIHelloRepo creates a new mock instance.
func NewMockIHelloRepo(ctrl *gomock.Controller) *MockIHelloRepo {
	mock := &MockIHelloRepo{ctrl: ctrl}
	mock.recorder = &MockIHelloRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIHelloRepo) EXPECT() *MockIHelloRepoMockRecorder {
	return m.recorder
}

// Hello mocks base method.
func (m *MockIHelloRepo) Hello(ctx context.Context, do *do.HelloDO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Hello", ctx, do)
	ret0, _ := ret[0].(error)
	return ret0
}

// Hello indicates an expected call of Hello.
func (mr *MockIHelloRepoMockRecorder) Hello(ctx, do interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Hello", reflect.TypeOf((*MockIHelloRepo)(nil).Hello), ctx, do)
}
