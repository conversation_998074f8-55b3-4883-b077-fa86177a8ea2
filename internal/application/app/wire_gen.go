// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package app

import (
	"business-workflow/internal/application/svc"
	"github.com/google/wire"
)

// Injectors from wire.go:

//go:generate wire
func NewApp() (*App, error) {
	service, err := svc.NewSvc()
	if err != nil {
		return nil, err
	}
	app := &App{
		Svc: service,
	}
	return app, nil
}

// wire.go:

var appSet = wire.NewSet(svc.NewSvc, wire.Struct(new(App), "*"))
