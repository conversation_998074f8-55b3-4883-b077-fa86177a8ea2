// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package svc

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/redis"
	"business-workflow/internal/repo/commentary_repo/impl"
	impl2 "business-workflow/internal/service/commentary_svc/impl"
	"github.com/google/wire"
)

// Injectors from wire.go:

//go:generate wire
func NewSvc() (*Service, error) {
	iCommentaryMainTaskRepo := impl.NewCommentaryMainTaskRepoImpl()
	iCommentarySubTaskRepo := impl.NewCommentarySubTaskRepoImpl()
	iCommentarySubtitleItemRepo := impl.NewCommentarySubtitleItemRepoImpl()
	iCommentarySourceDetailRepo := impl.NewCommentarySourceDetailRepoImpl()
	iCommentaryMainTaskService := impl2.NewCommentaryMainTaskServiceImpl(iCommentaryMainTaskRepo, iCommentarySubTaskRepo, iCommentarySubtitleItemRepo, iCommentarySourceDetailRepo)
	iCommentarySubTaskService := impl2.NewCommentarySubTaskServiceImpl(iCommentarySubTaskRepo)
	iCommentarySubtitleItemService := impl2.NewCommentarySubtitleItemServiceImpl(iCommentarySubTaskService)
	service := &Service{
		CommentaryMainTaskService:     iCommentaryMainTaskService,
		CommentarySubTaskService:      iCommentarySubTaskService,
		CommentarySubtitleItemService: iCommentarySubtitleItemService,
	}
	return service, nil
}

// wire.go:

var svcSet = wire.NewSet(impl2.NewCommentaryMainTaskServiceImpl, impl2.NewCommentarySubTaskServiceImpl, impl2.NewCommentarySubtitleItemServiceImpl, impl.NewCommentaryMainTaskRepoImpl, impl.NewCommentarySubTaskRepoImpl, impl.NewCommentarySubtitleItemRepoImpl, impl.NewCommentarySourceDetailRepoImpl, db.GetDB, redis.GetClient, wire.Struct(new(Service), "*"))
