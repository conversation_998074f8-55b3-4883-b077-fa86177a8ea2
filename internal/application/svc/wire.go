//go:build wireinject
// +build wireinject

package svc

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/common/redis"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	commentary_svc_impl "business-workflow/internal/service/commentary_svc/impl"
	"github.com/google/wire"
)

var svcSet = wire.NewSet(
	commentary_svc_impl.NewCommentaryMainTaskServiceImpl,
	commentary_svc_impl.NewCommentarySubTaskServiceImpl,
	commentary_svc_impl.NewCommentarySubtitleItemServiceImpl,
	commentary_repo_impl.NewCommentaryMainTaskRepoImpl,
	commentary_repo_impl.NewCommentarySubTaskRepoImpl,
	commentary_repo_impl.NewCommentarySubtitleItemRepoImpl,
	commentary_repo_impl.NewCommentarySourceDetailRepoImpl,
	db.GetDB,
	redis.GetClient,
	wire.Struct(new(Service), "*"),
)

//go:generate wire
func NewSvc() (*Service, error) {
	wire.Build(svcSet)
	return &Service{}, nil
}
