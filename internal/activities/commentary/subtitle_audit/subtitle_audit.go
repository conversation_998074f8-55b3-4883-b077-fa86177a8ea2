package subtitle_audit

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"go.temporal.io/sdk/temporal"

	"github.com/gogf/gf/v2/frame/g"
)

func SubtitleAduitProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "SubtitleAuditProcess task:%v, sub_task:%+v", task, subTask)
	// 直接出的 不用审核
	// 后续用户上传的srt 才审核
	repoImpl := impl.NewCommentarySubTaskRepoImpl()
	err := repoImpl.UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"audit_status": consts.AuditStatusSuccess,
	})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskFields err",
			"oss error",
			err)
	}
	return task, nil
}
