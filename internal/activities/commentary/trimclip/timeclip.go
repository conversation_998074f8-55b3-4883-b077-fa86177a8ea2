package trimclip

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	engine_task_impl "business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/tyr/x/utils"
	"go.temporal.io/sdk/temporal"
)

type TrimClipActivity struct {
	subTaskRepo    commentary_repo.ICommentarySubTaskRepo
	itemsRepo      commentary_repo.ICommentarySubtitleItemRepo
	engineTaskRepo enginetaskrepo.IEngineTaskRepo
}

func (a *TrimClipActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	return false
}
func (a *TrimClipActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	return nil
}
func (a *TrimClipActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	_, err := a.subTaskRepo.UpdateSubTaskStatusByMainTaskId(ctx, task.Id, consts.SubTaskStatusFailed, "trimclip error ")

	return temporal.NewNonRetryableApplicationError("trimclip error", "concat error", err)
}
func (a *TrimClipActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	prefix := fmt.Sprintf("trimclip_%d_%d", task.Id, subTask.Id)
	mainRepo := impl.NewCommentaryMainTaskRepoImpl()
	mainTask, err := mainRepo.GetTaskById(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetTaskById error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"GetTaskById err",
			"db error",
			err)
	}
	//
	g.Log().Info(ctx, "clipUrl:%s", mainTask.ClipMergeVideoUrl)
	// 下载视频
	clipVideoPath := util.GenLocalPathWithPrefix(prefix, uuid.New().String(), ".mp4")
	objectKey, err := obs.GetObjectNameByHttpsUrl(mainTask.ClipMergeVideoUrl)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"obs error",
			"GetObjectNameByHttpsUrl err",
			err)
	}
	err = obs.GetOsClient().DownloadFile(objectKey, clipVideoPath)
	if err != nil {
		return nil, temporal.NewApplicationError(
			"TrimClipProcess DownloadFile err",
			"DownloadFile error",
			err)
	}

	// 获取字幕 译文裁剪相对时间

	items, err := a.itemsRepo.GetSubtitleItemsBySubTaskId(ctx, subTask.Id)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubtitleItemsBySubTaskId err",
			"db error",
			err)
	}
	// 进行裁剪
	cutFiles := make([]string, 0)
	uploadUrls := make([]string, 0)
	for _, item := range items {
		//逐个裁剪
		outFilePath := util.GenLocalPathWithPrefix(prefix, uuid.New().String(), ".mp4")
		cutFiles = append(cutFiles, outFilePath)
		err := ffmpeg.CutVideo(clipVideoPath, util.FfmpegTimeStr2Str(item.ClipSubtitleStartStr), util.FfmpegTimeStr2Str(item.ClipSubtitleEndStr), outFilePath)
		if err != nil {
			g.Log().Errorf(ctx, "CutVideo error:%v", err)
			return nil, temporal.NewNonRetryableApplicationError(
				"CutVideo err",
				"ffmpeg error",
				err)
		}
		// 上传
		key := obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "trimclip", ".mp4", "public")
		fileUrl, err := obs.GetOsClient().UploadFile(outFilePath, key, acl.AclTypePublicRead)
		if err != nil {
			return nil, temporal.NewApplicationError(
				"UploadFile err",
				"oss error",
				err)
		}
		uploadUrls = append(uploadUrls, fileUrl)
	}
	// 合并
	mergeVideoParams := &common.VideoParam{
		VideoInfos: lo.Map(uploadUrls, func(detail string, _ int) *common.VideoInfo {
			return &common.VideoInfo{
				ID:             id_generator.GenerateIdNotStrict(),
				OriginVideoUrl: detail,
			}
		}),
		Resolution:    task.TargetResolution,
		FPS:           30,
		CRF:           23,
		Mode:          1,
		MergeVideoUrl: obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "merge", ".mp4", "public"),
	}

	req := &common.VideoTranscodingRequest{
		VideoParams: []*common.VideoParam{mergeVideoParams},
	}
	totalCost := lo.SumBy(cutFiles, func(url string) float64 {
		duration, err := ffmpeg.ProbeDuration(url)
		if err != nil {
			g.Log().Errorf(ctx, "ProbeDuration failed, err: %v", err)
			return 0
		}
		return duration
	})
	// 无字幕和有字幕的时长是一样的
	g.Log().Infof(ctx, "TrimClipActivity HandleSubmit req:%v", req)
	businessScenariosType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	if task.TaskType == consts.TaskTypeHighlight {
		businessScenariosType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	}
	curTaskInfo := &pb.TaskInfo{
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY,
		BusinessScenariosType: int32(businessScenariosType),
		CommonInputContent:    utils.ToJson(req),
		TaskType:              pb.TaskType_VIDEO_TRANSCODING,
		TaskCost:              totalCost,
		RefererId:             task.Id,
	}
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(pb.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", totalCost),
		})
	// 构建单个任务请求
	createReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{curTaskInfo},
	}

	// 创建任务
	g.Log().Infof(ctx, "creating trimclip  task req:%v", createReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create trimclip  task err:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"create trimclip  task err",
			"create trimclip  task err",
			err)
	}

	// 写入业务task表
	engineTask := &do.BusinessEngineTask{
		EngineTaskId: createRes.TaskIds[0],
		EngineStatus: consts.EngineWorkflowStatusInit,
	}

	err = a.engineTaskRepo.CreateWorkflowEngineTask(ctx, engineTask)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetWorkflowEngineTask error",
			err)
	}
	return []int64{engineTask.EngineTaskId}, nil

}
func (a *TrimClipActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Infof(ctx, "TrimClipActivity HandleResult:%v", result)

	one := result[0]
	detail, err := one.TaskInfoConvertPB()
	if err != nil {
		g.Log().Errorf(ctx, "TaskInfoConvertPB err:%v", err)
		return task, err
	}

	switch detail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		return a.handleSuccess(ctx, task, subTask, detail)
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return a.handleFailure(ctx, task, detail)
	}

	return task, nil
}

func (a *TrimClipActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, detail *pb.Task) (*bo.CommentaryMainTaskBO, error) {

	req := &common.VideoTranscodingRequest{}
	err := json.Unmarshal([]byte(detail.CommonInputContent), req)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}

	items, err := a.itemsRepo.GetSubtitleItemsBySubTaskId(ctx, subTask.Id)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubtitleItemsBySubTaskId err",
			"db error",
			err)
	}
	var total int64
	// 准备批量更新数据
	updateDataList := make([]struct {
		ID     int64
		Fields map[string]interface{}
	}, 0, len(items))

	// 写入字幕译文时间
	for _, item := range items {
		subtitleStartTime, err := util.SrtStringTime2Ms(item.ClipSubtitleStartStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms err:%v", err)
			return nil, err
		}
		subtitleEndtime, err := util.SrtStringTime2Ms(item.ClipSubtitleEndStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms err:%v", err)
			return nil, err
		}
		originStartTime, err := util.SrtStringTime2Ms(item.ClipOriginSubtitleStartStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms err:%v", err)
			return nil, err
		}
		originEndTime, err := util.SrtStringTime2Ms(item.ClipOriginSubtitleEndStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms err:%v", err)
			return nil, err
		}

		// 计算原文时长
		originDuration := originEndTime - originStartTime
		// 计算译文时长
		subtitleDuration := subtitleEndtime - subtitleStartTime

		// 设置新的开始时间
		newStart := total
		// 使用译文的实际时长来计算结束时间
		newEnd := total + subtitleDuration
		total = newEnd

		// 转换为字符串格式
		newSubtitleStartStr := util.SrtTimeMs2String(newStart)
		newSubtitleEndStr := util.SrtTimeMs2String(newEnd)
		newOriginStartStr := util.SrtTimeMs2String(newStart)
		newOriginEndStr := util.SrtTimeMs2String(newStart + originDuration)

		// 准备更新字段
		updateFields := map[string]interface{}{
			"subtitle_start_str":        newSubtitleStartStr,
			"subtitle_end_str":          newSubtitleEndStr,
			"origin_subtitle_start_str": newOriginStartStr,
			"origin_subtitle_end_str":   newOriginEndStr,
		}

		updateDataList = append(updateDataList, struct {
			ID     int64
			Fields map[string]interface{}
		}{
			ID:     item.Id,
			Fields: updateFields,
		})
	}

	// 批量更新字幕项的时间信息
	err = a.itemsRepo.UpdateSubtitleItemFieldsBatch(ctx, updateDataList)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubtitleItemFieldsBatch err:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubtitleItemFieldsBatch err",
			"db error",
			err)
	}
	mergeUrl := obs.GetCdnUrlByObjectName(req.VideoParams[0].MergeVideoUrl)
	duration, err := ffmpeg.ProbeDuration(mergeUrl)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"ProbeDuration err",
			"ffmpeg error",
			err)
	}

	// 更新 子任务
	err = a.subTaskRepo.UpdateSubTaskFields(ctx, subTask.Id, map[string]interface{}{
		"material_highlight_url":      mergeUrl,
		"material_highlight_duration": duration,
	})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskFields err",
			"db error",
			err)
	}
	return task, nil
}

func (a *TrimClipActivity) parseTranscodingResponse(ctx context.Context, detail *pb.Task) (*common.VideoTranscodingResponse, error) {
	resp := &common.VideoTranscodingResponse{}
	err := json.Unmarshal([]byte(detail.CommonOutputContent), resp)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}
	return resp, nil
}

func (a *TrimClipActivity) handleFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, detail *pb.Task) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Errorf(ctx, "ConcatActivity HandleFailed:%v", detail)
	return nil, temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", nil)
}
func TrimClipProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Infof(ctx, "TrimClipProcess task:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &TrimClipActivity{
		subTaskRepo:    impl.NewCommentarySubTaskRepoImpl(),
		itemsRepo:      impl.NewCommentarySubtitleItemRepoImpl(),
		engineTaskRepo: engine_task_impl.NewEngineTaskRepoImpl(),
	}, task, subTask)
}
