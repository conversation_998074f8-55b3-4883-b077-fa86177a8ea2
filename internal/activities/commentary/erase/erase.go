package erase

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/erase"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/repo/inpainting"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type EraseActivity struct {
	ocrDb       inpainting.IInpaintingOcrRepo
	subTaskRepo commentary_repo.ICommentarySubTaskRepo
	engineRepo  enginetaskrepo.IEngineTaskRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultPreEraseNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultPreEraseRetryError",
		"InvalidOrderError",
		nil)
)

func (a *EraseActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	if task.EraseMode == consts.EraseModeOff {
		return true
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity skip, subTaskId: %v", subTask.Id)
		return true
	}
	if ocrTask.Status == consts.InpaintingStatusEraseComplete {
		return true
	}
	return false
}

func (a *EraseActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 实现等待逻辑...
	/*
		return temporal.NewNonRetryableApplicationError( // 不需要重试的错误
			"invalid order format",
			"InvalidOrderError",
			nil,
		)
	*/
	/*
		return temporal.NewApplicationError( // 需要重试的错误
			"invalid order format",
			"InvalidOrderError",
			nil,
		)
	*/
	return nil
}

func (a *EraseActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {

	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity HandleSubmit, GetOcrTaskBySubId failed, err: %v", err)
		return nil, err
	}
	chunks, err := impaintImpl.NewInpaintingOcrTaskImpl().GetAllChunks(ctx, ocrTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity getEraseTaskList, GetAllChunks failed, err: %v", err)
		return nil, err
	}
	if ocrTask.Status != consts.InpaintingStatusPreprocessComplete {
		bids := make([]int64, 0, len(chunks))
		for _, chunk := range chunks {
			bids = append(bids, chunk.Id)
		}
		// 是否已经提交过了呢？？？
		taskType := omniProto.TaskType_VIDEO_TRANSLATION_ERASE
		if ocrTask.EraseEdition == consts.EraseEditionProfessional {
			taskType = omniProto.TaskType_VIDEO_TRANSLATION_ERASE_PRO
		}
		engineTasks, err := a.engineRepo.GetWorkflowEngineTasksByBidAndType(ctx, bids, int(taskType))
		if err != nil {
			g.Log().Errorf(ctx, "EraseActivity HandleSubmit, GetWorkflowEngineTasksByBidAndType failed, err: %v", err)
			return nil, noRetryErr
		}
		engineIds := make([]int64, 0, len(engineTasks))
		for _, val := range engineTasks {
			engineIds = append(engineIds, val.EngineTaskId)
		}
		return engineIds, nil
	}
	enginetask, err := a.submitToEngine(ctx, task, subTask, ocrTask, chunks)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity HandleSubmit, submitToEngine failed, err: %v", err)
		return nil, err
	}
	engineIds := make([]int64, 0, len(enginetask))
	for _, val := range enginetask {
		engineIds = append(engineIds, val.EngineTaskId)
	}
	return engineIds, nil
}

func (a *EraseActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	ocrTask *do.WorkflowOcrTask, allChunks []*do.WorkflowEraseChunkTask) ([]*do.BusinessEngineTask, error) {

	taskList, err := a.getEraseTaskList(ctx, task, subTask, ocrTask, allChunks)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity submitToEngine, getEraseTaskList failed, err: %v", err)
		return nil, err
	}
	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: taskList,
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "CreateTask EraseActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != len(taskList) {
		g.Log().Errorf(ctx, "CreateTask EraseActivity failed, len is not %v: %v", len(taskList), len(createRes.TaskIds))
		return nil, retryErr
	}
	ocrTask.PreprocessSubmitAt = time.Now()
	ocrTask.Status = consts.InpaintingStatusEraseSubmitted

	enginetasks := make([]*do.BusinessEngineTask, 0, len(taskList))
	for idx, val := range taskList {
		enginetask := &do.BusinessEngineTask{
			EngineTaskId: createRes.TaskIds[idx],
			MainTaskId:   subTask.MainTaskId,
			Bid:          allChunks[idx].Id, //擦除表的ID
			TenantID:     ocrTask.TenantID,
			TaskType:     int(val.TaskType),
			EngineStatus: consts.EngineWorkflowStatusInit,
		}
		enginetasks = append(enginetasks, enginetask)
	}

	err = impaintImpl.NewInpaintingOcrTaskImpl().CreateEngineTasksAndUpdateOcrTask(ctx, enginetasks, ocrTask)
	if err != nil {
		return nil, err
	}
	return enginetasks, nil
}

func (a *EraseActivity) getEraseTaskList(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask, allChunks []*do.WorkflowEraseChunkTask) ([]*omniProto.TaskInfo, error) {
	// 返回擦除任务的列表
	taskList := make([]*omniProto.TaskInfo, 0)
	for _, chunk := range allChunks {
		taskInfo := &omniProto.TaskInfo{}
		taskInfo.Name = fmt.Sprintf("commentary_erase_%v", chunk.Id)
		taskInfo.RefererId = task.Id
		taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_ERASE
		if ocrTask.EraseEdition == consts.EraseEditionProfessional {
			taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_ERASE_PRO
			taskInfo.Name = fmt.Sprintf("commentary_erase_pro_%v", chunk.Id)
		}
		taskInfo.Quantity = 1
		chunkSt := &erase.PreprocessChunk{}
		err := json.Unmarshal([]byte(chunk.Chunk), chunkSt)
		if err != nil {
			//chunk解析失败，整个任务都要失败
			g.Log().Errorf(ctx, "EraseActivity getEraseTaskList, Unmarshal failed, err: %v", err)
			return nil, err
		}
		req := a.buildEraseReq(ctx, subTask, ocrTask, chunkSt)
		data, err := json.Marshal(req)
		if err != nil {
			g.Log().Errorf(ctx, "json.Marshal failed, err: %v", err)
			return nil, err
		}
		taskInfo.CommonInputContent = string(data)
		taskList = append(taskList, taskInfo)
	}
	return taskList, nil
}

func (a *EraseActivity) buildEraseReq(ctx context.Context, subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask, chunk *erase.PreprocessChunk) *erase.EraseV2Req {
	req := &erase.EraseV2Req{}
	req.Id = ocrTask.Bid
	req.ChunkIdx = chunk.ChunkId
	req.ChunkBbox = chunk.ChunkBbox
	req.InferBbox = chunk.InferBbox
	req.FramesRange = chunk.FramesRange
	req.ChunkPath = chunk.ChunkPath
	req.MasterFrames = chunk.MasterFrames
	req.InferSize = chunk.InferSize
	req.OccupiedSize = chunk.OccupiedSize

	return req
}

func (a *EraseActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	// 处理成功...
	g.Log().Infof(ctx, "EraseActivity HandleResult:%+v", task)
	if len(result) == 0 {
		return task, fmt.Errorf("EraseActivity HandleResult, len is 0")
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity HandleResult, GetOcrTaskBySubId failed, err: %v", err)
		return task, err
	}
	isFailed := false
	allDone := true
	code := 0
	msg := ""
	for _, val := range result {
		if val.EngineStatus != consts.EngineWorkflowStatusSuccess {
			allDone = false
		}
		if val.EngineStatus == consts.EngineWorkflowStatusFailed {
			code, msg = a.getEraseFailedCodeMsg(ctx, subTask, val)
			g.Log().Errorf(ctx, "EraseActivity HandleResult, EngineStatus failed, task:%+v", val)
			isFailed = true
			break
		}
	}
	if isFailed {
		return task, processor.HandleSubTaskFail(ctx, subTask, code, msg)
	}
	if !allDone {
		return task, processor.HandleSubTaskFail(ctx, subTask, int(erase.AiCodeUnkown), "all done is false")
	}
	ocrTask.Status = consts.InpaintingStatusEraseComplete
	// 更新ocr任务状态
	err = a.ocrDb.UpdateOcrTask(ctx, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity HandleResult, UpdateOcrTaskStatus failed, err: %v", err)
	}
	return task, nil
}

func (a *EraseActivity) getEraseFailedCodeMsg(ctx context.Context, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask) (int, string) {
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity getEraseFailedCodeMsg, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return int(erase.AiCodeUnkown), ""
	}
	g.Log().Infof(ctx, "EraseActivity getEraseFailedCodeMsg, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		subTask.MainTaskId, subTask.Id, engineTask.EngineTaskId)
	res := &erase.PreprocessResultRes{}
	err = json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "EraseActivity getEraseFailedCodeMsg 完成时返回, 解析结果失败, taskId: %v, err: %s", res.Id, err.Error())
		return int(erase.AiCodeUnkown), ""
	}
	return int(res.Code), res.Msg
}

func (a *EraseActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	return nil
}

func EraseProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "EraseProcess task:%+v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &EraseActivity{
		ocrDb:       impaintImpl.NewInpaintingOcrTaskImpl(),
		subTaskRepo: commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
		engineRepo:  impl.NewEngineTaskRepoImpl(),
	}, task, subTask)
}
