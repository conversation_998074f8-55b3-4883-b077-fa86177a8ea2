package refund

import (
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	"go.temporal.io/sdk/temporal"
)

// RefundProcess 退款处理入口函数
// 参数:
//   - ctx: 上下文
//   - task: 主任务业务对象
//   - subTask: 子任务业务对象，如果为nil则退款全部，否则只退款指定子任务
//
// 返回值:
//   - *bo.CommentaryMainTaskBO: 处理后的主任务业务对象
//   - error: 处理错误
func RefundProcess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "RefundProcess task:%v", task)

	// 检查是否需要跳过退款
	if shouldSkipRefund(ctx, task) {
		g.Log().Info(ctx, "跳过退款处理，任务ID: %d", task.Id)
		return task, nil
	}

	// 执行退款逻辑
	if err := executeRefund(ctx, task); err != nil {
		return handleRefundFailure(ctx, task, err)
	}

	// 退款成功，更新状态
	return handleRefundSuccess(ctx, task)
}

// shouldSkipRefund 判断是否应该跳过退款
func shouldSkipRefund(ctx context.Context, task *bo.CommentaryMainTaskBO) bool {
	// 如果任务状态为成功，则跳过退款
	if task.Status == consts.MainTaskStatusCompleted {
		g.Log().Info(ctx, "任务已完成，跳过退款，任务ID: %d", task.Id)
		return true
	}
	// 如果没有扣款订单ID，则跳过退款
	if task.PayOrderId == "" {
		g.Log().Info(ctx, "无扣款订单ID，跳过退款，任务ID: %d", task.Id)
		return true
	}
	return false
}

// checkAllSubTasksFailed 检查主任务下的所有子任务是否都失败
func checkAllSubTasksFailed(ctx context.Context, mainTaskId int64) (bool, error) {
	subTaskRepo := commentary_repo_impl.NewCommentarySubTaskRepoImpl()
	subTasks, err := subTaskRepo.GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		return false, fmt.Errorf("获取子任务列表失败: %w", err)
	}

	if len(subTasks) == 0 {
		return false, nil
	}

	// 检查是否所有子任务都失败
	for _, subTask := range subTasks {
		if subTask.Status != consts.SubTaskStatusFailed {
			return false, nil
		}
	}

	g.Log().Info(ctx, "检查发现主任务 %d 下的所有子任务都已失败", mainTaskId)
	return true, nil
}

// executeRefund 执行退款逻辑
func executeRefund(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	// 检查是否所有子任务都失败，如果是则执行全量退款
	allFailed, err := checkAllSubTasksFailed(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "检查子任务状态失败: %v", err)
		return fmt.Errorf("检查子任务状态失败: %w", err)
	}

	if allFailed {
		return executeFullRefund(ctx, task)
	}

	// 不是所有子任务都失败，执行部分退款逻辑
	return executePartialRefund(ctx, task)
}

// executeSubTaskRefund 执行单个子任务退款逻辑
func executeSubTaskRefund(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	g.Log().Info(ctx, "开始单个子任务退款处理，子任务ID: %d", subTask.Id)

	// 检查是否所有子任务都失败
	allFailed, err := checkAllSubTasksFailed(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "检查子任务状态失败: %v", err)
		return fmt.Errorf("检查子任务状态失败: %w", err)
	}

	if allFailed {
		// 所有子任务都失败，退款主任务
		refundOrderId := fmt.Sprintf("refund_mainTask_%d_%d", task.Id, time.Now().Unix())
		g.Log().Info(ctx, "所有子任务都失败，开始退款主任务，主任务ID: %d, 退款订单ID: %s", task.Id, refundOrderId)
		return callRefundAPI(ctx, task, refundOrderId)
	}

	// 不是所有子任务都失败，只退款当前子任务
	refundOrderId := fmt.Sprintf("refund_subTask_%d_%d", subTask.Id, time.Now().Unix())
	g.Log().Info(ctx, "处理当前子任务退款，子任务ID: %d, 退款订单ID: %s", subTask.Id, refundOrderId)
	return callRefundAPI(ctx, task, refundOrderId)
}

// executeFullRefund 执行全部任务退款逻辑
func executeFullRefund(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	g.Log().Info(ctx, "开始全部任务退款，主任务ID: %d", task.Id)

	// 1. 退款主任务
	if err := refundMainTask(ctx, task); err != nil {
		return err
	}

	// 2. 遍历退款所有子任务
	return refundAllSubTasks(ctx, task)
}

// executePartialRefund 执行部分退款逻辑
func executePartialRefund(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	g.Log().Info(ctx, "开始部分退款处理，主任务ID: %d", task.Id)

	// 只退款失败的子任务
	return refundFailedSubTasks(ctx, task)
}

// refundMainTask 退款主任务
func refundMainTask(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	refundOrderId := fmt.Sprintf("refund_mainTask_%d_%d", task.Id, time.Now().Unix())
	g.Log().Info(ctx, "退款主任务，主任务ID: %d, 退款订单ID: %s", task.Id, refundOrderId)
	return callRefundAPI(ctx, task, refundOrderId)
}

// refundAllSubTasks 遍历退款所有子任务
func refundAllSubTasks(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	subTaskRepo := commentary_repo_impl.NewCommentarySubTaskRepoImpl()
	subTasks, err := subTaskRepo.GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "获取子任务列表失败: %v", err)
		return fmt.Errorf("获取子任务列表失败: %w", err)
	}

	for _, subTaskItem := range subTasks {
		refundOrderId := fmt.Sprintf("refund_subTask_%d_%d", subTaskItem.Id, time.Now().Unix())
		g.Log().Info(ctx, "退款子任务，子任务ID: %d, 退款订单ID: %s", subTaskItem.Id, refundOrderId)

		if err := callRefundAPI(ctx, task, refundOrderId); err != nil {
			g.Log().Error(ctx, "调用子任务退款接口失败，子任务ID: %d, 错误: %v", subTaskItem.Id, err)
			// 继续处理其他子任务，不中断整个流程
			continue
		}

		g.Log().Info(ctx, "子任务退款接口调用成功，子任务ID: %d, 退款订单ID: %s", subTaskItem.Id, refundOrderId)
	}

	g.Log().Info(ctx, "全部任务退款处理完成，主任务ID: %d", task.Id)
	return nil
}

// callRefundAPI 调用退款接口的公共方法
func callRefundAPI(ctx context.Context, task *bo.CommentaryMainTaskBO, refundOrderId string) error {
	// 构建退款请求
	refundReq := &omni_balance.AVoiceRefundEventCreditsRequest{
		AppId:      task.AppId,
		TenantId:   task.TenantId,
		PayOrderId: task.PayOrderId, // 原扣费订单ID
		OutOrderId: refundOrderId,   // 退款订单ID
	}

	// 调用omni-balance退款接口
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	_, err := balanceClient.AVoiceRefundEventCredits(ctx, refundReq)
	if err != nil {
		g.Log().Error(ctx, "调用退款接口失败: %v", err)
		return fmt.Errorf("调用退款接口失败: %w", err)
	}

	g.Log().Info(ctx, "退款接口调用成功，退款订单ID: %s", refundOrderId)
	return nil
}

// handleRefundFailure 处理退款失败
func handleRefundFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, err error) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Error(ctx, "退款失败: %v", err)

	// 退款失败时不更新支付状态，保持原状态
	// 只记录错误并返回

	return task, temporal.NewNonRetryableApplicationError("RefundError", "refund error", nil)
}

// handleRefundSuccess 处理退款成功
func handleRefundSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	// 检查是否所有子任务都失败来决定退款范围
	allFailed, err := checkAllSubTasksFailed(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "检查子任务状态失败: %v", err)
		// 即使检查失败，也认为退款成功
	}

	if allFailed {
		// 全部退款成功，更新主任务和所有子任务的支付状态
		updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusRefund)
		updateSubTaskPayOrderStatus(ctx, task.Id, consts.PayOrderStatusRefund)
		g.Log().Info(ctx, "全部任务退款成功，主任务ID: %d", task.Id)
	} else {
		// 部分退款成功，只更新失败子任务的支付状态
		updateFailedSubTasksPayOrderStatus(ctx, task.Id)
		g.Log().Info(ctx, "部分任务退款成功，主任务ID: %d", task.Id)
	}

	return task, nil
}

// updatePayOrderStatus 更新主任务支付状态
func updatePayOrderStatus(ctx context.Context, taskId int64, payOrderStatus consts.PayOrderStatus) {
	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}
	if err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().UpdateTaskFields(ctx, taskId, fields); err != nil {
		g.Log().Error(ctx, "UpdatePayOrderStatus err:%v", err)
	}
}

// updateSubTaskPayOrderStatus 批量更新主任务下所有子任务的支付状态
func updateSubTaskPayOrderStatus(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus) {
	// 批量更新主任务下所有子任务的支付状态
	rowsAffected, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskPayOrderStatusByMainTaskId(ctx, mainTaskId, payOrderStatus)
	if err != nil {
		g.Log().Error(ctx, "UpdateSubTaskPayOrderStatusByMainTaskId err:%v, mainTaskId:%d", err, mainTaskId)
		return
	}

	g.Log().Info(ctx, "批量更新子任务支付状态成功, mainTaskId:%d, payOrderStatus:%d, rowsAffected:%d", mainTaskId, payOrderStatus, rowsAffected)
}

// updateSubTaskPayOrderStatusById 更新指定子任务的支付状态
func updateSubTaskPayOrderStatusById(ctx context.Context, subTaskId int64, payOrderStatus consts.PayOrderStatus) {
	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}
	if err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskFields(ctx, subTaskId, fields); err != nil {
		g.Log().Error(ctx, "UpdateSubTaskPayOrderStatusById err:%v, subTaskId:%d", err, subTaskId)
		return
	}

	g.Log().Info(ctx, "更新子任务支付状态成功, subTaskId:%d, payOrderStatus:%d", subTaskId, payOrderStatus)
}

// refundFailedSubTasks 退款失败的子任务
func refundFailedSubTasks(ctx context.Context, task *bo.CommentaryMainTaskBO) error {
	// 获取所有失败的子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Error(ctx, "获取子任务列表失败: %v", err)
		return fmt.Errorf("获取子任务列表失败: %w", err)
	}

	for _, subTask := range subTasks {
		if subTask.Status == consts.SubTaskStatusFailed {
			refundOrderId := fmt.Sprintf("refund_subTask_%d_%d", subTask.Id, time.Now().Unix())
			g.Log().Info(ctx, "退款失败子任务，子任务ID: %d, 退款订单ID: %s", subTask.Id, refundOrderId)
			if err := callRefundAPI(ctx, task, refundOrderId); err != nil {
				g.Log().Error(ctx, "退款失败子任务失败: %v", err)
				return err
			}
		}
	}

	return nil
}

// updateFailedSubTasksPayOrderStatus 更新失败子任务的支付状态
func updateFailedSubTasksPayOrderStatus(ctx context.Context, mainTaskId int64) {
	// 获取所有失败的子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, mainTaskId)
	if err != nil {
		g.Log().Error(ctx, "获取子任务列表失败: %v", err)
		return
	}

	for _, subTask := range subTasks {
		if subTask.Status == consts.SubTaskStatusFailed {
			updateSubTaskPayOrderStatusById(ctx, subTask.Id, consts.PayOrderStatusRefund)
		}
	}
}
