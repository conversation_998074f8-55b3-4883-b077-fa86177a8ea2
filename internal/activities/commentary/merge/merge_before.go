package merge

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	"go.temporal.io/sdk/temporal"
	"os"
	"path/filepath"
)

// MergeBeforeProcess 合成前置处理  , BGM处理
func MergeBeforeProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "MergeBeforeProcess task:%v, sub_task:%v", task, subTask)

	subTaskRepo := impl.NewCommentarySubTaskRepoImpl()

	// 获取子任务详情
	sub, err := subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskById failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubTaskById error",
			"db error",
			err)
	}

	if sub.BgmUrl == "" {
		g.Log().Info(ctx, "BGM URL is empty, skip BGM processing for subTask: %d", subTask.Id)
		return task, nil
	}

	duration := sub.MaterialHighlightDuration
	if duration <= 0 {
		g.Log().Errorf(ctx, "material_highlight_duration is 0, subTask: %d", subTask.Id)
		return task, temporal.NewNonRetryableApplicationError("material_highlight_duration is 0", "db error", nil)
	}

	g.Log().Infof(ctx, "开始处理BGM，子任务ID: %d, BGM URL: %s, 目标时长: %.2f秒", subTask.Id, sub.BgmUrl, duration)

	// 1. 从OSS下载BGM文件
	originalBgmPath, err := downloadBgmFile(ctx, sub.BgmUrl)
	if err != nil {
		g.Log().Errorf(ctx, "下载BGM文件失败, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"下载BGM文件失败",
			"obs error",
			err)
	}
	defer os.Remove(originalBgmPath) // 清理临时文件

	// 2. 调整BGM时长
	processedBgmPath, err := processBgmDuration(ctx, originalBgmPath, duration)
	if err != nil {
		g.Log().Errorf(ctx, "调整BGM时长失败, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"调整BGM时长失败",
			"ffmpeg error",
			err)
	}
	defer os.Remove(processedBgmPath) // 清理临时文件

	// 3. 上传处理后的BGM到OSS
	processedBgmUrl, err := uploadProcessedBgm(ctx, processedBgmPath)
	if err != nil {
		g.Log().Errorf(ctx, "上传处理后的BGM失败, err: %v", err)
		return nil, temporal.NewApplicationError(
			"上传处理后的BGM失败",
			"obs error",
			err)
	}

	// 4. 更新子任务的BGM URL
	err = updateSubTaskBgm(ctx, subTask.Id, processedBgmUrl)
	if err != nil {
		g.Log().Errorf(ctx, "更新子任务BGM失败, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"更新子任务BGM失败",
			"db error",
			err)
	}

	g.Log().Infof(ctx, "BGM处理完成，子任务ID: %d, 新BGM URL: %s", subTask.Id, processedBgmUrl)
	return task, nil
}

// downloadBgmFile 从OSS下载BGM文件到本地临时目录
func downloadBgmFile(ctx context.Context, bgmUrl string) (string, error) {
	// 从URL获取对象名称
	objectName, err := obs.GetObjectNameByHttpsUrl(obs.GetCdnUrlByObjectName(bgmUrl))
	if err != nil {
		return "", fmt.Errorf("解析BGM URL失败: %w", err)
	}

	// 生成本地临时文件路径
	ext := filepath.Ext(objectName)
	if ext == "" {
		ext = ".mp3" // 默认扩展名
	}
	localPath := util.GenLocalPath(fmt.Sprintf("bgm_original_%s", uuid.New().String()), ext)

	// 下载文件
	err = obs.GetOsClient().DownloadFile(objectName, localPath)
	if err != nil {
		return "", fmt.Errorf("下载BGM文件失败: %w", err)
	}

	g.Log().Infof(ctx, "BGM文件下载成功: %s -> %s", bgmUrl, localPath)
	return localPath, nil
}

// processBgmDuration 调整BGM文件的时长
func processBgmDuration(ctx context.Context, inputPath string, targetDurationSec float64) (string, error) {
	// 生成输出文件路径
	ext := filepath.Ext(inputPath)
	outputPath := util.GenLocalPath(fmt.Sprintf("bgm_processed_%s", uuid.New().String()), ext)

	// 使用ffmpeg处理音频时长
	err := ffmpeg.ProcessAudioDuration(inputPath, targetDurationSec, outputPath)
	if err != nil {
		return "", fmt.Errorf("处理BGM时长失败: %w", err)
	}

	g.Log().Infof(ctx, "BGM时长调整成功: %s -> %s, 目标时长: %.2f秒", inputPath, outputPath, targetDurationSec)
	return outputPath, nil
}

// uploadProcessedBgm 上传处理后的BGM文件到OSS
func uploadProcessedBgm(ctx context.Context, localPath string) (string, error) {
	// 生成OSS对象名称
	ext := filepath.Ext(localPath)
	publicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeAudioCommentary, "bgm", ext)

	// 上传文件
	uploadedUrl, err := obs.GetOsClient().UploadFile(localPath, publicObsName, acl.AclTypePublicRead)
	if err != nil {
		return "", fmt.Errorf("上传BGM文件失败: %w", err)
	}

	g.Log().Infof(ctx, "BGM文件上传成功: %s -> %s", localPath, uploadedUrl)
	return uploadedUrl, nil
}

// updateSubTaskBgm 更新子任务的BGM URL
func updateSubTaskBgm(ctx context.Context, subTaskId int64, bgmUrl string) error {
	subTaskRepo := impl.NewCommentarySubTaskRepoImpl()

	rowsAffected, err := subTaskRepo.UpdateSubTaskBgm(ctx, subTaskId, bgmUrl)
	if err != nil {
		return fmt.Errorf("更新子任务BGM失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("子任务不存在或BGM URL未发生变化, subTaskId: %d", subTaskId)
	}

	g.Log().Infof(ctx, "子任务BGM更新成功, ID: %d, BGM URL: %s, 影响行数: %d", subTaskId, bgmUrl, rowsAffected)
	return nil
}
