package merge

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/merge"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	"business-workflow/internal/repo/subtitle_merge"
	mergeRepoImpl "business-workflow/internal/repo/subtitle_merge/impl"
	hobs "business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type MergeActivity struct {
	subTaskRepo commentary_repo.ICommentarySubTaskRepo
	itemRepo    commentary_repo.ICommentarySubtitleItemRepo
	mergeRepo   subtitle_merge.ISubtitleMergeTask
	engineRepo  enginetaskrepo.IEngineTaskRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultMergeNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultMergeRetryError",
		"InvalidOrderError",
		nil)
)

func (a *MergeActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	return false
}

func (a *MergeActivity) toMergeAudioTask(ctx context.Context, subTask *do.CommentarySubTask, items []*do.CommentarySubtitleItem) (*processor.AudioMergeTask, error) {
	mergeTask := &processor.AudioMergeTask{}
	mergeTask.TaskId = subTask.Id
	mergeTask.BgUrl = subTask.BgmUrl
	mergeTask.DisableBgm = subTask.BgmMode == consts.BgmModeOff
	for _, item := range items {
		audioItem := &processor.AudioItem{}
		audioItem.ItemIdx = int(item.ItemIdx)
		audioItem.TtsUrl = item.TTSUrl
		audioItem.SubtitleStartStr = item.SubtitleStartStr
		if item.AudioConfig != nil {
			audioItem.Config = &processor.AudioConfig{}
			audioItem.Config.VolumeGainDB = item.AudioConfig.VolumeGainDB
			audioItem.Config.Speed = item.AudioConfig.Speed
		}
		mergeTask.TtsUrls = append(mergeTask.TtsUrls, audioItem)
	}
	return mergeTask, nil
}

func (a *MergeActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 合成音频并上传？？？或者放在本地？？？
	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, GetSubTaskById error: %v", err)
		return retryErr
	}
	items, err := a.itemRepo.GetSubtitleItemsBySubTaskId(ctx, subTaskDO.Id)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, GetItemsBySubTaskId error: %v", err)
		return retryErr
	}
	mergeTask, err := a.toMergeAudioTask(ctx, subTaskDO, items)
	_, err = processor.MergeAudio(ctx, mergeTask, 5)
	if err != nil {
		g.Log().Error(ctx, "MergeActivity WaitCondition, MergeAudioTask failed, subTaskId: %v, err: %v", subTask.Id, err)
		return retryErr
	}
	// 生成一条合成记录
	taskDO := &do.WorkflowMergeTask{}
	taskDO.MainTaskId = task.Id
	taskDO.SubTaskId = subTask.Id
	taskDO.AudioUrl = mergeTask.OutLocalPath
	taskDO.VideoUrl = subTaskDO.NoSubtitleMaterialHighlightUrl
	taskDO.AssUrl = subTaskDO.SubtitleFileUrl
	taskDO.TenantID = subTask.TenantId
	taskDO.AppId = task.AppId
	taskDO.VideoDuration = float32(subTask.VideoDuration)
	taskDO.Status = int(consts.MergeStatusPending)
	return a.mergeRepo.CreateSubtitleMergeTask(ctx, taskDO)
}

func (a *MergeActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 查找原始任务
	_, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleSubmit, GetOcrTaskBySubId failed, err: %v", err)
		return nil, err
	}
	// 查找合成任务
	mergeTask, err := a.mergeRepo.GetLatestMergeTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleSubmit, GetLatestMergeTaskBySubId failed, err: %v", err)
		return nil, err
	}
	// 查找engine任务
	engineTask, err := a.engineRepo.GetWorkflowEngineTaskByBidAndType(ctx, mergeTask.Id, int(omniProto.TaskType_COMMENTARY_SUBTITLE_MERGE))
	if err != nil {
		g.Log().Warningf(ctx, "MergeActivity HandleSubmit, GetWorkflowEngineTaskByBid failed, err: %v", err)
		return nil, noRetryErr
	}
	if engineTask.Id > 0 {
		g.Log().Warningf(ctx, "MergeActivity HandleSubmit, engineTask is not nil: %v", engineTask)
	}
	if mergeTask.Status != int(consts.MergeStatusPending) {
		if engineTask.Id > 0 && engineTask.Bid == mergeTask.Id {
			g.Log().Warningf(ctx, "MergeActivity HandleSubmit, engineTask is not nil: %v", engineTask)
			return []int64{engineTask.EngineTaskId}, nil
		} else {
			g.Log().Errorf(ctx, "MergeActivity HandleSubmit, ocrTask.Stutus is not MergeStatusPending: %v", mergeTask.Status)
			return nil, noRetryErr
		}
	}
	engineTask, err = a.submitToEngine(ctx, task, subTask, mergeTask)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleSubmit, submitToEngine failed, subTaskId: %v, mergeId: %v, err: %v",
			subTask.Id, mergeTask.SubTaskId, err)
		return nil, retryErr
	}
	return []int64{engineTask.EngineTaskId}, nil
}

func (a *MergeActivity) BuildSubtitleMergeReq(mergeTask *do.WorkflowMergeTask) *merge.SubtitleMergeSubmitReq {
	req := &merge.SubtitleMergeSubmitReq{
		MergeTaskId:      mergeTask.Id,
		SubtitleId:       mergeTask.SubTaskId,
		VocalAudioPath:   mergeTask.AudioUrl,
		AssFilePath:      mergeTask.AssUrl,
		IsWaterMark:      false,
		IsVocalTask:      mergeTask.Vocal == 1,
		NotitleVideoPath: mergeTask.VideoUrl,
		TargetObjectName: mergeTask.PostVideoObjectName,
		PostVideoPath:    "",
		VideoDuration:    mergeTask.VideoDuration,
		Crf:              consts.Crf,
		Preset:           consts.Preset,
	}
	return req
}

func (a *MergeActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, mergeTask *do.WorkflowMergeTask) (*do.BusinessEngineTask, error) {
	req := a.BuildSubtitleMergeReq(mergeTask)
	req.PostVideoPath = fmt.Sprintf("%s/post_video_%v_%v_%s.mp4",
		processor.GetLocalVideoPathById(ctx, subTask.Id), subTask.MainTaskId, subTask.Id, uuid.New().String())

	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_subtitle_merge_%v", mergeTask.Id)
	taskInfo.TaskType = omniProto.TaskType_COMMENTARY_SUBTITLE_MERGE
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id

	data, err := json.Marshal(req)
	if err != nil {
		g.Log().Error(ctx, "json.Marshal failed, err: %v", err)
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Error(ctx, "CreateTask MergeActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Error(ctx, "CreateTask MergeActivity failed, len is not 1: %v", len(createRes.TaskIds))
		return nil, retryErr
	}

	mergeTask.ProcessSubmitAt = time.Now()
	mergeTask.Status = int(consts.MergeStatusProcessing)

	enginetask := &do.BusinessEngineTask{
		EngineTaskId: createRes.TaskIds[0],
		MainTaskId:   subTask.MainTaskId,
		Bid:          mergeTask.Id,
		TenantID:     mergeTask.TenantID,
		TaskType:     int(taskInfo.TaskType),
		EngineStatus: consts.EngineWorkflowStatusInit,
	}

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity submitToEngine, GetSubTaskById failed, subTaskId: %v, err: %v", subTask.Id, err)
		return nil, err
	}
	subTaskDO.MergeStatus = consts.MergeStatusProcessing
	err = a.mergeRepo.CreateEngineTasksAndUpdateMergeTask(ctx, []*do.BusinessEngineTask{enginetask}, mergeTask, subTaskDO)
	if err != nil {
		return nil, err
	}
	return enginetask, nil
}

func (a *MergeActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Infof(ctx, "MergeActivity HandleResult:%v", task)
	if len(result) == 0 {
		g.Log().Errorf(ctx, "MergeActivity HandleResult, result is empty")
		return task, noRetryErr
	}
	engineTask := result[0]
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity HandleResult, unmarshal merge engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return task, noRetryErr
	}
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity handleSuccess failed, err: %v", err)
			return task, noRetryErr
		}
		return task, nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "MergeActivity handleFail failed, err: %v", err)
		}
		return task, noRetryErr
	} else {
		g.Log().Warningf(ctx, "MergeActivity HandleResult, status maybe wrong, engineStatus: %v", engineTask.EngineStatus)
		return task, retryErr
	}
}

func (a *MergeActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Infof(ctx, "MergeActivity HandleSuccess, subTaskId: %v, mergeTaskId: %v", subTask.Id, engineTask.Bid)
	res := &merge.SubtitleMergeEngineRes{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask, err := a.mergeRepo.GetMergeTaskById(ctx, engineTask.Bid)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get merge task by bid failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask.Status = int(consts.MergeStatusCompleted)
	if res.MergeStartAt > 0 {
		mergeTask.MergeStartAt = time.UnixMilli(res.MergeStartAt)
		mergeTask.MergeEndAt = time.UnixMilli(res.MergeEndAt)
		mergeTask.UploadStartAt = time.UnixMilli(res.MergeUploadStartAt)
		mergeTask.UploadEndAt = time.UnixMilli(res.MergeUploadEndAt)
	}
	_, mergeTask.PostUrl, err = hobs.GetOsClient().GetObjectUrl(mergeTask.PostVideoObjectName)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get post video url failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleSuccess, get subtask by id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	subTaskDO.MergedVideoUrl = mergeTask.PostUrl
	subTaskDO.MergeStatus = consts.MergeStatusCompleted

	return a.mergeRepo.UpdateMergeTaskAndSubTask(ctx, mergeTask, subTaskDO)
}

func (a *MergeActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Errorf(ctx, "PostEraseActivity handleFail, subTaskId: %v, engineTaskId: %v",
		subTask.Id, engineTask.EngineTaskId)
	res := &merge.SubtitleMergeEngineRes{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask, err := a.mergeRepo.GetMergeTaskById(ctx, engineTask.Bid)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, merge task by bid failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	mergeTask.Status = int(consts.MergeStatusFailed)

	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, get subtask by id failed, err: %v, subTaskId: %v",
			err, subTask.Id)
		return err
	}

	err = a.mergeRepo.UpdateMergeTaskAndSubTask(ctx, mergeTask, subTaskDO)
	if err != nil {
		g.Log().Errorf(ctx, "MergeActivity handleFail, UpdateMergeTaskAndSubTask failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}

	return processor.HandleSubTaskFail(ctx, subTask, int(res.RetCode), res.ErrMsg)
}

func (a *MergeActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	return nil
}

func MergeProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "MergeProcess task:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &MergeActivity{
		subTaskRepo: commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
		itemRepo:    commentaryRepoImpl.NewCommentarySubtitleItemRepoImpl(),
		mergeRepo:   mergeRepoImpl.NewSubtitleMergeTaskImpl(),
		engineRepo:  impl.NewEngineTaskRepoImpl(),
	}, task, subTask)
}
