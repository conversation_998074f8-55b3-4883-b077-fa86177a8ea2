package merge

import (
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"context"
	"fmt"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	omniBalance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
)

// MergeAfterProcess 合成后处理活动，负责赠送免费积分和免费合成次数
// 该函数在子任务合成完成后调用，为子任务赠送免费积分和免费合成次数
func MergeAfterProcess(ctx context.Context, mainTask *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	logger := g.Log()
	logger.Infof(ctx, "MergeAfterProcess start, mainTaskId: %d, subTaskId: %d", mainTask.Id, subTask.Id)

	// 获取 omni-balance gRPC 客户端
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	if balanceClient == nil {
		return fmt.Errorf("failed to get omni-balance client")
	}

	// 准备赠送积分的任务订单
	// 注意：这里需要传入真实的支付订单ID，让omni-balance服务根据支付订单历史记录计算赠送积分数量
	// 区分主任务和子任务的扣费订单：主任务扣费使用mainTask_前缀，子任务赠送使用subTask_前缀
	payOrderId := fmt.Sprintf("subTask_%d", subTask.Id)

	taskOrderList := []*omniBalance.AVoiceTaskOrder{
		{
			TaskId:     subTask.Id,
			PayOrderId: payOrderId, // 使用关联的支付订单ID
			Credits:    0,          // 设置为0，让服务端根据PayOrderId查询历史记录计算赠送积分数量
		},
	}

	// 调用赠送积分接口
	giveCreditsReq := &omniBalance.AVoiceGiveTaskCreditsRequest{
		AppId:         mainTask.AppId,
		TenantId:      mainTask.TenantId,
		TaskOrderList: taskOrderList,
	}

	_, err := balanceClient.AVoiceGiveTaskCredits(ctx, giveCreditsReq)
	if err != nil {
		logger.Errorf(ctx, "Failed to give task credits: %v", err)
		return fmt.Errorf("failed to give task credits: %w", err)
	}

	logger.Infof(ctx, "Successfully gave task credits for sub task %d", subTask.Id)

	// 赠送免费合成次数
	giveSynthesisReq := &omniBalance.AVoiceGiveFreeSynthesisTimesRequest{
		AppId:      mainTask.AppId,
		TenantId:   subTask.TenantId,
		TaskId:     strconv.FormatInt(subTask.Id, 10),
		GiveTimes:  consts.FreeSynthesisGiveTimes, // 赠送免费合成次数
		OperatorId: 0, // 系统操作
	}

	_, err = balanceClient.AVoiceGiveFreeSynthesisTimes(ctx, giveSynthesisReq)
	if err != nil {
		logger.Errorf(ctx, "Failed to give free synthesis times for task %d: %v", subTask.Id, err)
		return fmt.Errorf("failed to give free synthesis times: %w", err)
	}

	logger.Infof(ctx, "Successfully gave free synthesis times for task %d", subTask.Id)
	logger.Infof(ctx, "MergeAfterProcess completed successfully")
	return nil
}
