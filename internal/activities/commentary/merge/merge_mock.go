package merge

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"
	"os"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"
	"go.temporal.io/sdk/temporal"
)

func MergeMockProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "MergeMockProcess task:%v, sub_task:%v", task, subTask)

	subTaskRepo := impl.NewCommentarySubTaskRepoImpl()

	// 获取子任务详情
	sub, err := subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetSubTaskById failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubTaskById error",
			"db error",
			err)
	}

	// 收集需要合并的视频URL
	var videoUrls []string

	// 添加MaterialHighlightUrl
	if sub.MaterialHighlightUrl != "" {
		videoUrls = append(videoUrls, sub.MaterialHighlightUrl)
	}

	// 添加HighlightEpisodesUrls (数组类型)
	if len(sub.HighlightEpisodesUrls) > 0 {
		for _, url := range sub.HighlightEpisodesUrls {
			url = strings.TrimSpace(url)
			if url != "" {
				videoUrls = append(videoUrls, url)
			}
		}
	}

	// 添加EndTagUrl
	if sub.EndTagUrl != "" {
		videoUrls = append(videoUrls, sub.EndTagUrl)
	}

	// 检查是否有视频需要合并
	if len(videoUrls) == 0 {
		g.Log().Warningf(ctx, "No video URLs found for merge, subTask: %d", subTask.Id)
		return task, nil
	}

	g.Log().Infof(ctx, "Found %d videos to merge: %v", len(videoUrls), videoUrls)

	// 下载视频文件到本地
	var localFiles []string
	var tempFiles []string // 用于清理

	defer func() {
		// 清理临时文件
		for _, file := range tempFiles {
			if err := os.Remove(file); err != nil {
				g.Log().Warningf(ctx, "Failed to remove temp file %s: %v", file, err)
			}
		}
	}()

	for i, videoUrl := range videoUrls {
		// 生成本地文件路径
		localPath := util.GenLocalPath(fmt.Sprintf("merge_input_%d_%s", i, uuid.New().String()), ".mp4")
		localFiles = append(localFiles, localPath)
		tempFiles = append(tempFiles, localPath)

		// 从URL获取对象名称
		objectName, err := obs.GetObjectNameByHttpsUrl(videoUrl)
		if err != nil {
			g.Log().Errorf(ctx, "GetObjectNameByHttpsUrl failed for %s, err: %v", videoUrl, err)
			return nil, temporal.NewNonRetryableApplicationError(
				"GetObjectNameByHttpsUrl error",
				"url parse error",
				err)
		}

		// 下载文件
		err = obs.GetOsClient().DownloadFile(objectName, localPath)
		if err != nil {
			g.Log().Errorf(ctx, "DownloadFile failed for %s, err: %v", objectName, err)
			return nil, temporal.NewNonRetryableApplicationError(
				"DownloadFile error",
				"download error",
				err)
		}

		g.Log().Infof(ctx, "Downloaded video %d: %s -> %s", i+1, videoUrl, localPath)
	}

	// 使用ffmpeg合并视频
	mergedFilePath := util.GenLocalPath(fmt.Sprintf("merged_%s", uuid.New().String()), ".mp4")
	tempFiles = append(tempFiles, mergedFilePath)

	err = ffmpeg.MergeVideosByConcat(localFiles, mergedFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "MergeVideosByConcat failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"MergeVideosByConcat error",
			"merge error",
			err)
	}

	g.Log().Infof(ctx, "Successfully merged videos to: %s", mergedFilePath)

	// 上传合并后的视频到OSS
	publicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeVideoCommentary, "merge", ".mp4")
	mergedVideoUrl, err := obs.GetOsClient().UploadFile(mergedFilePath, publicObsName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "UploadFile failed, err: %v", err)
		return nil, temporal.NewApplicationError(
			"UploadFile error",
			"oss error",
			err)
	}

	g.Log().Infof(ctx, "Successfully uploaded merged video: %s", mergedVideoUrl)

	// 更新子任务状态和URL
	updateFields := map[string]interface{}{
		"status":                int(consts.SubTaskStatusCompleted),
		"merged_video_url":      mergedVideoUrl,
		"no_subtitle_video_url": mergedVideoUrl, // 假设合并后的视频就是无字幕版本
	}

	err = subTaskRepo.UpdateSubTaskFields(ctx, subTask.Id, updateFields)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskFields failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateSubTaskFields error",
			"db error",
			err)
	}

	g.Log().Infof(ctx, "Successfully updated subTask %d with merged video URL", subTask.Id)

	return task, nil
}
