package transcoding

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	impl2 "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/util"
	"business-workflow/internal/util/ffmpeg"
	"business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/google/uuid"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/acl"

	"github.com/samber/lo"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gitlab.ttyuyin.com/tyr/x/utils"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

type TransCodingActivity struct {
	engineTaskRepo   enginetaskrepo.IEngineTaskRepo
	sourceDetailRepo commentary_repo.ICommentarySourceDetailRepo
	subTaskRepo      commentary_repo.ICommentarySubTaskRepo
	mainTaskRepo     commentary_repo.ICommentaryMainTaskRepo
}

func (a *TransCodingActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	mainTask, err := a.mainTaskRepo.GetTaskById(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "GetTaskById error:%v", err)
		return false
	}
	// 执行过就不执行了
	if len(mainTask.ClipMergeVideoUrl) > 0 && len(mainTask.CompressVideoUrl) > 0 {
		g.Log().Infof(ctx, "transcoding already done, skip id:%v", task.Id)
		return true
	}
	return false
}

func (a *TransCodingActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 实现等待逻辑...

	return nil
}

func (a *TransCodingActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 批量获取
	//获取全部视频
	sourceDetails, err := a.sourceDetailRepo.GetSourceDetailsByMainTaskIdWithFileType(ctx, task.Id, []consts.SourceFileType{consts.SourceFileTypeVideo})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetSourceDetailsByMainTaskIdWithFileType error",
			err)
	}
	// 只转码解说
	sourceDetails = lo.Filter(sourceDetails, func(detail *do.CommentarySourceDetail, _ int) bool {
		return detail.BizType == consts.SourceBizTypeCommentary
	})

	g.Log().Infof(ctx, "sourceDetails:%v", sourceDetails)
	resolutionList := []string{string(consts.Resolution720x1280), string(consts.Resolution480x854)}
	if task.AspectRatio == string(consts.AspectRatio16_9) {
		resolutionList = []string{string(consts.Resolution1280x720), string(consts.Resolution854x480)}
	}
	req := &common.VideoTranscodingRequest{
		VideoParams: lo.Map(resolutionList, func(resolution string, i int) *common.VideoParam {
			FPS := 30
			CRF := 23
			if i == 1 { // 低分辨率 质量对应降低
				FPS = 25
				CRF = 28
			}
			return &common.VideoParam{
				VideoInfos: lo.Map(sourceDetails, func(detail *do.CommentarySourceDetail, _ int) *common.VideoInfo {
					return &common.VideoInfo{
						ID:             detail.Id,
						OriginVideoUrl: detail.SourceUrl,
						ResultVideoUrl: obs.GeneratePublicOBSUrlPubOrPri(consts.ObsModeVideoCommentary, "transcoding", ".mp4", "public"),
					}
				}),
				Resolution: resolution,
				FPS:        FPS,
				CRF:        CRF,
				Mode:       0,
			}
		}),
	}
	// 请求前 先更新表  写入key
	updateData := make([]struct {
		ID     int64
		Fields map[string]interface{}
	}, len(sourceDetails))

	for i, detail := range sourceDetails {
		updateData[i] = struct {
			ID     int64
			Fields map[string]interface{}
		}{
			ID: detail.Id,
			Fields: map[string]interface{}{
				"target_resolution_key": req.VideoParams[0].VideoInfos[i].ResultVideoUrl, // 高分辨率
				"target_resolution_url": obs.GetCdnUrlByObjectName(req.VideoParams[0].VideoInfos[i].ResultVideoUrl),
				"low_resolution_key":    req.VideoParams[1].VideoInfos[i].ResultVideoUrl, // 低分辨率
				"low_resolution_url":    obs.GetCdnUrlByObjectName(req.VideoParams[1].VideoInfos[i].ResultVideoUrl),
			},
		}
	}

	err = a.sourceDetailRepo.BatchUpdateSourceDetailFields(ctx, updateData)

	if err != nil {
		g.Log().Errorf(ctx, "BatchUpdateSourceDetailFields error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"BatchUpdateSourceDetailFields error",
			err)
	}
	totalCost := lo.SumBy(sourceDetails, func(detail *do.CommentarySourceDetail) float64 {
		return detail.Duration
	})
	g.Log().Infof(ctx, "TransCodingActivity HandleSubmit req:%v", req)
	businessScenariosType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	if task.TaskType == consts.TaskTypeHighlight {
		businessScenariosType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	}
	curTaskInfo := &pb.TaskInfo{
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_COMMENTARY,
		BusinessScenariosType: int32(businessScenariosType),
		CommonInputContent:    utils.ToJson(req),
		TaskType:              pb.TaskType_VIDEO_TRANSCODING,
		TaskCost:              totalCost,
		RefererId:             task.Id,
	}
	// taskBase := &pb.TaskBase{
	// 	AppId:    task.AppId,
	// 	TenantId: task.TenantId,
	// 	Priority: 0,
	// 	Apikey:   task.ApiKey,
	// 	Source:   int64(pb.TaskSource_INTERNAL_SOURCE),
	// 	ExtraMap: map[string]string{
	// 		// "task_cost": fmt.Sprintf("%v", totalCost),
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", totalCost),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(pb.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", totalCost),
		})
	// 构建单个任务请求
	createReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{curTaskInfo},
	}

	// 创建任务
	g.Log().Infof(ctx, "creating transcoding  task req:%v", createReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create transcoding  task err:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"create transcoding  task err",
			"create transcoding  task err",
			err)
	}

	// 写入业务task表
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	engineTask := &do.BusinessEngineTask{
		EngineTaskId: createRes.TaskIds[0],
		EngineStatus: consts.EngineWorkflowStatusInit,
	}

	err = engineTaskRepo.CreateWorkflowEngineTask(ctx, engineTask)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetWorkflowEngineTask error",
			err)
	}
	return []int64{engineTask.EngineTaskId}, nil
}

func (a *TransCodingActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Infof(ctx, "TransCodingActivity HandleResult:%v", result)

	one := result[0]
	detail, err := one.TaskInfoConvertPB()
	if err != nil {
		g.Log().Errorf(ctx, "TaskInfoConvertPB err:%v", err)
		return task, err
	}

	switch detail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		return a.handleSuccess(ctx, task, subTask, detail)
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return a.handleFailure(ctx, task, detail)
	}

	return task, nil
}

func (a *TransCodingActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	_, err := a.subTaskRepo.UpdateSubTaskStatusByMainTaskId(ctx, task.Id, consts.SubTaskStatusFailed, "transcoding error ")

	return temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", err)
}

func (a *TransCodingActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, detail *pb.Task) (*bo.CommentaryMainTaskBO, error) {
	// 1. 下载素材资源
	// 2. 本地快速合并
	// 3. 上传oss
	// 4. 调用engine 解说
	list, err := a.sourceDetailRepo.GetSourceDetailsByMainTaskId(ctx, task.Id)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetSourceDetailsByMainTaskIdByBizType error",
			err)
	}
	// 这里只处理解说,  后面合并还会再转码合并
	list = lo.Filter(list, func(detail *do.CommentarySourceDetail, _ int) bool {
		return detail.SourceFileType == consts.SourceFileTypeVideo && detail.BizType == consts.SourceBizTypeCommentary
	})
	lowMergeFileList := make([]string, 0, len(list))
	clipMergeFileList := make([]string, 0, len(list))
	// 清理文件
	defer func() {
		for _, file := range lowMergeFileList {
			_ = os.RemoveAll(file)
		}
		for _, file := range clipMergeFileList {
			_ = os.RemoveAll(file)
		}

	}()
	for _, detail := range list {
		localPath := util.GenLocalPath(uuid.New().String(), ".mp4")
		if detail.BizType == consts.SourceBizTypeCommentary {
			lowMergeFileList = append(lowMergeFileList, localPath)
		}
		err := obs.GetOsClient().DownloadFile(detail.LowResolutionKey, localPath)
		if err != nil {
			g.Log().Errorf(ctx, "DownloadFile failed, err: %v, key: %s", err, detail.LowResolutionKey)
			return nil, temporal.NewNonRetryableApplicationError(
				"download failed",
				"oss error",
				err)
		}

		targetLocalPath := util.GenLocalPath(uuid.New().String(), ".mp4")
		if detail.BizType == consts.SourceBizTypeCommentary {
			clipMergeFileList = append(clipMergeFileList, targetLocalPath)
		}
		err = obs.GetOsClient().DownloadFile(detail.TargetResolutionKey, targetLocalPath)
		if err != nil {
			g.Log().Errorf(ctx, "DownloadFile failed, err: %v, key: %s", err, detail.TargetResolutionKey)
			return nil, temporal.NewNonRetryableApplicationError(
				"db error",
				"GetSourceDetailsByMainTaskIdByBizType error",
				err)
		}

	}
	mergeFilePath := util.GenLocalPath(uuid.New().String(), ".mp4")
	// ffmpeg 合并文件
	err = ffmpeg.MergeVideosByConcat(lowMergeFileList, mergeFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "MergeVideosByConcat failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"merge failed",
			"MergeVideosByConcat error",
			err)
	}
	publicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeVideoCommentary, "merge", ".mp4")
	// 上传oss
	cdnUrl, err := obs.GetOsClient().UploadFile(mergeFilePath, publicObsName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "UploadFile failed, err: %v", err)
		return nil, err
	}

	targetMergeFilePath := util.GenLocalPath(uuid.New().String(), ".mp4")
	// ffmpeg 合并文件
	err = ffmpeg.MergeVideosByConcat(clipMergeFileList, targetMergeFilePath)
	if err != nil {
		g.Log().Errorf(ctx, "MergeVideosByConcat failed, err: %v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"merge failed",
			"MergeVideosByConcat error",
			err)
	}
	targetPublicObsName := obs.GeneratePublicOBSUrl(consts.ObsModeVideoCommentary, "merge", ".mp4")
	// 上传oss
	targetUrl, err := obs.GetOsClient().UploadFile(targetMergeFilePath, targetPublicObsName, acl.AclTypePublicRead)
	if err != nil {
		g.Log().Errorf(ctx, "UploadFile failed, err: %v", err)
		return nil, err
	}
	// 更新数据库
	err = a.mainTaskRepo.UpdateTaskFields(ctx, task.Id, map[string]interface{}{"clip_merge_video_url": targetUrl, "compress_video_url": cdnUrl})
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"UpdateTaskFields error",
			"db error",
			err)
	}

	g.Log().Infof(ctx, "transcoding success")
	return task, nil
}

func (a *TransCodingActivity) parseTranscodingResponse(ctx context.Context, detail *pb.Task) (*common.VideoTranscodingResponse, error) {
	resp := &common.VideoTranscodingResponse{}
	err := json.Unmarshal([]byte(detail.CommonOutputContent), resp)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError("db error", "Unmarshal error", err)
	}
	return resp, nil
}

func (a *TransCodingActivity) handleFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, detail *pb.Task) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Errorf(ctx, "TransCodingActivity HandleFailed:%v", detail)
	return nil, temporal.NewNonRetryableApplicationError("transcoding error", "transcoding error", nil)
}

func TransCodingProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Infof(ctx, "TransCodingProcess task:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &TransCodingActivity{
		sourceDetailRepo: impl2.NewCommentarySourceDetailRepoImpl(),
		engineTaskRepo:   impl.NewEngineTaskRepoImpl(),
		mainTaskRepo:     impl2.NewCommentaryMainTaskRepoImpl(),
		subTaskRepo:      impl2.NewCommentarySubTaskRepoImpl(),
	}, task, subTask)
}
