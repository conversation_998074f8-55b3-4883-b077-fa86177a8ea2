package texttranslate

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/common"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"errors"
	"fmt"
	"time"

	"business-workflow/internal/common/omni_engine"

	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	engine_task_impl "business-workflow/internal/repo/engine_task_repo/impl"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/google/uuid"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"go.temporal.io/sdk/temporal"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"
)

type TextTranslateActivity struct {
	commentarySubtitleitemRepo commentary_repo.ICommentarySubtitleItemRepo
	engineTaskRepo             enginetaskrepo.IEngineTaskRepo
	commentarySubTaskRepo      commentary_repo.ICommentarySubTaskRepo
	isFirst                    bool // 是否为第一次直出与二次编辑的文本翻译
	TranslateBack              bool
	subtitleItemId             int64
}

func (a *TextTranslateActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	// 高光剪辑 不用翻译
	if task.TaskType == consts.TaskTypeHighlight {
		return true
	}
	return false
}

func (a *TextTranslateActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// TODO
	// 1. 检查CommentarySubtitleItem是否有原文

	return nil
}

func (a *TextTranslateActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {

	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return nil, err
	}

	subtitleItem := subtitleItems[0]
	fromLang := subtitleItem.SourceLangId
	toLang := subtitleItem.TargetLangId
	textLen := len([]rune(subtitleItem.OriginSubtitle))
	indexList := []int64{}
	orderId := uuid.New().String()
	timeout := int64(15 * 60) // 15分钟

	// 获取全部原文和译文
	var sourceSubtitle []*pb.AgentTranslateContentItem
	var targetSubtitle []*pb.AgentTranslateContentItem
	fullSubtitleItems, err := a.commentarySubtitleitemRepo.GetSubtitleItemsBySubTaskId(ctx, subtitleItem.SubTaskId)
	if err != nil {
		g.Log().Errorf(ctx, "GetTranslateSubtitleItemBySubtitleId error:%v", err)
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetTranslateSubtitleItemBySubtitleId error",
			err)
	}
	for _, subitem := range fullSubtitleItems {
		sourceSubtitle = append(sourceSubtitle, &pb.AgentTranslateContentItem{Id: int64(subitem.ItemIdx), Text: subitem.OriginSubtitle})
		targetSubtitle = append(targetSubtitle, &pb.AgentTranslateContentItem{Id: int64(subitem.ItemIdx), Text: subitem.TargetSubtitle})
	}
	if a.TranslateBack {
		fromLang, toLang = toLang, fromLang                             // 如果是回译，则交换语言
		sourceSubtitle, targetSubtitle = targetSubtitle, sourceSubtitle // 交换原文和译
	}

	// 判断是否是预设指令
	customInstruct := true
	for _, info := range config.GetConfig().TranslatePromptConfig.Infos {
		if info.Prompt == subtitleItem.CustomPrompt {
			customInstruct = false
		}
	}
	priority := 0
	source := int64(pb.TaskSource_INTERNAL_SOURCE)
	phonemeAlign := true
	if !a.isFirst {
		priority = 9
		source = int64(pb.TaskSource_WORKBENCH_SOURCE)
		indexList = []int64{int64(subtitleItem.ItemIdx)}
		phonemeAlign = false
	}

	// 构建通用的 TaskBase
	// taskBase := &pb.TaskBase{
	// 	AppId:    task.AppId,
	// 	TenantId: task.TenantId,
	// 	Apikey:   task.ApiKey,
	// 	Priority: int64(priority),
	// 	TimeOut:  timeout,
	// 	Source:   source,
	// 	ExtraMap: map[string]string{
	// 		"order_id": orderId,
	// 		// "task_cost":          strconv.Itoa(textLen),
	// 		// "video_translate_id": fmt.Sprintf("%v", subtitleItem.Id),
	// 		consts.BaseTaskCost:                 strconv.Itoa(textLen),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	taskBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, source,
		map[string]string{
			consts.BaseTaskCost:    fmt.Sprintf("%v", textLen),
			consts.BaseTaskOrderId: orderId,
		})
	taskBase.Priority = int64(priority)
	taskBase.TimeOut = timeout

	prompt := subtitleItem.CustomPrompt
	promptMappingTo := config.GetConfig().TranslatePromptConfig.GetPromptMappingTo(subtitleItem.CustomPrompt)
	if promptMappingTo != "" {
		g.Log().Infof(ctx, "[ReTranslate] prompt mapping, from: %s, to: %s", prompt, promptMappingTo)
		// 如果有映射，则使用映射后的提示词
		prompt = promptMappingTo
	}
	taskInfo := &pb.TaskInfo{
		Name:                  fmt.Sprintf("translate_%v", orderId),
		TaskType:              pb.TaskType_AGENT_TRANSLATION_SERVICE,
		RefererId:             task.Id,
		BizType:               pb.VoiceBusinessType_VOICE_BUSINESS_TYPE_VIDEO_TRANSLATE, // 设置业务类型，用于任务调度时选择队列
		BusinessScenariosType: int32(pb.BusinessScenariosType_BUSINESS_VIDEO_TRANSLATION_TEXT_TRANSLATE),
		AgentTranslateInputContent: &pb.AgentTranslateInputContent{
			FromLang:          fromLang,
			ToLang:            toLang,
			Instruction:       prompt,
			Content:           &pb.AgentTranslateContentInfo{Subtitles: sourceSubtitle},
			IndexList:         indexList,
			Translation:       &pb.AgentTranslateContentInfo{Subtitles: targetSubtitle},
			CustomInstruction: customInstruct,
			PhonemeAlign:      phonemeAlign,
		},
	}

	// 创建任务
	createTaskReq := &pb.CreateTaskReq{
		TaskBase: taskBase,
		TaskList: []*pb.TaskInfo{taskInfo},
	}

	// 在context中添加x-voice-business-type标识
	ctx = context.WithValue(ctx, "x-voice-business-type", "video-commentary")
	g.Log().Infof(ctx, "[ReTranslate] CreateTask, req:%v", createTaskReq)
	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createTaskReq)
	if err != nil {
		g.Log().Errorf(ctx, "CreateTask failed, err: %v, tid: %d", err, task.Id)
		return nil, err
	}

	if len(createRes.TaskIds) != 1 {
		return nil, errors.New("create single tts task error")
	}
	handleTimeout := time.Now().Add(time.Duration(timeout) * time.Second)
	enginetask := &do.BusinessEngineTask{
		AppId:         task.AppId,
		TenantID:      task.TenantId,
		MainTaskId:    subtitleItem.Id,
		TaskType:      int(taskInfo.TaskType),
		EngineTaskId:  createRes.TaskIds[0],
		EngineStatus:  consts.EngineWorkflowStatusInit,
		HandleTimeout: &handleTimeout, // 15分钟超时
	}
	err = a.engineTaskRepo.CreateWorkflowEngineTask(ctx, enginetask)
	if err != nil {
		return nil, temporal.NewNonRetryableApplicationError(
			"db error",
			"GetWorkflowEngineTask error",
			err)
	}

	return []int64{enginetask.EngineTaskId}, nil
}

func (a *TextTranslateActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return nil, err
	}

	engineTask := result[0]
	// 开启gorm事务
	// 更新字幕记录
	taskDetail := &pb.Task{}
	err = protojson.Unmarshal([]byte(engineTask.TaskInfo), taskDetail)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal task_info error:%v", err)
		return nil, err
	}
	switch taskDetail.GetStatus() {
	case pb.TaskStatus_COMPLETED:
		err = a.HandleSuccess(ctx, subTask, subtitleItems, taskDetail)
		if err != nil {
			g.Log().Errorf(ctx, "handle success error:%v", err)
			return nil, err
		}
	case pb.TaskStatus_FAILED, pb.TaskStatus_QUEUING_TIMEOUT:
		return nil, temporal.NewNonRetryableApplicationError(
			"engine agent translate task failed",
			"task failed",
			fmt.Errorf("engine task failed, status: %v", taskDetail.GetStatus()))
	}

	g.Log().Infof(ctx, "TextTranslateActivity HandleSuccess:%v", result)
	return task, nil
}

func (a *TextTranslateActivity) HandleSuccess(ctx context.Context, subTask *bo.CommentarySubTaskBO, subtitleItems []*do.CommentarySubtitleItem, taskDetail *pb.Task) error {
	result := taskDetail.AgentTranslateOutputContent.Data.Result
	err := db.GetDB().Transaction(func(tx *gorm.DB) error {
		for _, subtitleItem := range subtitleItems {
			for _, postItem := range result.Translation.Subtitles {
				// 如果是直出任务可以用item_index判断，如果是二次编辑，subtitleItem只会有一个，只用筛选返回的id与当时提交的是否一致
				if (a.isFirst && int64(subtitleItem.ItemIdx) == postItem.Id) || (!a.isFirst && taskDetail.AgentTranslateInputContent.IndexList[0] == postItem.Id) {
					g.Log().Infof(ctx, "TextTranslateActivity HandleSuccess subtitleItemId:%v, itemIdx:%v, postItem:%v", subtitleItem.Id, subtitleItem.ItemIdx, postItem)
					if subtitleItem.LatestTextTranslateInfo == nil {
						subtitleItem.LatestTextTranslateInfo = &common.LatestTextTranslateInfo{}
					}
					if a.TranslateBack {
						subtitleItem.LatestTextTranslateInfo.LatestTranslateBackText = subtitleItem.TargetSubtitle
						subtitleItem.BackTranslateText = postItem.Text
					} else {
						subtitleItem.TargetSubtitle = postItem.Text
						subtitleItem.LatestTextTranslateInfo.LatestOriginSubtitle = subtitleItem.OriginSubtitle
						subtitleItem.TextTranslateStatus = consts.SubtitleItemReTranslateStatusNone
					}
				}
			}
			err := a.commentarySubtitleitemRepo.UpdateSubtitleItemTranslateInfo(ctx, subtitleItem, tx)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return temporal.NewNonRetryableApplicationError(
			"HandleSuccess error",
			"db error",
			err)
	}
	return nil
}

func (a *TextTranslateActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	subtitleItems, err := a.getSubtitleItemList(ctx, subTask)
	if err != nil {
		g.Log().Errorf(ctx, "getSubtitleItemList error:%v", err)
		return err
	}
	err = db.GetDB().Transaction(func(tx *gorm.DB) error {
		for _, subtitleItem := range subtitleItems {
			g.Log().Errorf(ctx, "subtitle_item_id:%v, agent text translate HandleFailed", subtitleItem.Id)
			subtitleItem.GenerateVoiceStatus = consts.SubtitleItemRegenerateStatusFailed
			err := a.commentarySubtitleitemRepo.UpdateGenerateVoiceStatus(ctx, subtitleItem.Id, subtitleItem.GenerateVoiceStatus, tx)
			if err != nil {
				g.Log().Errorf(ctx, "handle success error:%v", err)
				return err
			}
		}
		if a.isFirst {
			err := a.commentarySubTaskRepo.UpdateSubTaskStatus(ctx, subTask.Id, int(consts.SubTaskStatusFailed), aerr.Error(), tx)
			if err != nil {
				g.Log().Errorf(ctx, "update sub task status failed:%v", err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		return temporal.NewNonRetryableApplicationError(
			"HandleFailed error",
			"db error",
			err)
	}
	return nil
}

func (a *TextTranslateActivity) getSubtitleItemList(ctx context.Context, subTask *bo.CommentarySubTaskBO) ([]*do.CommentarySubtitleItem, error) {
	var subtitleItems []*do.CommentarySubtitleItem
	var err error
	//区分第一次直出与二次编辑的流程
	if a.isFirst {
		// 直出根据subTaskId获取所有字幕
		subtitleItems, err = a.commentarySubtitleitemRepo.GetSubtitleItemsBySubTaskId(ctx, subTask.Id)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(

				"GetSubtitleItemsBySubTaskId error",
				"db error",
				err)
		}
	} else {
		// 二次编辑只获取当前字幕
		subtitleItem, err := a.commentarySubtitleitemRepo.GetSubtitleItemById(ctx, a.subtitleItemId)
		if err != nil {
			return nil, temporal.NewNonRetryableApplicationError(
				"GetSubtitleItemById error",
				"db error",
				err)
		}
		subtitleItems = []*do.CommentarySubtitleItem{subtitleItem}
	}
	if len(subtitleItems) == 0 {
		return nil, temporal.NewNonRetryableApplicationError(
			"GetSubtitleItemsBySubTaskId no items",
			"db error",
			nil,
		)
	}
	return subtitleItems, nil
}

func TextTranslateProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64, translateBack bool) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Infof(ctx, "TextTranslateProcess:%v, sub_task:%v", task, subTask)
	a := &activities.Processor{}
	isFirst := true
	if subtitleItemId > 0 {
		// 如果传了字幕id，说明是二次编辑
		isFirst = false
	}
	act := &TextTranslateActivity{
		engineTaskRepo:             engine_task_impl.NewEngineTaskRepoImpl(),
		commentarySubtitleitemRepo: commentary_repo_impl.NewCommentarySubtitleItemRepoImpl(),
		commentarySubTaskRepo:      commentary_repo_impl.NewCommentarySubTaskRepoImpl(),
		TranslateBack:              translateBack,
		subtitleItemId:             subtitleItemId,
		isFirst:                    isFirst,
	}
	return a.Process(ctx, act, task, subTask)
}
