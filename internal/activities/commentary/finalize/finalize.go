package finalize

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"go.temporal.io/sdk/temporal"
)

func FinalizeProcess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "FinalizeProcess Process, task: %+v", task)

	mainTaskRepo := impl.NewCommentaryMainTaskRepoImpl()
	subTaskRepo := impl.NewCommentarySubTaskRepoImpl()

	// 获取该主任务下的所有子任务
	subTasks, err := subTaskRepo.GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "获取子任务列表失败, mainTaskId: %d, err: %v", task.Id, err)
		return nil, temporal.NewNonRetryableApplicationError(
			"获取子任务列表失败",
			"db error",
			err)
	}

	// 统计子任务状态
	var (
		totalCount      = len(subTasks)
		failedCount     = 0
		completedCount  = 0
		processingCount = 0
		pendingCount    = 0
	)

	for _, subTask := range subTasks {
		switch subTask.Status {
		case consts.SubTaskStatusFailed:
			failedCount++
		case consts.SubTaskStatusCompleted:
			completedCount++
		case consts.SubTaskStatusProcessing:
			processingCount++
		case consts.SubTaskStatusPending:
			pendingCount++
		}
	}

	g.Log().Infof(ctx, "子任务状态统计 - 总数: %d, 失败: %d, 完成: %d, 处理中: %d, 待处理: %d",
		totalCount, failedCount, completedCount, processingCount, pendingCount)

	// 判断主任务最终状态
	var finalStatus consts.CommentaryMainTaskStatus
	var errMsg string

	if failedCount == totalCount {
		// 全部失败，主任务设置为失败
		finalStatus = consts.MainTaskStatusFailed
		errMsg = fmt.Sprintf("所有子任务都失败了，失败数量: %d", failedCount)
		g.Log().Errorf(ctx, "主任务失败: %s, mainTaskId: %d", errMsg, task.Id)
	} else {
		// 其他情况（有成功的或者还在处理中的），主任务设置为完成
		finalStatus = consts.MainTaskStatusCompleted
		errMsg = ""
		g.Log().Infof(ctx, "主任务完成, mainTaskId: %d, 成功子任务数: %d, 失败子任务数: %d",
			task.Id, completedCount, failedCount)
	}

	// 更新主任务状态
	err = mainTaskRepo.UpdateTaskStatus(ctx, task.Id, finalStatus, errMsg)
	if err != nil {
		g.Log().Errorf(ctx, "更新主任务状态失败, mainTaskId: %d, status: %d, err: %v",
			task.Id, finalStatus, err)
		return nil, temporal.NewNonRetryableApplicationError(
			"更新主任务状态失败",
			"db error",
			err)
	}

	g.Log().Infof(ctx, "主任务状态更新成功, mainTaskId: %d, 最终状态: %d", task.Id, finalStatus)
	return task, nil
}
