package deduct

import (
	"business-workflow/internal/entity/bo"
	"context"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"github.com/gogf/gf/v2/frame/g"
)

// 处理首次直出的扣费逻辑
func DeductProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "DeductProcess Process, task: %+v", task)

	// TODO 处理任务扣款
	return task, nil
}
