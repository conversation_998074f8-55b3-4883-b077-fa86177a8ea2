package tts

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/util"
	"business-workflow/internal/util/media"
	"context"
	"fmt"
	"math"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
)

func getTTSDurationMs(ctx context.Context, ttsAudio string) (ttsMp3durationMs int64, err error) {
	ttsAudioInfo, err := media.GetAudioVideoInfo(ttsAudio)
	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "GetAudioVideoInfo failed, err: %v", err)
		return
	}
	if ttsAudioInfo.AudioInfo.DurationFloat <= 0 {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "GetAudioVideoInfo failed, audio duration is invalid, ttsUrl: %s", ttsAudio)
		err = fmt.Errorf("audio duration is invalid")
		return
	}
	ttsMp3durationMs = int64(ttsAudioInfo.AudioInfo.DurationFloat * 1000)
	return
}

func adjustTTSSpeed(ctx context.Context, ttsAudio string, ttsMp3durationMs int64, subtitleItem *do.CommentarySubtitleItem) (newSpeed float32, newTtsMp3durationMs int64, adjustSpeed bool, newTTSUrl string, err error) {
	if ttsAudio == "" {
		g.Log().Errorf(ctx, "ttsAudio is empty, sub_task_id: %d, subtitle_item_id: %d", subtitleItem.SubTaskId, subtitleItem.Id)
		return 1, 0, false, "", fmt.Errorf("ttsAudio is empty")
	}

	if ttsMp3durationMs <= 0 {
		g.Log().Errorf(ctx, "ttsMp3durationMs is invalid, ttsAudio: %s, duration: %d", ttsAudio, ttsMp3durationMs)
		return 1, 0, false, "", fmt.Errorf("ttsMp3durationMs is invalid")
	}
	oldStartMs, err := util.SrtStringTime2Ms(subtitleItem.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, subtitleStartStr: %s", err, subtitleItem.SubtitleStartStr)
		return 1, 0, false, "", err
	}
	oldEndMs, err := util.SrtStringTime2Ms(subtitleItem.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, SubtitleEndStr: %s", err, subtitleItem.SubtitleEndStr)
		return 1, 0, false, "", err
	}
	oldDurationMs := oldEndMs - oldStartMs
	if oldDurationMs <= 0 {
		g.Log().Errorf(ctx, "oldDurationMs is invalid, oldStartMs: %v, oldEndMs: %v", oldStartMs, oldEndMs)
		return 1, 0, false, "", fmt.Errorf("oldDurationMs is invalid")
	}
	if oldDurationMs >= ttsMp3durationMs {
		return 1, ttsMp3durationMs, true, "", nil
	}
	newSpeed = float32(ttsMp3durationMs) / float32(oldDurationMs)
	//保留两位小数，向上取整
	newSpeed = float32(math.Ceil(float64(newSpeed)*100)) / 100
	//newTtsMp3durationMs = int64(float64(ttsMp3durationMs) / float64(newSpeed))
	if newSpeed > consts.SpeedMax {
		g.Log().Infof(ctx, "newSpeed is greater than max speed, newSpeed: %v, maxSpeed: %v", newSpeed, consts.SpeedMax)
		newSpeed = consts.SpeedMax
	}
	return newSpeed, ttsMp3durationMs, true, "", nil

}
func computeNewSubtitleEndStr(ctx context.Context, ttsMp3durationMs int64, subtitleStartStr string, speed float32) (newSubtitleEndStr string, err error) {
	if ttsMp3durationMs <= 0 {
		g.Log().Errorf(ctx, "ttsMp3durationMs is invalid, ttsMp3durationMs: %d", ttsMp3durationMs)
		return "", fmt.Errorf("ttsMp3durationMs is invalid")
	}
	newTtsMp3durationMs := int64(float64(ttsMp3durationMs) / float64(speed))

	srtStartMs, err := util.SrtStringTime2Ms(subtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, subtitleStartStr: %s", err, subtitleStartStr)
		return "", err
	}
	newSrtEndMs := srtStartMs + newTtsMp3durationMs
	newSubtitleEndStr = strings.Replace(util.SrtTimeMs2StringDot(newSrtEndMs), ".", ",", 1)
	return newSubtitleEndStr, nil
}

func getExpectDuration(ctx context.Context, subtitleItem *do.CommentarySubtitleItem) (int64, error) {
	startMs, err := util.SrtStringTime2Ms(subtitleItem.SubtitleStartStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, subtitleStartStr: %s", err, subtitleItem.SubtitleStartStr)
		return 0, err
	}
	endMs, err := util.SrtStringTime2Ms(subtitleItem.SubtitleEndStr)
	if err != nil {
		g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, SubtitleEndStr: %s", err, subtitleItem.SubtitleEndStr)
		return 0, err
	}
	//tts为空则用原文时间
	if subtitleItem.TTSUrl == "" {
		startMs, err = util.SrtStringTime2Ms(subtitleItem.OriginSubtitleStartStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, OriginSubtitleStartStr: %s", err, subtitleItem.OriginSubtitleStartStr)
			return 0, err
		}
		endMs, err = util.SrtStringTime2Ms(subtitleItem.OriginSubtitleEndStr)
		if err != nil {
			g.Log().Errorf(ctx, "SrtStringTime2Ms failed, err: %v, OriginSubtitleEndStr: %s", err, subtitleItem.OriginSubtitleEndStr)
			return 0, err
		}
		g.Log().Infof(ctx, "tts为空，用原文时间，startMs: %v, endMs: %v", startMs, endMs)
	}
	durationMs := endMs - startMs
	if durationMs <= 0 {
		g.Log().Errorf(ctx, "durationMs is invalid, startMs: %v, endMs: %v", startMs, endMs)
		return 0, err
	}
	return durationMs, nil
}
