package tts

import (
	"business-workflow/internal/entity/bo"
	"context"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"github.com/gogf/gf/v2/frame/g"
)

// 处理二次编辑tts扣费逻辑
func TTSDeductProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "CleanUpProcess Process, task: %+v", task)

	// TODO 处理任务扣款
	return task, nil
}
