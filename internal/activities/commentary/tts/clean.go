package tts

import (
	"business-workflow/internal/entity/bo"
	"context"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"github.com/gogf/gf/v2/frame/g"
)

// 处理二次编辑tts扣费回滚等清理逻辑
func TTSCleanUpProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "TTSCleanProcess Process, task: %+v", task)

	// TODO 检查item generate_voice状态是否为成功，未成功进行任务失败处理退费
	return task, nil
}
