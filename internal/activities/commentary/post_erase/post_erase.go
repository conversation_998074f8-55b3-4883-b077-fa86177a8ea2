package posterase

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common/erase"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"business-workflow/internal/repo/inpainting"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	hobs "business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type PostEraseActivity struct {
	ocrDb       inpainting.IInpaintingOcrRepo
	subTaskRepo commentary_repo.ICommentarySubTaskRepo
	engineRepo  enginetaskrepo.IEngineTaskRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultPostEraseNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultPostEraseRetryError",
		"InvalidOrderError",
		nil)
)

func (a *PostEraseActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	if task.EraseMode == consts.EraseModeOff {
		return true
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PreEraseActivity skip, subTaskId: %v", subTask.Id)
		return true
	}
	if ocrTask.Status == consts.InpaintingStatusPostprocrssComplete {
		return true
	}
	return false
}

func (a *PostEraseActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	// 实现等待逻辑...
	/*
		return temporal.NewNonRetryableApplicationError( // 不需要重试的错误
			"invalid order format",
			"InvalidOrderError",
			nil,
		)
	*/
	/*
		return temporal.NewApplicationError( // 需要重试的错误
			"invalid order format",
			"InvalidOrderError",
			nil,
		)
	*/
	return nil
}

func (a *PostEraseActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	// 实现提交逻辑...
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, GetOcrTaskBySubId failed, err: %v", err)
		return nil, err
	}
	engineTask, err := a.engineRepo.GetWorkflowEngineTaskByBidAndType(ctx, ocrTask.Bid, int(omniProto.TaskType_VIDEO_TRANSLATION_POST_MERGE))
	if err != nil {
		g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, GetWorkflowEngineTaskByBid failed, err: %v", err)
		return nil, noRetryErr
	}
	if engineTask.Id > 0 {
		g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
	}
	if ocrTask.Status != consts.InpaintingStatusEraseComplete {
		if engineTask.Id > 0 && engineTask.Bid == ocrTask.Bid {
			g.Log().Warningf(ctx, "PostEraseActivity HandleSubmit, engineTask is not nil: %v", engineTask)
			return []int64{engineTask.EngineTaskId}, nil
		} else {
			g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, ocrTask.Stutus is not InpaintingStatusPreprocessComplete: %v", ocrTask.Status)
			return nil, noRetryErr
		}
	}
	enginetask, err := a.submitToEngine(ctx, task, subTask, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleSubmit, submitToEngine failed, err: %v", err)
		return nil, err
	}
	return []int64{enginetask.EngineTaskId}, nil
}

func (a *PostEraseActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask) (*do.BusinessEngineTask, error) {
	mergeReq := &erase.MergeV2SubmitReq{}
	mergeReq.Id = ocrTask.Bid
	mergeReq.TargetObject = processor.GetObjectName(subTask.TenantId, subTask.Id, "post_output.mp4", "post_merge")
	// reqBase := &omniProto.TaskBase{
	// 	ExtraMap: map[string]string{
	// 		// "task_cost": fmt.Sprintf("%d", int(subTask.VideoDuration)),
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", subTask.VideoDuration),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_postprocess_%v", ocrTask.Bid)
	taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_POST_MERGE
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id

	data, err := json.Marshal(mergeReq)
	if err != nil {
		g.Log().Error(ctx, "json.Marshal failed, err: %v", err)
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, err: %v", err)
		return nil, retryErr
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Error(ctx, "CreateTask PreEraseActivity failed, len is not 1: %v", len(createRes.TaskIds))
		return nil, retryErr
	}
	ocrTask.PostprocessSubmitAt = time.Now()
	ocrTask.Status = consts.InpaintingStatusPostprocrssSubmitted
	_, cdnUrl, err := hobs.GetOsClient().GetObjectUrl(mergeReq.TargetObject)
	if err != nil {
		g.Log().Error(ctx, "GetObjectUrl failed, err: %v", err)
		return nil, err
	}
	ocrTask.NotitleVideoUrl = cdnUrl

	enginetask := &do.BusinessEngineTask{
		EngineTaskId: createRes.TaskIds[0],
		MainTaskId:   subTask.MainTaskId,
		Bid:          ocrTask.Id,
		TenantID:     ocrTask.TenantID,
		TaskType:     int(taskInfo.TaskType),
		EngineStatus: consts.EngineWorkflowStatusInit,
	}

	err = impaintImpl.NewInpaintingOcrTaskImpl().CreateEngineTasksAndUpdateOcrTask(ctx, []*do.BusinessEngineTask{enginetask}, ocrTask)
	if err != nil {
		return nil, err
	}
	return enginetask, nil
}

func (a *PostEraseActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	// 处理成功...
	g.Log().Infof(ctx, "PostEraseActivity HandleResult:%+v", subTask)
	if len(result) == 0 {
		g.Log().Errorf(ctx, "PostEraseActivity HandleResult, result is empty")
		return task, noRetryErr
	}
	engineTask := result[0]
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity HandleResult, unmarshal post_erase engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return task, noRetryErr
	}
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleSuccess failed, err: %v", err)
			return task, noRetryErr
		}
		return task, nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask, omniTask)
		if err != nil {
			g.Log().Errorf(ctx, "PostEraseActivity handleFail failed, err: %v", err)
		}
		return task, noRetryErr
	} else {
		g.Log().Warningf(ctx, "PostEraseActivity HandleResult, status maybe wrong, engineStatus: %v", engineTask.EngineStatus)
		return task, retryErr
	}
}

func (a *PostEraseActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Infof(ctx, "PostEraseActivity handleSuccess, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	// 解析结果
	extractRes := &erase.MergeQueryV2Res{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), extractRes)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, extractRes.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, get ocr task by sub id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	// 合成完成
	ocrTask.Status = consts.InpaintingStatusPostprocrssComplete
	ocrTask.PostprocessEndAt = time.Now()
	// 同时更新链接到子任务里
	subTaskD, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleSuccess, get sub task by id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	subTaskD.NoSubtitleMaterialHighlightUrl = ocrTask.NotitleVideoUrl
	return a.ocrDb.UpdateOcrTaskAndSaveNotitleVideoUrl(ctx, ocrTask, ocrTask.NotitleVideoUrl)
}

func (a *PostEraseActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO,
	engineTask *do.BusinessEngineTask, omniTask *omniProto.Task) error {
	g.Log().Errorf(ctx, "PostEraseActivity handleFail, subTaskId: %v, engineTaskId: %v",
		subTask.Id, engineTask.EngineTaskId)
	extractRes := &erase.MergeQueryV2Res{}
	err := json.Unmarshal([]byte(omniTask.CommonOutputContent), extractRes)
	if err != nil {
		g.Log().Errorf(ctx, "PostEraseActivity handleFail, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	if extractRes.Code == erase.AiCodeTaskTimeout {
		/// 重试合成
		g.Log().Errorf(ctx, "PostEraseActivity handleFail, task timeout, subTaskId: %v, engineTaskId: %v",
			subTask.Id, engineTask.EngineTaskId)
	}
	return processor.HandleSubTaskFail(ctx, subTask, int(extractRes.Code), extractRes.Msg)
}

func (a *PostEraseActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	return nil
}

func PostEraseProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "PostEraseProcess: task%v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &PostEraseActivity{
		ocrDb:       impaintImpl.NewInpaintingOcrTaskImpl(),
		subTaskRepo: commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
		engineRepo:  impl.NewEngineTaskRepoImpl(),
	}, task, subTask)
}
