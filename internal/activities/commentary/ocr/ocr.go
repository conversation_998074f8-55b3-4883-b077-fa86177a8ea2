package ocr

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/common"
	"business-workflow/internal/common/erase"
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/processor"
	"business-workflow/internal/repo/commentary_repo"
	commentaryRepoImpl "business-workflow/internal/repo/commentary_repo/impl"
	"business-workflow/internal/repo/inpainting"
	impaintImpl "business-workflow/internal/repo/inpainting/impl"
	"business-workflow/internal/util/id_generator"
	"business-workflow/internal/util/media"
	hobs "business-workflow/internal/util/obs"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omniProto "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"gorm.io/gorm"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/temporal"
)

// 确保在init时自动注册

type OCRActivity struct {
	ocrDb       inpainting.IInpaintingOcrRepo
	subTaskRepo commentary_repo.ICommentarySubTaskRepo
}

var (
	noRetryErr = temporal.NewNonRetryableApplicationError(
		"defaultOcrNoRetryError",
		"InvalidOrderError",
		nil)

	retryErr = temporal.NewApplicationError( // 需要重试的错误
		"defaultOcrRetryError",
		"InvalidOrderError",
		nil)
)

func (a *OCRActivity) IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool {
	if task.EraseMode == consts.EraseModeOff {
		return true
	}
	ocrTask, err := a.ocrDb.GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		g.Log().Warningf(ctx, "OCRActivity skip, subTaskId: %v", subTask.Id)
		return false
	}
	if ocrTask.Status == consts.InpaintingStatusOcrComplete {
		return true
	}
	return false
}

func (a *OCRActivity) WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error {
	return nil
}

func (a *OCRActivity) HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error) {
	subTaskDO, err := a.subTaskRepo.GetSubTaskById(ctx, subTask.Id)
	if err != nil {
		g.Log().Errorf(ctx, "OCRActivity handleSubmit get subTask failed, subTaskId: %v, err: %v", subTask.Id, err)
		return nil, noRetryErr
	}
	objectName, err := processor.GetObjectNameByHttpsUrl(subTaskDO.MaterialHighlightUrl)
	if err != nil {
		g.Log().Errorf(ctx, "OCRActivity handleSubmit get objectName failed, subTaskId: %v, videoUrl: %v err: %v",
			subTask.Id, subTaskDO.MaterialHighlightUrl, err)
		return nil, noRetryErr
	}
	directUrl, _, err := hobs.GetOsClient().GetObjectUrl(objectName)
	_, err = media.GetAudioVideoInfo(directUrl)
	if err != nil {
		g.Log().Errorf(ctx, "OCRActivity handleSubmit, GetAudioVideoInfo failed, subTaskId: %v, videoUrl: %v, err: %v",
			subTask.Id, directUrl, err)
		return nil, retryErr
	}
	// 实现提交逻辑...
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, subTask.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			ocrTask, err = a.createOcrTask(ctx, task, subTaskDO)
			if err != nil {
				return nil, temporal.NewApplicationError("submit to engine failed", "submitEngineFailed")
			}
		} else {
			g.Log().Errorf(ctx, "GetOcrTaskBySubId failed, subTaskId: %v, err: %v", subTask.Id, err)
			return nil, retryErr
		}
	}
	enginetask, err := a.submitToEngine(ctx, task, subTask, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "submitToEngine ocr to engine failed, mainTaskId: %v, subTaskId: %v, ocrId: %v, err: %v",
			subTask.MainTaskId, subTask.Id, ocrTask.Id, err)
		return nil, retryErr
	}
	return []int64{enginetask.EngineTaskId}, nil
}

func (a *OCRActivity) createOcrTask(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *do.CommentarySubTask) (*do.WorkflowOcrTask, error) {
	ocrTask := &do.WorkflowOcrTask{}
	ocrTask.AppId = task.AppId
	ocrTask.MainTaskId = subTask.MainTaskId
	ocrTask.SubTaskId = subTask.Id
	var err error
	ocrTask.Bid, err = id_generator.GenerateId()
	if err != nil {
		g.Log().Errorf(ctx, "GenerateId failed, err: %v", err)
		return nil, err
	}
	ocrTask.TenantID = subTask.TenantId
	ocrTask.Status = consts.InpaintingStatusOcrInit
	ocrTask.EraseEdition = task.EraseEdition
	ocrTask.VideoDuration = float32(subTask.VideoDuration)
	ocrTask.VideoUrl = subTask.MaterialHighlightUrl
	data, err := json.Marshal(task.OcrRectInfo)
	if err != nil {
		g.Log().Warningf(ctx, "Marshal ocr info failed, err: %s, taskId: %v", err.Error(), ocrTask.SubTaskId)
	}
	ocrTask.OcrRectInfo = string(data)
	ocrTask.UserRect = 0 // ??? 怎么判断是否有框，但我们默认是无框的
	// ocrTask.OcrRectInfo = subTask.OcrRectInfo
	g.Log().Infof(ctx, "createOcrTask, ocrTask: %v", ocrTask)
	return ocrTask, impaintImpl.NewInpaintingOcrTaskImpl().CreateOcrTask(ctx, ocrTask)
}

func (a *OCRActivity) buildOcrReq(ctx context.Context, ocrTask *do.WorkflowOcrTask) *erase.ExtractReq {
	extractReq := &erase.ExtractReq{
		Id: ocrTask.Bid,
	}
	extractReq.VideoUrl = ocrTask.VideoUrl
	extractReq.StartMSecond = -1
	extractReq.EndMSecond = -1
	extractReq.FullScreen = ocrTask.FullScreen
	extractReq.Language = processor.GetOcrLanguage(ocrTask.LangId)
	rect := common.Rectangle{}
	err := json.Unmarshal([]byte(ocrTask.OcrRectInfo), &rect)
	if err != nil {
		g.Log().Warningf(ctx, "Unmarshal ocr info failed, err: %s, taskId: %v", err.Error(), ocrTask.SubTaskId)
	}
	if len(rect.TopLeft) == 2 && len(rect.BottomRight) == 2 {
		///如果原任务是带SRT的，又有框，那就不需要提取？
		///当前版本中，提取总是必要的
		extractReq.Regions = []float64{rect.TopLeft[0], rect.TopLeft[1], rect.BottomRight[0], rect.BottomRight[1]}
	}
	return extractReq
}

func (a *OCRActivity) submitToEngine(ctx context.Context, task *bo.CommentaryMainTaskBO,
	subTask *bo.CommentarySubTaskBO, ocrTask *do.WorkflowOcrTask) (*do.BusinessEngineTask, error) {

	req := a.buildOcrReq(ctx, ocrTask)
	if req == nil {
		g.Log().Errorf(ctx, "buildOcrReq failed, mainTaskId: %v, subTaskId: %v, ocrId: %v",
			task.Id, ocrTask.SubTaskId, ocrTask.Id)
		return nil, temporal.NewNonRetryableApplicationError(
			"buildOcrReq",
			"GetWorkflowEngineTask error",
			nil)
	}
	// reqBase := &omniProto.TaskBase{
	// 	ExtraMap: map[string]string{
	// 		// "task_cost": fmt.Sprintf("%d", int(subTask.VideoDuration)),
	// 		consts.BaseTaskCost:                 fmt.Sprintf("%v", subTask.VideoDuration),
	// 		consts.BaseTaskCommentrayMainTaskID: strconv.FormatInt(task.Id, 10),
	// 		consts.BaseTaskCommentraySubTaskID:  strconv.FormatInt(subTask.Id, 10),
	// 		consts.BaseTaskCommentrayBizMode:    strconv.FormatInt(int64(task.BizMode), 10),
	// 		consts.BaseTaskCommentrayTaskType:   strconv.FormatInt(int64(task.TaskType), 10),
	// 	},
	// }
	reqBase := conv.CommentaryMainTaskBOToBaseTask(task, subTask, int64(omniProto.TaskSource_INTERNAL_SOURCE),
		map[string]string{
			consts.BaseTaskCost: fmt.Sprintf("%v", subTask.VideoDuration),
		})

	taskInfo := &omniProto.TaskInfo{}
	taskInfo.Name = fmt.Sprintf("commentary_extract_%v", ocrTask.Bid)
	taskInfo.TaskType = omniProto.TaskType_VIDEO_TRANSLATION_OCR_EXTRACT
	taskInfo.Quantity = 1
	taskInfo.RefererId = task.Id

	data, err := json.Marshal(req)
	if err != nil {
		g.Log().Error(ctx, "json.Marshal")
		return nil, err
	}
	taskInfo.CommonInputContent = string(data)

	createReq := &omniProto.CreateTaskReq{
		TaskBase: reqBase,
		TaskList: []*omniProto.TaskInfo{taskInfo},
	}

	createRes, err := omni_engine.GetOmniEngineClient().CreateTask(ctx, createReq)
	if err != nil {
		g.Log().Errorf(ctx, "create single ocr engine task failed, err:%v", err)
		return nil, err
	}
	if len(createRes.TaskIds) != 1 {
		g.Log().Errorf(ctx, "create single ocr engine task failed, err: len is not 1, %v", len(createRes.TaskIds))
		return nil, retryErr
	}

	ocrTask.OcrSubmitAt = time.Now()
	ocrTask.Status = consts.InpaintingStatusOcrSubmitted

	enginetask := &do.BusinessEngineTask{
		EngineTaskId: createRes.TaskIds[0],
		MainTaskId:   subTask.MainTaskId,
		Bid:          ocrTask.Bid,
		TenantID:     ocrTask.TenantID,
		TaskType:     int(omniProto.TaskType_VIDEO_TRANSLATION_OCR_EXTRACT),
		EngineStatus: consts.EngineWorkflowStatusInit,
	}

	err = impaintImpl.NewInpaintingOcrTaskImpl().CreateEngineTasksAndUpdateOcrTask(ctx, []*do.BusinessEngineTask{enginetask}, ocrTask)
	if err != nil {
		g.Log().Errorf(ctx, "create ocr task failed, err: %v", err)
		return nil, err
	}
	return enginetask, nil
}

func (a *OCRActivity) HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, result []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) {
	// 处理成功...
	g.Log().Infof(ctx, "ocr HandleResult, mainTaskId: %v, subTaskId: %v", subTask.MainTaskId, subTask.Id)
	if len(result) != 1 {
		g.Log().Errorf(ctx, "ocr result len: %v", len(result))
		return task, fmt.Errorf("ocr result len is not 1: %v ", len(result))
	}

	engineTask := result[0]
	g.Log().Infof(ctx, "ocr HandleResult, mainTaskId: %v, subTaskId: %v, status: %v", subTask.MainTaskId, subTask.Id, engineTask.EngineStatus)
	if engineTask.EngineStatus == consts.EngineWorkflowStatusSuccess {
		err := a.handleSuccess(ctx, task, subTask, engineTask)
		if err != nil {
			return task, temporal.NewApplicationError( // 需要重试的错误
				"handle ocr result failed",
				"HandleOcrResultFailed",
			)
		}
		return task, nil
	} else if engineTask.EngineStatus == consts.EngineWorkflowStatusFailed ||
		engineTask.EngineStatus == consts.EngineWorkflowStatusTimeout {
		err := a.handleFail(ctx, task, subTask, engineTask)
		if err != nil {
			return task, temporal.NewApplicationError( // 需要重试的错误
				"handle ocr result failed",
				"HandleOcrFailFailed",
			)
		}
		return task, nil
	} else {
		return task, temporal.NewApplicationError( // 需要重试的错误
			"handle ocr result, engine status not right",
			"HandleOcrResultFailed",
		)
	}
}

func (a *OCRActivity) handleSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask) error {
	// 反序列化
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal ocr engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	g.Log().Infof(ctx, "ocr handleSuccess, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	// 解析结果
	extractRes := &erase.ExtractResultRes{}
	err = json.Unmarshal([]byte(omniTask.CommonOutputContent), extractRes)
	if err != nil {
		g.Log().Errorf(ctx, "unmarshal ocr engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	ocrTask, err := impaintImpl.NewInpaintingOcrTaskImpl().GetOcrTaskBySubId(ctx, extractRes.Id)
	if err != nil {
		g.Log().Errorf(ctx, "get ocr task by sub id failed, err: %v, subTaskId: %v, engineTaskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	return a.handleOcrResult(ctx, ocrTask, extractRes)
}

func (a *OCRActivity) handleOcrResult(ctx context.Context, ocrTask *do.WorkflowOcrTask, extractRes *erase.ExtractResultRes) error {
	g.Log().Infof(ctx, "ocr handleOcrResult, ocrTaskId: %v", ocrTask.Id)
	texts, _ := json.Marshal(extractRes.MergedTexts)
	var err error
	ocrTask.TextsCompress, err = processor.BytesCompress(texts)
	if err != nil {
		g.Log().Errorf(ctx, "compress texts failed, err: %v", err)
		return err
	}
	ocrRectInfo, hasSubtitle, err := a.handleOcrRectInfo(ctx, extractRes.Regions[0])
	if err != nil {
		g.Log().Errorf(ctx, "handle ocr rect info failed, err: %v", err)
		return err
	}
	// 如果原来的ocrRectInfo为空，才更新
	if len(ocrTask.OcrRectInfo) < 5 {
		ocrTask.OcrRectInfo = ocrRectInfo
	} else {
		// 有用户指定区域，设置为1
		ocrTask.UserRect = 1
	}
	boxesStr, _ := json.Marshal(extractRes.BBoxesFile)
	if len(extractRes.Regions) == 1 {
		regionStr, _ := json.Marshal(extractRes.Regions[0])
		ocrTask.Region = string(regionStr)
	}
	ocrTask.BboxsCompress, err = processor.BytesCompress(boxesStr)
	if err != nil {
		g.Log().Errorf(ctx, "compress bboxes failed, err: %v", err)
		return err
	}
	// 下一步：预处理
	ocrTask.Status = consts.InpaintingStatusOcrComplete
	if !hasSubtitle {
		g.Log().Infof(ctx, "ocr handleOcrResult, subTaskId: %v, ocrTaskId: %v, 无字幕视频 hasSubtitle: %v", ocrTask.SubTaskId, ocrTask.Id, hasSubtitle)
		// 无字幕，直接成功
		ocrTask.NotitleVideoUrl = ocrTask.VideoUrl
		ocrTask.Status = consts.InpaintingStatusEraseComplete
	}
	return impaintImpl.NewInpaintingOcrTaskImpl().UpdateOcrTask(ctx, ocrTask)
}

func (a *OCRActivity) handleOcrRectInfo(ctx context.Context, region []float64) (string, bool, error) {
	RegionTopLeft := []float64{region[0], region[1]}
	RegionBottomRight := []float64{region[2], region[3]}
	topLeft := []float64{RegionTopLeft[0], RegionTopLeft[1]}
	topRight := []float64{RegionBottomRight[0], RegionTopLeft[1]}
	bottomLeft := []float64{RegionTopLeft[0], RegionBottomRight[1]}
	bottomRight := []float64{RegionBottomRight[0], RegionBottomRight[1]}
	//赋值
	rect := common.Rectangle{}
	rect.TopLeft = topLeft
	rect.TopRight = topRight
	rect.BottomLeft = bottomLeft
	rect.BottomRight = bottomRight
	data, err := json.Marshal(&rect)
	if err != nil {
		return "", false, err
	}
	if region[0] < 0 && region[1] < 0 && region[2] < 0 && region[3] < 0 {
		// 无字幕视频
		return string(data), false, nil
	}
	return string(data), true, nil
}

func (a *OCRActivity) handleFail(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, engineTask *do.BusinessEngineTask) error {
	g.Log().Errorf(ctx, "handle ocr fail, subTaskId: %v, taskId: %v", subTask.Id, task.Id)
	omniTask := &omniProto.Task{}
	err := json.Unmarshal([]byte(engineTask.TaskInfo), omniTask)
	if err != nil {
		g.Log().Errorf(ctx, "OCRActivity, unmarshal engine task info failed, err: %v, subTaskId: %v, taskId: %v",
			err, subTask.Id, engineTask.EngineTaskId)
		return err
	}
	g.Log().Infof(ctx, "OCRActivity, handleFail, mainTaskId: %v, subTaskId: %v, engineTaskId: %v",
		task.Id, subTask.Id, engineTask.EngineTaskId)
	res := &erase.ExtractResultRes{}
	err = json.Unmarshal([]byte(omniTask.CommonOutputContent), res)
	if err != nil {
		return fmt.Errorf("OCRActivity, handleFail 完成时返回, 解析结果失败, taskId: %v, err: %s", res.Id, err.Error())
	}
	if res.Code == erase.AiCodeCudaOutOfMemory ||
		res.Code == erase.AiCodeTaskTimeout ||
		res.Code == erase.AiCodeTaskNotExist {
		/// 可重试的擦除
	}
	/// to do... 其他错误， 任务失败
	return processor.HandleSubTaskFail(ctx, subTask, int(res.Code), res.Msg)
}
func (a *OCRActivity) HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error {
	return nil
}
func OCRProcess(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "OCRProcess: task:%+v, sub_task:%+v", task, subTask)
	a := &activities.Processor{}
	return a.Process(ctx, &OCRActivity{
		ocrDb:       impaintImpl.NewInpaintingOcrTaskImpl(),
		subTaskRepo: commentaryRepoImpl.NewCommentarySubTaskRepoImpl(),
	}, task, subTask)
}
