package deduction

import (
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	commentary_repo_impl "business-workflow/internal/repo/commentary_repo/impl"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	omni_balance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	omni_tenant "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_tenant"
	"go.temporal.io/sdk/temporal"
)

func DeductionProcess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	ctx = context.WithValue(ctx, trace.ReqId, task.RequestID)
	g.Log().Info(ctx, "DeductionProcess task:%v", task)

	// 获取所有子任务
	subTasks, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().GetSubTasksByMainTaskId(ctx, task.Id)
	if err != nil {
		g.Log().Errorf(ctx, "获取子任务列表失败: %v", err)
		return handleDeductionFailure(ctx, task, fmt.Errorf("获取子任务列表失败: %w", err))
	}

	// 转换为BO对象
	subTaskBOs := make([]*bo.CommentarySubTaskBO, len(subTasks))
	for i, st := range subTasks {
		subTaskBOs[i] = &bo.CommentarySubTaskBO{
			Id:            st.Id,
			VideoDuration: st.VideoDuration,
		}
	}

	// 执行扣款逻辑
	if err := executeDeduction(ctx, task, subTaskBOs); err != nil {
		return handleDeductionFailure(ctx, task, err)
	}

	// 扣款成功，更新状态
	return handleDeductionSuccess(ctx, task)
}

func executeDeduction(ctx context.Context, task *bo.CommentaryMainTaskBO, subTasks []*bo.CommentarySubTaskBO) error {
	// 1. 通过omni-tenant服务获取apikey_id
	tenantClient := omni_engine.GetOmniTenantClient()
	tenantReq := &omni_tenant.GetTenantApikeyByApikeyReq{
		Apikey: task.ApiKey,
	}
	tenantResp, err := tenantClient.GetTenantApikeyByApikey(ctx, tenantReq)
	if err != nil {
		g.Log().Errorf(ctx, "获取租户apikey信息失败: %v apiKey:%v", err, task.ApiKey)
		return fmt.Errorf("获取租户apikey信息失败: %w", err)
	}

	if tenantResp.TenantApikeyInfo == nil {
		return fmt.Errorf("租户apikey信息为空")
	}

	apikeyId := tenantResp.TenantApikeyInfo.ApikeyId

	// 2. 计算总时长
	var totalDuration float64
	for _, subTask := range subTasks {
		totalDuration += subTask.VideoDuration
	}

	// 3. 构建扣款请求的segments
	segments := make([]*omni_balance.VideoCommentarySegment, len(subTasks))
	for i, subTask := range subTasks {
		segments[i] = &omni_balance.VideoCommentarySegment{
			SegmentId:         fmt.Sprintf("subTask_%d", subTask.Id),
			GeneratedDuration: int64(subTask.VideoDuration),
		}
	}

	// 4. 确定业务类型
	OpenapiBusinessType := pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_CLIP
	if task.TaskType == consts.TaskTypeCommentary {
		OpenapiBusinessType = pb.BusinessScenariosType_BUSINESS_VIDEO_COMMENTARY_HIGHLIGHT_COMMENTARY
	}

	// 5. 调用omni-balance服务进行扣款
	balanceClient := omni_engine.GetOmniBalanceAVLBalance()
	exOrderId := fmt.Sprintf("mainTask_%d", task.Id)
	deductionReq := &omni_balance.AVoiceVideoCommentaryDeductionCreditsRequest{
		AppId:               task.AppId,
		TenantId:            task.TenantId,
		ApikeyId:            apikeyId,
		OpenapiBusinessType: int32(OpenapiBusinessType), // 扣费类型
		OriginalDuration:    int64(totalDuration),
		Segments:            segments,
		MainTaskId:          exOrderId,
		ExOrderId:           exOrderId,
		EraseType:           int32(task.EraseMode), // 使用擦除模式作为擦除类型
		EraseMode:           int32(task.EraseMode),
	}

	_, err = balanceClient.AVoiceVideoCommentaryDeductionCredits(ctx, deductionReq)
	if err != nil {
		g.Log().Errorf(ctx, "视频解说扣款失败: %v", err)
		return fmt.Errorf("视频解说扣款失败: %w", err)
	}

	g.Log().Infof(ctx, "视频解说扣款成功，任务ID: %d, 总时长: %.2f秒, apikey_id: %d", task.Id, totalDuration, apikeyId)
	return nil
}

func handleDeductionFailure(ctx context.Context, task *bo.CommentaryMainTaskBO, err error) (*bo.CommentaryMainTaskBO, error) {
	g.Log().Error(ctx, "扣款失败: %v", err)

	// 更新主任务支付状态
	updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusFailed)

	// 更新子任务支付状态
	updateSubTaskPayOrderStatus(ctx, task.Id, consts.PayOrderStatusFailed)

	return task, temporal.NewNonRetryableApplicationError("DeductionError", "deduction error", nil)
}

func handleDeductionSuccess(ctx context.Context, task *bo.CommentaryMainTaskBO) (*bo.CommentaryMainTaskBO, error) {
	// 更新主任务支付状态为已支付
	updatePayOrderStatus(ctx, task.Id, consts.PayOrderStatusPaid)

	// 更新子任务支付状态为已支付
	updateSubTaskPayOrderStatus(ctx, task.Id, consts.PayOrderStatusPaid)

	// 更新子任务支付状态已在 updateSubTaskPayOrderStatus 中处理
	return task, nil
}

func updateTaskStatus(ctx context.Context, taskId int64, status consts.CommentaryMainTaskStatus, errMsg string) {
	if err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().UpdateTaskStatus(ctx, taskId, status, errMsg); err != nil {
		g.Log().Error(ctx, "UpdateTaskStatus err:%v", err)
	}
}

func updatePayOrderStatus(ctx context.Context, taskId int64, payOrderStatus consts.PayOrderStatus) {
	fields := map[string]interface{}{
		"pay_order_status": payOrderStatus,
	}
	if err := commentary_repo_impl.NewCommentaryMainTaskRepoImpl().UpdateTaskFields(ctx, taskId, fields); err != nil {
		g.Log().Error(ctx, "UpdatePayOrderStatus err:%v", err)
	}
}

func updateSubTaskPayOrderStatus(ctx context.Context, mainTaskId int64, payOrderStatus consts.PayOrderStatus) {
	// 批量更新主任务下所有子任务的支付状态
	rowsAffected, err := commentary_repo_impl.NewCommentarySubTaskRepoImpl().UpdateSubTaskPayOrderStatusByMainTaskId(ctx, mainTaskId, payOrderStatus)
	if err != nil {
		g.Log().Error(ctx, "UpdateSubTaskPayOrderStatusByMainTaskId err:%v, mainTaskId:%d", err, mainTaskId)
		return
	}

	g.Log().Info(ctx, "批量更新子任务支付状态成功, mainTaskId:%d, payOrderStatus:%d, rowsAffected:%d", mainTaskId, payOrderStatus, rowsAffected)
}
