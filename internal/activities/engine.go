package activities

import (
	"business-workflow/internal/common/omni_engine"
	"business-workflow/internal/consts"
	"business-workflow/internal/repo/engine_task_repo/impl"

	"context"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"google.golang.org/protobuf/encoding/protojson"
)

func BackgroundQueryEngineTaskDetail(ctx context.Context) {
	ctx = gctx.WithCtx(ctx)
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	omniTaskIds, err := engineTaskRepo.GetAllUnfinishedOmniTaskIds(ctx)
	if err != nil {
		return
	}

	// 分批处理任务，每批100个，顺序执行
	batchSize := 100
	omniClient := omni_engine.GetOmniEngineClient()

	for i := 0; i < len(omniTaskIds); i += batchSize {
		end := i + batchSize
		if end > len(omniTaskIds) {
			end = len(omniTaskIds)
		}

		batch := omniTaskIds[i:end]
		// 批量获取任务结果
		resp, err := omniClient.GetTaskDetail(ctx, &pb.GetTaskDetailRequest{
			TaskIds:     batch,
			IgnoreOwner: true,
		})
		if err != nil {
			g.Log().Errorf(ctx, "GetTaskDetail failed: %v", err)
			continue
		}
		g.Log().Infof(ctx, "GetTaskDetail success, taskIds: %v, results: %v", batch, resp.Tasks)

		// 处理每个任务的结果
		var wg sync.WaitGroup
		for _, omniTask := range resp.Tasks {
			wg.Add(1)
			go func(task *pb.Task) {
				defer wg.Done()
				if err := processOmniTaskResult(ctx, task); err != nil {
					g.Log().Errorf(ctx, "processOmniTaskResult failed: %v", err)
				}
			}(omniTask)
		}
		wg.Wait()
	}

	g.Log().Infof(ctx, "processUnfinishedTasks end")
	return
}

func processOmniTaskResult(ctx context.Context, task *pb.Task) error {
	// 将查询结果入库
	status := consts.EngineWorkflowStatusInit
	switch task.Status {
	case pb.TaskStatus_IN_PROGRESS:
		status = consts.EngineWorkflowStatusProcessing
	case pb.TaskStatus_COMPLETED:
		status = consts.EngineWorkflowStatusSuccess
	case pb.TaskStatus_FAILED:
		status = consts.EngineWorkflowStatusFailed
	case pb.TaskStatus_QUEUING_TIMEOUT:
		status = consts.EngineWorkflowStatusTimeout
	}
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	info, err := protojson.Marshal(task)
	if err != nil {
		return err
	}
	err = engineTaskRepo.UpdateTaskStatusAndInfo(ctx, task.Id, status, string(info))
	if err != nil {
		return err
	}
	return nil
}
