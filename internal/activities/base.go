package activities

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/engine_task_repo/impl"
	"context"
	"errors"
	"runtime/debug"
	"time"

	"go.temporal.io/sdk/temporal"

	"github.com/gogf/gf/v2/frame/g"
	"go.temporal.io/sdk/activity"
)

type IActivity interface {
	IsSkip(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) bool                                                                      // 判断是否直接跳转到下个activity
	WaitCondition(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) error                                                              //在正式进入activity逻辑前的条件校验
	HandleSubmit(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) ([]int64, error)                                                    // 提交任务到engine
	HandleResult(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, results []*do.BusinessEngineTask) (*bo.CommentaryMainTaskBO, error) // 处理任务结果
	HandleFailed(ctx context.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, aerr *temporal.ApplicationError) error                              // 处理任务失败，主要是更新状态
}

// LangDetectActivity 提供活动基类，封装通用逻辑
type Processor struct {
}

func (a *Processor) HandleQuery(ctx context.Context, taskIds []int64) ([]*do.BusinessEngineTask, error) {
	engineTaskRepo := impl.NewEngineTaskRepoImpl()
	if len(taskIds) == 0 {
		g.Log().Errorf(ctx, "HandleQuery no taskIds")
		return nil, nil
	}
	// 在循环里面每隔5s轮询WorkflowEngineTask表，直到状态为成功或失败
	for {
		engineTasks, err := engineTaskRepo.GetWorkflowEngineTasks(ctx, taskIds)
		if err != nil {
			g.Log().Errorf(ctx, "GetWorkflowEngineTask error:%v", err)
			// 数据库报错不需要重试
			return nil, temporal.NewNonRetryableApplicationError(
				"GetWorkflowEngineTasks error",
				"db error",
				err)
		}
		if len(engineTasks) == 0 {
			g.Log().Errorf(ctx, "GetWorkflowEngineTask no task")
			return nil, temporal.NewNonRetryableApplicationError(
				"GetWorkflowEngineTasks no task",
				"db error",
				nil)
		}
		allComplete := true
		for _, engineTask := range engineTasks {
			// 如果超过最大超时时间，也不用继续重试
			if engineTask.HandleTimeout != nil && engineTask.HandleTimeout.Before(time.Now()) {
				return nil, temporal.NewNonRetryableApplicationError(
					"GetWorkflowEngineTask timeout",
					"timeout",
					nil)
			}

			// 继续等待
			if engineTask.EngineStatus <= consts.EngineWorkflowStatusProcessing {
				allComplete = false
				break
			}
		}
		if allComplete {
			return engineTasks, nil
		}
		time.Sleep(5 * time.Second)

	}
}
func (a *Processor) Process(ctx context.Context, act IActivity, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO) (retTask *bo.CommentaryMainTaskBO, err error) {
	// 在Process依次调用WaitCondition，HandleSubmit，HandleResult方法
	defer func() {
		retTask = task
		if r := recover(); r != nil {
			g.Log().Errorf(ctx, "activity process panic, err:%v, stack:%s", r, string(debug.Stack()))
			err = temporal.NewNonRetryableApplicationError(
				"activity process panic",
				"panic",
				errors.New(string(debug.Stack())),
			)
		}
		if err != nil {
			g.Log().Errorf(ctx, "activity process failed, err:%v", err)
			var appErr *temporal.ApplicationError
			if errors.As(err, &appErr) {
				if appErr.NonRetryable() {
					// 如果是不可重试错误，处理失败逻辑
					g.Log().Errorf(ctx, "TextTranslateProcess non-retryable error: %v", appErr)
					act.HandleFailed(ctx, task, subTask, appErr)

				}
			}
		}
	}()
	if act.IsSkip(ctx, task, subTask) {
		// 直接跳到下个节点
		return
	}
	err = act.WaitCondition(ctx, task, subTask)
	if err != nil {
		return
	}

	var engineTaskIDs []int64
	if activity.HasHeartbeatDetails(ctx) { // 显式检查
		activity.GetHeartbeatDetails(ctx, &engineTaskIDs)
	}
	if len(engineTaskIDs) == 0 {
		engineTaskIDs, err = act.HandleSubmit(ctx, task, subTask)
		if err != nil {
			return
		}
		activity.RecordHeartbeat(ctx, engineTaskIDs)
	}
	if len(engineTaskIDs) > 0 {
		var results []*do.BusinessEngineTask
		results, err = a.HandleQuery(ctx, engineTaskIDs)
		if err != nil {
			return
		}

		task, err = act.HandleResult(ctx, task, subTask, results)
		if err != nil {
			return
		}
	}

	return
}
