package commentary_repo

import (
	"business-workflow/internal/entity/do"
	"context"

	"gorm.io/gorm"
)

type CommentarySubtitleItemSnapshotRepoImpl interface {
	// CreateSubtitleSnapshotItems 创建字幕快照项
	CreateSubtitleSnapshotItems(ctx context.Context, items []*do.CommentarySubtitleSnapshotItem, tx ...*gorm.DB) error
	// GetSubtitleSnapshotItemsBySubTaskId 根据子任务ID获取字幕快照项列表
	GetSubtitleSnapshotItemsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentarySubtitleSnapshotItem, error)
}
