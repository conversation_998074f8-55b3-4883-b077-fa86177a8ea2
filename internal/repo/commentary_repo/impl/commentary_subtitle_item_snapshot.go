package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"

	"gorm.io/gorm"
)

type CommentarySubtitleItemSnapShotRepoImpl struct {
	db *gorm.DB
}

func NewCommentarySubtitleItemSnapShotRepoImpl() commentary_repo.CommentarySubtitleItemSnapshotRepoImpl {
	return &CommentarySubtitleItemSnapShotRepoImpl{db: db.GetDB()}
}

// CreateSubtitleSnapshotItems 创建字幕快照项
func (r *CommentarySubtitleItemSnapShotRepoImpl) CreateSubtitleSnapshotItems(ctx context.Context, items []*do.CommentarySubtitleSnapshotItem, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.Debug().WithContext(ctx).Create(&items).Error; err != nil {
		return err
	}
	return nil
}

// GetSubtitleSnapshotItemsBySubTaskId 根据子任务ID获取字幕快照项列表
func (r *CommentarySubtitleItemSnapShotRepoImpl) GetSubtitleSnapshotItemsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentarySubtitleSnapshotItem, error) {
	var items []*do.CommentarySubtitleSnapshotItem
	if err := r.db.WithContext(ctx).Where("sub_task_id = ?", subTaskId).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}
