package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CommentarySubtitleItemRepoImpl struct {
	db *gorm.DB
}

func NewCommentarySubtitleItemRepoImpl() commentary_repo.ICommentarySubtitleItemRepo {
	return &CommentarySubtitleItemRepoImpl{db: db.GetDB()}
}

// CreateSubtitleItem 创建字幕项
func (r *CommentarySubtitleItemRepoImpl) CreateSubtitleItem(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	if item == nil {
		return fmt.Errorf("字幕项不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Create(item).Error; err != nil {
		return fmt.Errorf("创建字幕项失败: %w", err)
	}
	return nil
}

// CreateSubtitleItems 批量创建字幕项
func (r *CommentarySubtitleItemRepoImpl) CreateSubtitleItems(ctx context.Context, items []*do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	if len(items) == 0 {
		return fmt.Errorf("字幕项列表不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).CreateInBatches(items, 100).Error; err != nil {
		return fmt.Errorf("批量创建字幕项失败: %w", err)
	}
	return nil
}

// GetSubtitleItemById 根据ID获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemById(ctx context.Context, id int64) (*do.CommentarySubtitleItem, error) {
	var item do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("获取字幕项失败: %w", err)
	}
	return &item, nil
}

// GetSubtitleItemByIdAndSubTaskId 根据ID和子任务ID获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemByIdAndSubTaskId(ctx context.Context, id int64, subTaskId int64) (*do.CommentarySubtitleItem, error) {
	var item do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("id = ? AND sub_task_id = ?", id, subTaskId).First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("根据ID和子任务ID获取字幕项失败: %w", err)
	}
	return &item, nil
}

// GetSubtitleItemBySubtitleItemId 根据字幕项ID获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemBySubtitleItemId(ctx context.Context, subtitleItemId int64) (*do.CommentarySubtitleItem, error) {
	var item do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("id = ?", subtitleItemId).First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("根据字幕项ID获取字幕项失败: %w", err)
	}
	return &item, nil
}

// GetSubtitleItemsByIdsWithSelectFields 根据ID列表和子任务ID获取字幕项，只查询指定字段
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsByIdsWithSelectFields(ctx context.Context, subTaskId int64, ids []int64, selectFields []string) ([]*do.CommentarySubtitleItem, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	if len(selectFields) == 0 {
		return nil, nil
	}

	var items []*do.CommentarySubtitleItem
	err := r.db.WithContext(ctx).
		Select(strings.Join(selectFields, ",")).
		Where("id IN (?) AND sub_task_id = ?", ids, subTaskId).
		Find(&items).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("根据ID列表和子任务ID获取字幕项失败: %w", err)
	}

	return items, nil
}

// UpdateSubtitleItemsWithCase 批量更新字幕项，使用CASE WHEN语句
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemsWithCase(ctx context.Context, items []*do.CommentarySubtitleItem, fields []string, batchSize int) error {
	return r.UpdateSubtitleItemsWithTxAndCase(ctx, r.db, items, fields, batchSize)
}

// UpdateSubtitleItemsWithTxAndCase 在事务中批量更新字幕项，使用CASE WHEN语句
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemsWithTxAndCase(ctx context.Context, tx *gorm.DB, items []*do.CommentarySubtitleItem, fields []string, batchSize int) error {
	if len(items) == 0 {
		return nil
	}
	if len(fields) == 0 {
		return nil
	}

	// 更新时间戳
	now := time.Now()
	for _, item := range items {
		item.UpdatedAt = now
	}

	// 确保更新时间字段在更新列表中
	fieldsWithTime := append(fields, "updated_at")

	// 设置默认批次大小
	if batchSize <= 0 {
		batchSize = 100
	}

	// 分批处理
	for i := 0; i < len(items); i += batchSize {
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}
		chunk := items[i:end]

		// 使用 ON DUPLICATE KEY UPDATE 语法进行批量更新
		err := tx.WithContext(ctx).Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},            // 主键冲突时
			DoUpdates: clause.AssignmentColumns(fieldsWithTime), // 更新这些字段
		}).Create(&chunk).Error

		if err != nil {
			return fmt.Errorf("批量更新字幕项失败: %w", err)
		}
	}

	return nil
}

// GetSubtitleItemsBySubTaskId 根据子任务ID获取字幕项列表
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentarySubtitleItem, error) {
	var items []*do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("sub_task_id = ?", subTaskId).Order("item_idx ASC").Find(&items).Error; err != nil {
		return nil, fmt.Errorf("根据子任务ID获取字幕项列表失败: %w", err)
	}
	return items, nil
}

// GetSubtitleItemsByMainTaskId 根据主任务ID获取字幕项列表
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySubtitleItem, error) {
	var items []*do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("main_task_id = ?", mainTaskId).Order("sub_task_id ASC, item_idx ASC").Find(&items).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID获取字幕项列表失败: %w", err)
	}
	return items, nil
}

// GetSubtitleItemsByIds 根据ID列表获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsByIds(ctx context.Context, ids []int64) ([]*do.CommentarySubtitleItem, error) {
	if len(ids) == 0 {
		return []*do.CommentarySubtitleItem{}, nil
	}

	var items []*do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&items).Error; err != nil {
		return nil, fmt.Errorf("根据ID列表获取字幕项失败: %w", err)
	}
	return items, nil
}

// GetSubtitleItemsBySubtitleItemIds 根据字幕项ID列表获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsBySubtitleItemIds(ctx context.Context, subtitleItemIds []int64) ([]*do.CommentarySubtitleItem, error) {
	if len(subtitleItemIds) == 0 {
		return []*do.CommentarySubtitleItem{}, nil
	}

	var items []*do.CommentarySubtitleItem
	if err := r.db.WithContext(ctx).Where("id IN ?", subtitleItemIds).Find(&items).Error; err != nil {
		return nil, fmt.Errorf("根据字幕项ID列表获取字幕项失败: %w", err)
	}
	return items, nil
}

// UpdateSubtitleItem 更新字幕项
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItem(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	if item == nil {
		return fmt.Errorf("字幕项不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Save(item).Error; err != nil {
		return fmt.Errorf("更新字幕项失败: %w", err)
	}
	return nil
}

// UpdateCommentarySubtitleItemTx 指定字段更新字幕项（事务版本）
func (r *CommentarySubtitleItemRepoImpl) UpdateCommentarySubtitleItemTx(ctx context.Context, tx *gorm.DB, item *do.CommentarySubtitleItem, fields []string) error {
	if item == nil {
		return fmt.Errorf("字幕项不能为空")
	}
	if len(fields) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	// 添加更新时间字段
	fields = append(fields, "update_time")

	// 如果没有传入事务，使用默认数据库连接
	db := r.db
	if tx != nil {
		db = tx
	}

	if err := db.WithContext(ctx).Model(item).Select(fields).Omit("id").Updates(item).Error; err != nil {
		return fmt.Errorf("更新字幕项失败: %w", err)
	}
	return nil
}

// UpdateSubtitleItems 批量更新字幕项
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItems(ctx context.Context, items []*do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	if len(items) == 0 {
		return fmt.Errorf("字幕项列表不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	for _, item := range items {
		if err := db.WithContext(ctx).Save(item).Error; err != nil {
			return fmt.Errorf("批量更新字幕项失败: %w", err)
		}
	}
	return nil
}

// UpdateGenerateVoiceStatus 更新字幕项的 generate_voice_status
func (r *CommentarySubtitleItemRepoImpl) UpdateGenerateVoiceStatus(ctx context.Context, id int64, generateVoiceStatus int, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	updates := map[string]interface{}{
		"generate_voice_status": generateVoiceStatus,
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新generate_voice_status失败: %w", err)
	}
	return nil
}

// UpdateTextTranslateStatus 更新字幕项的 text_translate_status
func (r *CommentarySubtitleItemRepoImpl) UpdateTextTranslateStatus(ctx context.Context, id int64, textTranslateStatus int, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	updates := map[string]interface{}{
		"text_translate_status": textTranslateStatus,
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新text_translate_status失败: %w", err)
	}
	return nil
}

// UpdateSubtitleItemTtsInfo 更新字幕项TTS信息
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemTtsInfo(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	fields := []string{
		"tts_url",
		"tts_words",
		"generate_voice_status",
		"subtitle_end_str",
		"audio_config",
		"latest_tts_info",
		"clip_subtitle_start_str",
		"clip_subtitle_end_str",
	}
	if err := db.WithContext(ctx).Model(item).Select(fields).Omit("id").Updates(item).Error; err != nil {
		return fmt.Errorf("更新字幕项TTS信息失败: %w", err)
	}
	return nil
}

// UpdateSubtitleItemTranslateInfo 更新字幕项翻译信息
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemTranslateInfo(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	fields := []string{
		"text_translate_status",
		"target_subtitle",
		"latest_text_translate_info",
		"back_translate_text",
	}
	if err := db.WithContext(ctx).Model(item).Select(fields).Omit("id").Updates(item).Error; err != nil {
		return fmt.Errorf("更新text_translate_status失败: %w", err)
	}
	return nil
}

// DeleteSubtitleItem 删除字幕项
func (r *CommentarySubtitleItemRepoImpl) DeleteSubtitleItem(ctx context.Context, id int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Delete(&do.CommentarySubtitleItem{}, id).Error; err != nil {
		return fmt.Errorf("删除字幕项失败: %w", err)
	}
	return nil
}

// DeleteSubtitleItemBySubtitleItemId 根据字幕项ID删除字幕项
func (r *CommentarySubtitleItemRepoImpl) DeleteSubtitleItemBySubtitleItemId(ctx context.Context, subtitleItemId int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Where("id = ?", subtitleItemId).Delete(&do.CommentarySubtitleItem{}).Error; err != nil {
		return fmt.Errorf("根据字幕项ID删除字幕项失败: %w", err)
	}
	return nil
}

// DeleteSubtitleItemsBySubTaskId 根据子任务ID删除字幕项
func (r *CommentarySubtitleItemRepoImpl) DeleteSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Where("sub_task_id = ?", subTaskId).Delete(&do.CommentarySubtitleItem{}).Error; err != nil {
		return fmt.Errorf("根据子任务ID删除字幕项失败: %w", err)
	}
	return nil
}

// DeleteSubtitleItemsByMainTaskId 根据主任务ID删除字幕项
func (r *CommentarySubtitleItemRepoImpl) DeleteSubtitleItemsByMainTaskId(ctx context.Context, mainTaskId int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Where("main_task_id = ?", mainTaskId).Delete(&do.CommentarySubtitleItem{}).Error; err != nil {
		return fmt.Errorf("根据主任务ID删除字幕项失败: %w", err)
	}
	return nil
}

// GetSubtitleItemsCount 获取字幕项总数
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsCount(ctx context.Context, subTaskId int64) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("sub_task_id = ?", subTaskId).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("获取字幕项总数失败: %w", err)
	}
	return count, nil
}

// GetSubtitleItemsWithPagination 分页获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsWithPagination(ctx context.Context, subTaskId int64, page, pageSize int) ([]*do.CommentarySubtitleItem, int64, error) {
	var items []*do.CommentarySubtitleItem
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("sub_task_id = ?", subTaskId).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取字幕项总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := r.db.WithContext(ctx).Where("sub_task_id = ?", subTaskId).Order("item_idx ASC").Offset(offset).Limit(pageSize).Find(&items).Error; err != nil {
		return nil, 0, fmt.Errorf("分页获取字幕项失败: %w", err)
	}

	return items, total, nil
}

// UpdateSubtitleItemsBySubTaskId 根据子任务ID批量更新字幕项
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64, updates map[string]interface{}, tx ...*gorm.DB) error {
	if len(updates) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("sub_task_id = ?", subTaskId).Updates(updates).Error; err != nil {
		return fmt.Errorf("根据子任务ID批量更新字幕项失败: %w", err)
	}
	return nil
}

// GetSubtitleItemsByStatusAndSubTaskId 根据状态和子任务ID获取字幕项
func (r *CommentarySubtitleItemRepoImpl) GetSubtitleItemsByStatusAndSubTaskId(ctx context.Context, subTaskId int64, generateVoiceStatus, textTranslateStatus int) ([]*do.CommentarySubtitleItem, error) {
	var items []*do.CommentarySubtitleItem
	query := r.db.WithContext(ctx).Where("sub_task_id = ?", subTaskId)

	if generateVoiceStatus >= 0 {
		query = query.Where("generate_voice_status = ?", generateVoiceStatus)
	}
	if textTranslateStatus >= 0 {
		query = query.Where("text_translate_status = ?", textTranslateStatus)
	}

	if err := query.Order("item_idx ASC").Find(&items).Error; err != nil {
		return nil, fmt.Errorf("根据状态和子任务ID获取字幕项失败: %w", err)
	}
	return items, nil
}

func (r *CommentarySubtitleItemRepoImpl) UpdateTranslateSubtitleItemIdxRecurrence(ctx context.Context, subtitleId int64, offsetIdx, offsetNum int32, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}
	return db.WithContext(ctx).Model(&do.CommentarySubtitleItem{}).Where("subtitle_id = ? AND item_idx >= ?", subtitleId, offsetIdx).
		Update("item_idx", gorm.Expr("item_idx + ?", offsetNum)).Error
}

// UpdateSubtitleItemFieldsBatch 批量更新字幕项字段
func (r *CommentarySubtitleItemRepoImpl) UpdateSubtitleItemFieldsBatch(ctx context.Context, updateData []struct {
	ID     int64
	Fields map[string]interface{}
}, tx ...*gorm.DB) error {
	if len(updateData) == 0 {
		return fmt.Errorf("更新数据不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	// 使用事务批量更新
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, data := range updateData {
			if err := tx.Model(&do.CommentarySubtitleItem{}).Where("id = ?", data.ID).Updates(data.Fields).Error; err != nil {
				return fmt.Errorf("批量更新字幕项字段失败: %w", err)
			}
		}
		return nil
	})
}
