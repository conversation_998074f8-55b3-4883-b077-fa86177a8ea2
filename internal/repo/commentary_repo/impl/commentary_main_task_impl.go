package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type CommentaryMainTaskRepoImpl struct {
	db *gorm.DB
}

func NewCommentaryMainTaskRepoImpl() commentary_repo.ICommentaryMainTaskRepo {
	return &CommentaryMainTaskRepoImpl{db: db.GetDB()}
}

// CreateTask 创建解说主任务
func (r *CommentaryMainTaskRepoImpl) CreateTask(ctx context.Context, task *do.CommentaryMainTask, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.Debug().WithContext(ctx).Create(task).Error; err != nil {
		return fmt.Errorf("创建解说主任务失败: %w", err)
	}
	return nil
}

// GetTaskById 根据ID获取任务
func (r *CommentaryMainTaskRepoImpl) GetTaskById(ctx context.Context, id int64) (*do.CommentaryMainTask, error) {
	var task do.CommentaryMainTask
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&task).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("获取解说主任务失败: %w", err)
	}
	return &task, nil
}

// GetTasksByTenantId 根据租户ID获取任务列表
func (r *CommentaryMainTaskRepoImpl) GetTasksByTenantId(ctx context.Context, tenantId int64, page, pageSize int) ([]*do.CommentaryMainTask, error) {
	var tasks []*do.CommentaryMainTask
	var total int64

	query := r.db.WithContext(ctx).Model(&do.CommentaryMainTask{}).Where("tenant_id = ?", tenantId)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("获取任务总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	return tasks, nil
}

func (r *CommentaryMainTaskRepoImpl) GetTasksByCond(ctx context.Context, filterFields map[string][]interface{}, page, pageSize int) ([]*do.CommentaryMainTask, error) {
	var tasks []*do.CommentaryMainTask

	query := r.db.WithContext(ctx).Model(&do.CommentaryMainTask{})

	/// 支持多选 in 查询
	for field, values := range filterFields {
		if len(values) > 0 {
			query = query.Where(fmt.Sprintf("%s IN ?", field), values)
		}
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("根据条件获取任务列表失败: %w", err)
	}

	return tasks, nil
}

// GetTasksByIds 根据ID列表获取任务
func (r *CommentaryMainTaskRepoImpl) GetTasksByIds(ctx context.Context, ids []int64) ([]*do.CommentaryMainTask, error) {
	if len(ids) == 0 {
		return []*do.CommentaryMainTask{}, nil
	}

	var tasks []*do.CommentaryMainTask
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("根据ID列表获取任务失败: %w", err)
	}

	return tasks, nil
}

// GetTasksByOrderIds 根据订单ID列表获取任务
func (r *CommentaryMainTaskRepoImpl) GetTasksByOrderIds(ctx context.Context, orderIds []string) ([]*do.CommentaryMainTask, error) {
	if len(orderIds) == 0 {
		return []*do.CommentaryMainTask{}, nil
	}

	var tasks []*do.CommentaryMainTask
	if err := r.db.WithContext(ctx).Where("pay_order_id IN ?", orderIds).Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("根据订单ID列表获取任务失败: %w", err)
	}

	return tasks, nil
}

// UpdateTask 更新任务
func (r *CommentaryMainTaskRepoImpl) UpdateTask(ctx context.Context, task *do.CommentaryMainTask) error {
	if err := r.db.WithContext(ctx).Save(task).Error; err != nil {
		return fmt.Errorf("更新解说主任务失败: %w", err)
	}
	return nil
}

// UpdateTaskStatus 更新任务状态
func (r *CommentaryMainTaskRepoImpl) UpdateTaskStatus(ctx context.Context, id int64, status consts.CommentaryMainTaskStatus, errMsg string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	if errMsg != "" {
		updates["err_msg"] = errMsg
	}

	if err := r.db.WithContext(ctx).Model(&do.CommentaryMainTask{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}
	return nil
}

// DeleteTask 删除任务
func (r *CommentaryMainTaskRepoImpl) DeleteTask(ctx context.Context, id int64) error {
	if err := r.db.WithContext(ctx).Delete(&do.CommentaryMainTask{}, id).Error; err != nil {
		return fmt.Errorf("删除解说主任务失败: %w", err)
	}
	return nil
}
func (r *CommentaryMainTaskRepoImpl) UpdateTaskFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error {
	if len(fields) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Model(&do.CommentaryMainTask{}).Where("id = ?", id).Updates(fields).Error; err != nil {
		return fmt.Errorf("更新主任务失败: %w", err)
	}
	return nil
}
