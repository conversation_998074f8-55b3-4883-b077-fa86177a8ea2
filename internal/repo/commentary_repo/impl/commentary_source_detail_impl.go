package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/commentary_repo"
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type CommentarySourceDetailRepoImpl struct {
	db *gorm.DB
}

func NewCommentarySourceDetailRepoImpl() commentary_repo.ICommentarySourceDetailRepo {
	return &CommentarySourceDetailRepoImpl{db: db.GetDB()}
}

// CreateSourceDetail 创建资源详情
func (r *CommentarySourceDetailRepoImpl) CreateSourceDetail(ctx context.Context, detail *do.CommentarySourceDetail, tx ...*gorm.DB) error {
	if detail == nil {
		return fmt.Errorf("资源详情不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Create(detail).Error; err != nil {
		return fmt.Errorf("创建资源详情失败: %w", err)
	}
	return nil
}

// CreateSourceDetails 批量创建资源详情
func (r *CommentarySourceDetailRepoImpl) CreateSourceDetails(ctx context.Context, details []*do.CommentarySourceDetail, tx ...*gorm.DB) error {
	if len(details) == 0 {
		return fmt.Errorf("资源详情列表不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).CreateInBatches(details, 100).Error; err != nil {
		return fmt.Errorf("批量创建资源详情失败: %w", err)
	}
	return nil
}

// GetSourceDetailById 根据ID获取资源详情
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailById(ctx context.Context, id int64) (*do.CommentarySourceDetail, error) {
	var detail do.CommentarySourceDetail
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&detail).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("获取资源详情失败: %w", err)
	}
	return &detail, nil
}

// GetSourceDetailsByMainTaskId 根据主任务ID获取资源详情列表
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySourceDetail, error) {
	var details []*do.CommentarySourceDetail
	query := r.db.WithContext(ctx).Where("main_task_id = ?", mainTaskId)
	if err := query.Order("created_at ASC").Find(&details).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID获取资源详情列表失败: %w", err)
	}
	return details, nil
}

// GetSourceDetailsByIds 根据ID列表获取资源详情
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailsByIds(ctx context.Context, ids []int64) ([]*do.CommentarySourceDetail, error) {
	if len(ids) == 0 {
		return []*do.CommentarySourceDetail{}, nil
	}

	var details []*do.CommentarySourceDetail
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&details).Error; err != nil {
		return nil, fmt.Errorf("根据ID列表获取资源详情失败: %w", err)
	}

	return details, nil
}

// UpdateSourceDetail 更新资源详情
func (r *CommentarySourceDetailRepoImpl) UpdateSourceDetail(ctx context.Context, detail *do.CommentarySourceDetail, tx ...*gorm.DB) error {
	if detail == nil {
		return fmt.Errorf("资源详情不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Save(detail).Error; err != nil {
		return fmt.Errorf("更新资源详情失败: %w", err)
	}
	return nil
}

// UpdateSourceDetails 批量更新资源详情
func (r *CommentarySourceDetailRepoImpl) UpdateSourceDetails(ctx context.Context, details []*do.CommentarySourceDetail, tx ...*gorm.DB) error {
	if len(details) == 0 {
		return fmt.Errorf("资源详情列表不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	for _, detail := range details {
		if err := db.WithContext(ctx).Save(detail).Error; err != nil {
			return fmt.Errorf("批量更新资源详情失败: %w", err)
		}
	}
	return nil
}

// UpdateSourceDetailStatus 更新资源详情审核状态
func (r *CommentarySourceDetailRepoImpl) UpdateSourceDetailStatus(ctx context.Context, id int64, auditStatus int, errCode int, errMsg string, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	updates := map[string]interface{}{
		"audit_status": auditStatus,
		"err_code":     errCode,
		"err_msg":      errMsg,
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySourceDetail{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新资源详情审核状态失败: %w", err)
	}
	return nil
}

// UpdateSourceDetailFields 通用更新资源详情字段方法
func (r *CommentarySourceDetailRepoImpl) UpdateSourceDetailFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error {
	if len(fields) == 0 {
		return fmt.Errorf("更新字段不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Model(&do.CommentarySourceDetail{}).Where("id = ?", id).Updates(fields).Error; err != nil {
		return fmt.Errorf("更新资源详情字段失败: %w", err)
	}
	return nil
}

// BatchUpdateSourceDetailFields 批量更新资源详情字段
func (r *CommentarySourceDetailRepoImpl) BatchUpdateSourceDetailFields(ctx context.Context, updateData []struct {
	ID     int64
	Fields map[string]interface{}
}, tx ...*gorm.DB) error {
	if len(updateData) == 0 {
		return fmt.Errorf("更新数据不能为空")
	}

	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	// 使用事务批量更新
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, data := range updateData {
			if err := tx.Model(&do.CommentarySourceDetail{}).Where("id = ?", data.ID).Updates(data.Fields).Error; err != nil {
				return fmt.Errorf("批量更新资源详情字段失败: %w", err)
			}
		}
		return nil
	})
}

// DeleteSourceDetail 删除资源详情
func (r *CommentarySourceDetailRepoImpl) DeleteSourceDetail(ctx context.Context, id int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Delete(&do.CommentarySourceDetail{}, id).Error; err != nil {
		return fmt.Errorf("删除资源详情失败: %w", err)
	}
	return nil
}

// DeleteSourceDetailsByMainTaskId 根据主任务ID删除资源详情
func (r *CommentarySourceDetailRepoImpl) DeleteSourceDetailsByMainTaskId(ctx context.Context, mainTaskId int64, tx ...*gorm.DB) error {
	db := r.db
	if len(tx) > 0 && tx[0] != nil {
		db = tx[0]
	}

	if err := db.WithContext(ctx).Where("main_task_id = ?", mainTaskId).Delete(&do.CommentarySourceDetail{}).Error; err != nil {
		return fmt.Errorf("根据主任务ID删除资源详情失败: %w", err)
	}
	return nil
}

// GetSourceDetailsByMainTaskIdAndAuditStatus 根据主任务ID和审核状态获取资源详情列表
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailsByMainTaskIdAndAuditStatus(ctx context.Context, mainTaskId int64, auditStatus int) ([]*do.CommentarySourceDetail, error) {
	var details []*do.CommentarySourceDetail
	if err := r.db.WithContext(ctx).Where("main_task_id = ? AND audit_status = ?", mainTaskId, auditStatus).Order("created_at ASC").Find(&details).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID和审核状态获取资源详情列表失败: %w", err)
	}
	return details, nil
}
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailsByMainTaskIdWithFileType(ctx context.Context, mainTaskId int64, sourceFileType []consts.SourceFileType) ([]*do.CommentarySourceDetail, error) {
	var details []*do.CommentarySourceDetail
	if err := r.db.WithContext(ctx).Where("main_task_id = ? AND source_file_type IN ?", mainTaskId, sourceFileType).Order("created_at ASC").Find(&details).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID和资源类型获取资源详情列表失败: %w", err)
	}
	return details, nil
}
func (r *CommentarySourceDetailRepoImpl) GetSourceDetailsByMainTaskIdByBizType(ctx context.Context, mainTaskId int64, bizType consts.SourceBizType) ([]*do.CommentarySourceDetail, error) {
	var details []*do.CommentarySourceDetail
	if err := r.db.WithContext(ctx).Where("main_task_id = ? AND biz_type = ?", mainTaskId, bizType).Order("created_at ASC").Find(&details).Error; err != nil {
		return nil, fmt.Errorf("根据主任务ID和业务类型获取资源详情列表失败: %w", err)
	}
	return details, nil
}
