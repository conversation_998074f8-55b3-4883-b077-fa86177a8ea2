package commentary_repo

import (
	"business-workflow/internal/entity/do"
	"context"

	"gorm.io/gorm"
)

// ICommentarySubtitleItemRepo 解说字幕项仓储接口
type ICommentarySubtitleItemRepo interface {
	// CreateSubtitleItem 创建字幕项
	CreateSubtitleItem(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error

	// CreateSubtitleItems 批量创建字幕项
	CreateSubtitleItems(ctx context.Context, items []*do.CommentarySubtitleItem, tx ...*gorm.DB) error

	// GetSubtitleItemById 根据ID获取字幕项
	GetSubtitleItemById(ctx context.Context, id int64) (*do.CommentarySubtitleItem, error)

	// GetSubtitleItemByIdAndSubTaskId 根据ID和子任务ID获取字幕项
	GetSubtitleItemByIdAndSubTaskId(ctx context.Context, id int64, subTaskId int64) (*do.CommentarySubtitleItem, error)

	// GetSubtitleItemBySubtitleItemId 根据字幕项ID获取字幕项
	GetSubtitleItemBySubtitleItemId(ctx context.Context, subtitleItemId int64) (*do.CommentarySubtitleItem, error)

	// GetSubtitleItemsByIdsWithSelectFields 根据ID列表和子任务ID获取字幕项，只查询指定字段
	GetSubtitleItemsByIdsWithSelectFields(ctx context.Context, subTaskId int64, ids []int64, selectFields []string) ([]*do.CommentarySubtitleItem, error)

	// UpdateSubtitleItemsWithCase 批量更新字幕项，使用CASE WHEN语句
	UpdateSubtitleItemsWithCase(ctx context.Context, items []*do.CommentarySubtitleItem, fields []string, batchSize int) error

	// UpdateSubtitleItemsWithTxAndCase 在事务中批量更新字幕项，使用CASE WHEN语句
	UpdateSubtitleItemsWithTxAndCase(ctx context.Context, tx *gorm.DB, items []*do.CommentarySubtitleItem, fields []string, batchSize int) error

	// GetSubtitleItemsBySubTaskId 根据子任务ID获取字幕项列表
	GetSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64) ([]*do.CommentarySubtitleItem, error)

	// GetSubtitleItemsByMainTaskId 根据主任务ID获取字幕项列表
	GetSubtitleItemsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySubtitleItem, error)

	// GetSubtitleItemsByIds 根据ID列表获取字幕项
	GetSubtitleItemsByIds(ctx context.Context, ids []int64) ([]*do.CommentarySubtitleItem, error)

	// GetSubtitleItemsBySubtitleItemIds 根据字幕项ID列表获取字幕项
	GetSubtitleItemsBySubtitleItemIds(ctx context.Context, subtitleItemIds []int64) ([]*do.CommentarySubtitleItem, error)

	// UpdateSubtitleItem 更新字幕项
	UpdateSubtitleItem(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error

	// UpdateCommentarySubtitleItemTx 指定字段更新字幕项（事务版本）
	UpdateCommentarySubtitleItemTx(ctx context.Context, tx *gorm.DB, item *do.CommentarySubtitleItem, fields []string) error

	// UpdateSubtitleItems 批量更新字幕项
	UpdateSubtitleItems(ctx context.Context, items []*do.CommentarySubtitleItem, tx ...*gorm.DB) error

	// UpdateGenerateVoiceStatus 更新字幕项生成语音状态
	UpdateGenerateVoiceStatus(ctx context.Context, id int64, generateVoiceStatus int, tx ...*gorm.DB) error
	// UpdateTextTranslateStatus 更新字幕项翻译状态
	UpdateTextTranslateStatus(ctx context.Context, id int64, textTranslateStatus int, tx ...*gorm.DB) error

	// UpdateSubtitleItemTtsInfo 更新字幕项TTS信息
	UpdateSubtitleItemTtsInfo(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error
	// UpdateSubtitleItemTranslateInfo 更新字幕项翻译信息
	UpdateSubtitleItemTranslateInfo(ctx context.Context, item *do.CommentarySubtitleItem, tx ...*gorm.DB) error

	// DeleteSubtitleItem 删除字幕项
	DeleteSubtitleItem(ctx context.Context, id int64, tx ...*gorm.DB) error

	// DeleteSubtitleItemBySubtitleItemId 根据字幕项ID删除字幕项
	DeleteSubtitleItemBySubtitleItemId(ctx context.Context, subtitleItemId int64, tx ...*gorm.DB) error

	// DeleteSubtitleItemsBySubTaskId 根据子任务ID删除字幕项
	DeleteSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64, tx ...*gorm.DB) error

	// DeleteSubtitleItemsByMainTaskId 根据主任务ID删除字幕项
	DeleteSubtitleItemsByMainTaskId(ctx context.Context, mainTaskId int64, tx ...*gorm.DB) error

	// GetSubtitleItemsCount 获取字幕项总数
	GetSubtitleItemsCount(ctx context.Context, subTaskId int64) (int64, error)

	// GetSubtitleItemsWithPagination 分页获取字幕项
	GetSubtitleItemsWithPagination(ctx context.Context, subTaskId int64, page, pageSize int) ([]*do.CommentarySubtitleItem, int64, error)

	// UpdateSubtitleItemsBySubTaskId 根据子任务ID批量更新字幕项
	UpdateSubtitleItemsBySubTaskId(ctx context.Context, subTaskId int64, updates map[string]interface{}, tx ...*gorm.DB) error

	// GetSubtitleItemsByStatusAndSubTaskId 根据状态和子任务ID获取字幕项
	GetSubtitleItemsByStatusAndSubTaskId(ctx context.Context, subTaskId int64, generateVoiceStatus, textTranslateStatus int) ([]*do.CommentarySubtitleItem, error)

	// UpdateTranslateSubtitleItemIdxRecurrence 更新字幕项的翻译索引和重试次数
	UpdateTranslateSubtitleItemIdxRecurrence(ctx context.Context, subtitleId int64, offsetIdx, offsetNum int32, tx ...*gorm.DB) error

	// UpdateSubtitleItemFieldsBatch 批量更新字幕项字段
	UpdateSubtitleItemFieldsBatch(ctx context.Context, updateData []struct {
		ID     int64
		Fields map[string]interface{}
	}, tx ...*gorm.DB) error
}
