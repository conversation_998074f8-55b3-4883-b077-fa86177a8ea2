package commentary_repo

import (
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	"context"
	"gorm.io/gorm"
)

// ICommentarySourceDetailRepo 解说任务资源详情仓储接口
type ICommentarySourceDetailRepo interface {
	// CreateSourceDetail 创建资源详情
	CreateSourceDetail(ctx context.Context, detail *do.CommentarySourceDetail, tx ...*gorm.DB) error

	// CreateSourceDetails 批量创建资源详情
	CreateSourceDetails(ctx context.Context, details []*do.CommentarySourceDetail, tx ...*gorm.DB) error

	// GetSourceDetailById 根据ID获取资源详情
	GetSourceDetailById(ctx context.Context, id int64) (*do.CommentarySourceDetail, error)

	// GetSourceDetailsByMainTaskId 根据主任务ID获取资源详情列表
	GetSourceDetailsByMainTaskId(ctx context.Context, mainTaskId int64) ([]*do.CommentarySourceDetail, error)

	// GetSourceDetailsByMainTaskIdWithFileType 根据主任务ID获取资源详情列表 根据文件类型
	GetSourceDetailsByMainTaskIdWithFileType(ctx context.Context, mainTaskId int64, sourceFileType []consts.SourceFileType) ([]*do.CommentarySourceDetail, error)

	// GetSourceDetailsByIds 根据ID列表获取资源详情
	GetSourceDetailsByIds(ctx context.Context, ids []int64) ([]*do.CommentarySourceDetail, error)

	// UpdateSourceDetail 更新资源详情
	UpdateSourceDetail(ctx context.Context, detail *do.CommentarySourceDetail, tx ...*gorm.DB) error

	// UpdateSourceDetails 批量更新资源详情
	UpdateSourceDetails(ctx context.Context, details []*do.CommentarySourceDetail, tx ...*gorm.DB) error

	// UpdateSourceDetailStatus 更新资源详情审核状态
	UpdateSourceDetailStatus(ctx context.Context, id int64, auditStatus int, errCode int, errMsg string, tx ...*gorm.DB) error

	// DeleteSourceDetail 删除资源详情
	DeleteSourceDetail(ctx context.Context, id int64, tx ...*gorm.DB) error

	// DeleteSourceDetailsByMainTaskId 根据主任务ID删除资源详情
	DeleteSourceDetailsByMainTaskId(ctx context.Context, mainTaskId int64, tx ...*gorm.DB) error

	// GetSourceDetailsByMainTaskIdAndAuditStatus 根据主任务ID和审核状态获取资源详情列表
	GetSourceDetailsByMainTaskIdAndAuditStatus(ctx context.Context, mainTaskId int64, auditStatus int) ([]*do.CommentarySourceDetail, error)

	// UpdateSourceDetailFields 通用更新资源详情字段方法
	UpdateSourceDetailFields(ctx context.Context, id int64, fields map[string]interface{}, tx ...*gorm.DB) error

	// BatchUpdateSourceDetailFields 批量更新资源详情字段
	BatchUpdateSourceDetailFields(ctx context.Context, updateData []struct {
		ID     int64
		Fields map[string]interface{}
	}, tx ...*gorm.DB) error
}
