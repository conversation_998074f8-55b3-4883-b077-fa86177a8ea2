package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/consts"
	"business-workflow/internal/entity/do"
	enginetaskrepo "business-workflow/internal/repo/engine_task_repo"
	"context"

	"gorm.io/gorm"
)

type EngineTaskImpl struct {
	db *gorm.DB
}

func NewEngineTaskRepoImpl() enginetaskrepo.IEngineTaskRepo {
	return &EngineTaskImpl{db: db.GetDB()}
}

// 批量查询engine未完成的任务
func (impl *EngineTaskImpl) GetAllUnfinishedOmniTaskIds(ctx context.Context) ([]int64, error) {
	var startID int64
	pageSize := 300
	unfinishedTasks := make([]int64, 0)
	for {
		var tasks []*do.BusinessEngineTask

		result := impl.db.WithContext(ctx).Select("engine_task_id").Where("engine_status IN (?) AND id >?", []int{
			consts.EngineWorkflowStatusInit,
			consts.EngineWorkflowStatusPending,
			consts.EngineWorkflowStatusProcessing,
		}, startID).
			Order("id ASC").
			Limit(pageSize).
			Find(&tasks)
		if result.Error != nil {
			return nil, result.Error
		}
		for _, task := range tasks {
			unfinishedTasks = append(unfinishedTasks, task.EngineTaskId)
		}

		// 判断是否还有下一页
		if len(tasks) < pageSize {
			break
		}
		// 更新 startID 为下一页的起始 ID
		startID = tasks[len(tasks)-1].Id
	}
	return unfinishedTasks, nil
}

func (impl *EngineTaskImpl) UpdateTaskStatusAndInfo(ctx context.Context, taskId int64, status int, taskInfo string) error {
	result := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Where("engine_task_id = ? AND engine_status != ?", taskId, status).
		Updates(map[string]interface{}{
			"engine_status": status,
			"task_info":     taskInfo,
		})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (impl *EngineTaskImpl) GetWorkflowEngineTasks(ctx context.Context, taskIds []int64) ([]*do.BusinessEngineTask, error) {
	tasks := []*do.BusinessEngineTask{}
	err := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Where("engine_task_id in (?)", taskIds).
		Find(&tasks).Error
	return tasks, err
}

// 插入一条新的任务信息
func (impl *EngineTaskImpl) CreateWorkflowEngineTask(ctx context.Context, task *do.BusinessEngineTask) error {
	result := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Create(task)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (impl *EngineTaskImpl) GetWorkflowEngineTaskByBid(ctx context.Context, bid int64) (*do.BusinessEngineTask, error) {
	task := &do.BusinessEngineTask{}
	err := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Where("bid = ?", bid).
		First(task).Error
	return task, err
}

func (impl *EngineTaskImpl) GetWorkflowEngineTaskByBidAndType(ctx context.Context, bid int64, taskType int) (*do.BusinessEngineTask, error) {
	task := &do.BusinessEngineTask{}
	err := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Where("bid = ? AND task_type = ?", bid, taskType).
		First(task).Error
	return task, err
}

func (impl *EngineTaskImpl) GetWorkflowEngineTasksByBidAndType(ctx context.Context, bids []int64, taskType int) ([]*do.BusinessEngineTask, error) {
	tasks := []*do.BusinessEngineTask{}
	err := impl.db.WithContext(ctx).Model(&do.BusinessEngineTask{}).
		Where("bid in (?) AND task_type = ?", bids, taskType).
		Find(&tasks).Error
	return tasks, err
}
