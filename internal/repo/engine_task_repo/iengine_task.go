package enginetaskrepo

import (
	"business-workflow/internal/entity/do"
	"context"
)

type IEngineTaskRepo interface {
	GetAllUnfinishedOmniTaskIds(ctx context.Context) ([]int64, error)
	UpdateTaskStatusAndInfo(ctx context.Context, taskId int64, status int, taskInfo string) error
	GetWorkflowEngineTasks(ctx context.Context, taskIds []int64) ([]*do.BusinessEngineTask, error)
	CreateWorkflowEngineTask(ctx context.Context, task *do.BusinessEngineTask) error

	GetWorkflowEngineTaskByBid(ctx context.Context, bid int64) (*do.BusinessEngineTask, error)
	GetWorkflowEngineTaskByBidAndType(ctx context.Context, bid int64, taskType int) (*do.BusinessEngineTask, error)
	GetWorkflowEngineTasksByBidAndType(ctx context.Context, bids []int64, taskType int) ([]*do.BusinessEngineTask, error)
}
