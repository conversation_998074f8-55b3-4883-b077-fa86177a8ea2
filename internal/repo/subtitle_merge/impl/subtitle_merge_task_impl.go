package impl

import (
	"business-workflow/internal/common/db"
	"business-workflow/internal/entity/do"
	"business-workflow/internal/repo/subtitle_merge"
	"context"

	"gorm.io/gorm"
)

type SubtitleMergeTaskImpl struct {
	db *gorm.DB
}

func NewSubtitleMergeTaskImpl() subtitle_merge.ISubtitleMergeTask {
	return &SubtitleMergeTaskImpl{db: db.GetDB()}
}

func (impl *SubtitleMergeTaskImpl) CreateSubtitleMergeTask(ctx context.Context, task *do.WorkflowMergeTask) error {
	return impl.db.Create(task).Error
}

func (impl *SubtitleMergeTaskImpl) GetLatestMergeTaskBySubId(ctx context.Context, subId int64) (res *do.WorkflowMergeTask, err error) {
	err = impl.db.WithContext(ctx).Model(&do.WorkflowMergeTask{}).Where("sub_id = ?", subId).Last(&res).Error
	return res, err
}

func (impl *SubtitleMergeTaskImpl) GetMergeTaskById(ctx context.Context, taskId int64) (res *do.WorkflowMergeTask, err error) {
	err = impl.db.WithContext(ctx).Model(&do.WorkflowMergeTask{}).Where("id = ?", taskId).Last(&res).Error
	return res, err
}

func (impl *SubtitleMergeTaskImpl) UpdateSubtitleMergeTask(ctx context.Context, task *do.WorkflowMergeTask) error {
	return impl.db.Save(task).Error
}

func (impl *SubtitleMergeTaskImpl) CreateEngineTasksAndUpdateMergeTask(ctx context.Context, engineTasks []*do.BusinessEngineTask, task *do.WorkflowMergeTask, subTask *do.CommentarySubTask) error {
	return impl.db.Transaction(func(tx *gorm.DB) error {
		// 创建engine任务
		err := tx.WithContext(ctx).Create(engineTasks).Error
		if err != nil {
			return err
		}
		//fields := []string{"status", "ocr_submit_at", "user_rect"}
		// 更新ocr任务状态
		//err = tx.Model(&do.WorkflowOcrTask{}).Where("id = ?", task.Id).Select(fields).Updates(task).Error
		err = tx.Save(task).Error
		if err != nil {
			return err
		}

		return tx.Save(subTask).Error
	})
}

func (impl *SubtitleMergeTaskImpl) UpdateMergeTaskAndSubTask(ctx context.Context, task *do.WorkflowMergeTask, subTask *do.CommentarySubTask) error {
	return impl.db.Transaction(func(tx *gorm.DB) error {
		err := tx.WithContext(ctx).Save(subTask).Error
		if err != nil {
			return err
		}
		return tx.WithContext(ctx).Save(task).Error
	})

}
