package subtitle_merge

import (
	"business-workflow/internal/entity/do"
	"context"
)

type ISubtitleMergeTask interface {
	CreateSubtitleMergeTask(ctx context.Context, task *do.WorkflowMergeTask) error
	GetLatestMergeTaskBySubId(ctx context.Context, subId int64) (*do.WorkflowMergeTask, error)
	GetMergeTaskById(ctx context.Context, taskId int64) (*do.WorkflowMergeTask, error)
	UpdateSubtitleMergeTask(ctx context.Context, task *do.WorkflowMergeTask) error
	CreateEngineTasksAndUpdateMergeTask(ctx context.Context, engineTasks []*do.BusinessEngineTask,
		task *do.WorkflowMergeTask, subTask *do.CommentarySubTask) error
	UpdateMergeTaskAndSubTask(ctx context.Context, task *do.WorkflowMergeTask, subTask *do.CommentarySubTask) error
}
