package timer

import (
	"business-workflow/internal/activities"
	"business-workflow/internal/application/svc"
	"business-workflow/internal/common/config"
	"business-workflow/internal/common/redis"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
)

type App struct {
	timerD *timer.Timer
	svc    *svc.Service
}

func (receiver *App) Stop() {
	if receiver.timerD != nil {
		receiver.timerD.Stop()
	}
}

func (receiver *App) init() error {
	// 每10秒执行一次

	if err := receiver.timerD.AddCronTask("*/10 * * * * *", "DoEngineTaskQuery-xsj", timer.BuildFromLambda(activities.BackgroundQueryEngineTaskDetail)); err != nil {
		g.Log().Errorf(context.TODO(), "add cron task DoEngineTaskQuery failed, err: %v", err)
		return err
	}

	return nil
}

func NewApp(ctx context.Context, svc *svc.Service, timerDConfigFilePath string) *App {
	client := redis.GetClient()
	cfg := config.GetConfig()
	configFile := cfg.TimerConfig.ConfigFile
	if timerDConfigFilePath != "" {
		configFile = timerDConfigFilePath
	}
	d, err := timer.NewTimerD(ctx, "business-workflow", timer.WithV8RedisCmdable(client), timer.WithConfigFilePath(configFile))
	if err != nil {
		g.Log().Errorf(ctx, "new timerD failed, err: %v", err)
		panic(err)
	}
	app := &App{
		timerD: d,
		svc:    svc,
	}
	if err := app.init(); err != nil {
		panic(fmt.Errorf("init timer failed, err:%w", err))
	}
	app.timerD.Start()
	return app
}
