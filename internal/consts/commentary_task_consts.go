package consts

// CommentaryBizMode 解说业务类型

type CommentaryBizMode int

const (

	// 基本业务类型
	BaseFallback      CommentaryBizMode = 1      // 001 落版
	BaseHighlight     CommentaryBizMode = 1 << 3 // 001 000 高光剧集
	BaseCommentary    CommentaryBizMode = 1 << 6 // 001 000 000 解说
	BaseHighlightClip CommentaryBizMode = 1 << 7 // 010 000 000 高光剪辑
)

const (
	// 64:解说
	// 65:解说+落版
	// 72:解说+高光剧集
	// 73:解说+高光剧集+落版
	// 128:高光剪辑
	// 129:高光剪辑+落版

	BizModeNone                     CommentaryBizMode = 0                                             // 000 000 000
	BizModeOnlyCommentary                             = BaseCommentary                                // 001 000 000 仅解说
	BizModeCommentaryAndFallback                      = BaseCommentary | BaseFallback                 // 001 000 001 解说 + 落版
	BizModeCommentaryAndHighlight                     = BaseCommentary | BaseHighlight                // 001 010 000 解说 + 高光剧集
	BizModeAll                                        = BaseCommentary | BaseHighlight | BaseFallback // 001 010 001 解说 + 高光剧集 + 落版
	BizModeHighlightClip                              = BaseHighlightClip
	BizModeHighlightClipAndFallback                   = BaseHighlightClip | BaseFallback // 011 000 001 高光剪辑 + 落版
)

// 主任务状态
type CommentaryMainTaskStatus int

const (
	// MainTaskStatusPending 待执行
	MainTaskStatusPending CommentaryMainTaskStatus = 0

	// MainTaskStatusProcessing 执行中
	MainTaskStatusProcessing CommentaryMainTaskStatus = 1

	// MainTaskStatusCompleted 完成
	MainTaskStatusCompleted CommentaryMainTaskStatus = 2

	// 失败  提交工作流失败 扣款失败
	MainTaskStatusFailed CommentaryMainTaskStatus = 3
)

type CommentarySubTaskStatus int

const (
	//完成状态(0:待执行 1: 执行中 2: 成功 3:失败)
	SubTaskStatusPending    CommentarySubTaskStatus = 0
	SubTaskStatusProcessing CommentarySubTaskStatus = 1
	SubTaskStatusCompleted  CommentarySubTaskStatus = 2
	SubTaskStatusFailed     CommentarySubTaskStatus = 3
)

type CommentaryAspectRatio string

const (
	AspectRatio16_9 CommentaryAspectRatio = "16:9"
	AspectRatio9_16 CommentaryAspectRatio = "9:16"
)

// 分辨率
type CommentaryResolution string

const (
	Resolution1280x720 CommentaryResolution = "1280x720"
	Resolution720x1280 CommentaryResolution = "720x1280"
	Resolution854x480  CommentaryResolution = "854x480"
	Resolution480x854  CommentaryResolution = "480x854"
)

// 合成状态  0:待合成 1: 合成中 2: 已合成 3: 合成失败

type CommentaryMergeStatus int

const (
	MergeStatusPending    CommentaryMergeStatus = 0
	MergeStatusProcessing CommentaryMergeStatus = 1
	MergeStatusCompleted  CommentaryMergeStatus = 2
	MergeStatusFailed     CommentaryMergeStatus = 3
)
