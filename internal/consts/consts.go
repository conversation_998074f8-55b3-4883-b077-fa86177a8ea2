package consts

type ContextKey string

const (
	// 配置文件环境变量
	ConfigFile  = "CONFIG_FILE"
	ReqId       = "x-request-id"
	TrafficMark = "x-qw-traffic-mark"
	TenantId    = "TenantId"
	AppId       = "AppId"
)

const (
	BaseTaskOrderId              = "order_id"
	BaseTaskCost                 = "task_cost"
	BaseTaskCommentrayMainTaskID = "commentary_main_task_id"
	BaseTaskCommentraySubTaskID  = "commentary_sub_task_id"
	BaseTaskCommentrayBizMode    = "commentary_biz_mode"
	BaseTaskCommentrayTaskType   = "commentary_task_type"
	BaseTaskCommentrayEdit       = "commentary_edit"
)

const (
	// engine task状态
	EngineWorkflowStatusInit         = 0    // 初始化 （未提交到engine）
	EngineWorkflowStatusPending      = 1    // 待处理（成功提交到engine）
	EngineWorkflowStatusProcessing   = 2    // 处理中(worker取到任务)
	EngineWorkflowStatusSuccess      = 3    // 成功（算法完成）
	EngineWorkflowStatusFailed       = 4    // 失败（算法失败）
	EngineWorkflowStatusTimeout      = 5    // 超时（算法超时）
	EngineWorkflowStatusCancel       = 6    // 主任务已经完成，取消任务
	EngineWorkflowStatusRepeatFailed = 9999 // 重复提交导致的失败
)

type VoiceSource int8

const (
	VoiceSourceOfficial = 1 // 官网音色库
	VoiceSourceSubtitle = 2 // 片段原文音频
)

func (s VoiceSource) IsOfficialSource() bool {
	return s == VoiceSourceOfficial
}

func (s VoiceSource) IsSubtitleSource() bool {
	return s == VoiceSourceSubtitle
}

// CommentarySubtitleMode 字幕模式类型
type CommentarySubtitleMode int

const (
	// SubtitleModeOff 字幕关闭
	SubtitleModeOff CommentarySubtitleMode = 0

	// SubtitleModeOn 字幕开启
	SubtitleModeOn CommentarySubtitleMode = 1
)

// CommentaryEraseEdition 擦除版本类型
type CommentaryEraseEdition int

const (

	// EraseModeOff 擦除关闭
	EraseEditioneOff CommentaryEraseEdition = 0

	// EraseEditionNormal 普通版本
	EraseEditionNormal CommentaryEraseEdition = 1

	// EraseEditionProfessional 专业版本
	EraseEditionProfessional CommentaryEraseEdition = 2
)

// CommentaryEraseMode 擦除模式类型
type CommentaryEraseMode int

const (
	// EraseModeOff 擦除关闭
	EraseModeOff CommentaryEraseMode = 0

	// EraseModeSubtitle 字幕擦除
	EraseModeSubtitle CommentaryEraseMode = 1
	// EraseModeSelect 手动选框
	EraseModeSelect CommentaryEraseMode = 2
	// EraseModeFull 全屏擦除
	EraseModeFull CommentaryEraseMode = 3
)

// CommentaryTaskType 解说任务类型
type CommentaryTaskType int

const (
	// TaskTypeCommentary 解说任务
	TaskTypeCommentary CommentaryTaskType = 100

	// TaskTypeHighlight 高光任务
	TaskTypeHighlight CommentaryTaskType = 200
)

type SourceBizType int

const (
	SourceBizTypeCommentary SourceBizType = 1 // 解说
	SourceBizTypeHighlight  SourceBizType = 2 // 高光
	SourceBizTypeFallback   SourceBizType = 3 // 落版
)

type SourceFileType int

const (
	SourceFileTypeImage SourceFileType = 1 // 图片
	SourceFileTypeVideo SourceFileType = 2 // 视频
	SourceFileTypeAudio SourceFileType = 3 // 音频
)

type CommentaryBgmMode int

const (
	// BgmModeOff BGM关闭
	BgmModeOff CommentaryBgmMode = 0

	// BgmModeOn BGM开启
	BgmModeOn CommentaryBgmMode = 1
)

const (
	DubbingTypeSelf             = 0 // 自研配音
	DubbingTypeElevenLabs       = 1 // ElevenLabs dubbing配音
	DubbingTypeElevenLabsIVCTTS = 2 // ElevenLabs ivc+tts配音
)
const (
	SpeedMin = float32(0.5) // 最小语速
	SpeedMax = float32(2.0) // 最大语速
)

const (
	// TTS 模型类型
	TTSModelTypeUnknown = 0 // 未知
	TTSModelTypeIVC     = 1 // IVC
	TTSModelTypePVC     = 2 // PVC

)

const (
	// 声音重生成状态
	SubtitleItemRegenerateStatusNone       = 0 // 默认
	SubtitleItemRegenerateStatusNeed       = 1 // 需要重新生成
	SubtitleItemRegenerateStatusProcessing = 2 // 重新生成中
	SubtitleItemRegenerateStatusFailed     = 3 // 重新生成失败

	// 重新翻译状态
	SubtitleItemReTranslateStatusNone       = 0 // 默认
	SubtitleItemReTranslateStatusNeed       = 1 // 需要重新生成
	SubtitleItemReTranslateStatusProcessing = 2 // 重新生成中
	SubtitleItemReTranslateStatusFailed     = 3 // 重新生成失败

)

type SourceFromType int

const (
	SourceFromTypeLocal          = 1 // 本地
	SourceFromTypeVideoTranslate = 2 // 视频翻译
)

type ObsMode string

const (
	ObsModeVideoCommentary ObsMode = "video_commentary"
	ObsModeAudioCommentary ObsMode = "audio_commentary"
)

type TargetDurationRangeType int

const (
	TargetDurationRangeType1Minute     TargetDurationRangeType = 1 // 1分钟以内
	TargetDurationRangeType1To3Minute  TargetDurationRangeType = 2 // 1-3分钟
	TargetDurationRangeType3To5Minute  TargetDurationRangeType = 3 // 3-5分钟
	TargetDurationRangeType5To10Minute TargetDurationRangeType = 4 // 5-10分钟
)

type PayOrderStatus int

const (
	PayOrderStatusPending PayOrderStatus = 1   // 待支付
	PayOrderStatusPaid    PayOrderStatus = 100 // 已支付
	PayOrderStatusRefund  PayOrderStatus = 101 // 已退款
	PayOrderStatusFailed  PayOrderStatus = 102 // 支付失败

)

// 擦除相关状态
const (
	InpaintingStatusOcrInit                  = 9000 // OCR初始状态
	InpaintingStatusOcrSubmitted             = 9001 // OCR提交engine成功
	InpaintingStatusOcrWorkerPending         = 9002 // worker处理中
	InpaintingStatusOcrFailed                = 9003 // engine处理失败
	InpaintingStatusOcrComplete              = 9005 // OCR处理完成
	InpaintingStatusPreprocessSubmitted      = 9011 // 预处理提交engine成功
	InpaintingStatusPreprocessWorkerPending  = 9012
	InpaintingStatusPreprocessFailed         = 9013
	InpaintingStatusPreprocessComplete       = 9015
	InpaintingStatusPostprocrssSubmitted     = 9021 // 后处理提交engine成功
	InpaintingStatusPostprocrssWorkerPending = 9022
	InpaintingStatusPostprocrssFailed        = 9023
	InpaintingStatusPostprocrssComplete      = 9025

	InpaintingStatusEraseInit          = 9030 //擦除初始化
	InpaintingStatusEraseSubmitted     = 9031 //擦除提交engine成功
	InpaintingStatusEraseWorkerPending = 9032
	InpaintingStatusEraseFailed        = 9033
	InpaintingStatusEraseComplete      = 9035
)

// 未分类常量
const (
	BatchVocalCount = 100 // 批量处理的最大数量

	// 免费合成次数相关常量
	FreeSynthesisGiveTimes = 5 // 赠送免费合成次数
)

const (
	Crf     = "21"        //23为推荐质量，越小越好
	Preset  = "ultrafast" //最快的编码方式，但文件偏大
	Profile = "baseline"
)
