package impl

import (
	"business-workflow/internal/application/svc"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"business-workflow/internal/controller/commentary_ctrl"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/vo"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
)

type CommentaryMainTaskCtrlImpl struct {
	svc *svc.Service
}

func NewCommentaryMainTaskCtrlImpl(svc *svc.Service) commentary_ctrl.ICommentaryMainTask {
	return &CommentaryMainTaskCtrlImpl{
		svc: svc,
	}
}

func (c *CommentaryMainTaskCtrlImpl) CreateTask(ctx context.Context, req *vo.CommentaryMainTaskCreateReq) (res *vo.CommentaryMainTaskCreateRes, err error) {
	g.Log().Infof(ctx, "CommentaryMainTaskCreateReq req:%+v", req)
	// 参数验证 - Controller层负责请求参数的基础验证
	if err := c.validateCreateRequest(req); err != nil {
		return nil, uerrors.ErrParam
	}

	// VO 转 BO
	reqBO := conv.CommentaryMainTaskCreateReqVOToBO(req)

	// 调用服务层创建任务
	resBO, err := c.svc.CommentaryMainTaskService.CreateTask(ctx, reqBO)
	if err != nil {
		g.Log().Errorf(ctx, "CreateTask err:%v", err)
		return nil, uerrors.ErrBusy
	}

	// BO 转 VO
	return conv.CommentaryMainTaskCreateResBOToVO(resBO), nil
}

// validateCreateRequest 验证创建请求参数
func (c *CommentaryMainTaskCtrlImpl) validateCreateRequest(req *vo.CommentaryMainTaskCreateReq) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}

	if req.TargetLangId == "" {
		return fmt.Errorf("目标语言不能为空")
	}

	if req.AspectRatio == "" {
		return fmt.Errorf("高宽比不能为空")
	}

	// 验证高宽比只能是指定的枚举值
	if req.AspectRatio != string(consts.AspectRatio16_9) && req.AspectRatio != string(consts.AspectRatio9_16) {
		return fmt.Errorf("高宽比只能是16:9或9:16")
	}

	if req.TargetDurationRangeType <= 0 || req.TargetDurationRangeType > 4 {
		return fmt.Errorf("预估时长区间类型无效")
	}

	if req.TargetNumber <= 0 {
		return fmt.Errorf("生成视频数量必须大于0")
	}

	if req.EraseMode == consts.EraseModeSelect && len(req.TextRect) != 4 {
		return fmt.Errorf("OCR区域坐标必须包含4个值")
	}

	if len(req.MaterialSourceList) == 0 {
		return fmt.Errorf("素材源列表不能为空")
	}

	return nil
}

func (c *CommentaryMainTaskCtrlImpl) GetCommentaryTaskHistory(ctx context.Context, req *vo.GetCommentaryTaskHistoryReq) (*vo.GetCommentaryTaskInfoRes, error) {
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}

	// VO 转 BO (需要创建转换方法)
	reqBO := conv.GetCommentaryTaskHistoryReqVOToBO(req)

	// 调用服务层查询任务信息
	resBOs, err := c.svc.CommentaryMainTaskService.GetCommentaryTaskHistory(ctx, reqBO)
	if err != nil {
		return nil, err
	}

	// BO 转 VO (需要创建转换方法)
	return conv.GetCommentaryTaskInfoResBOToVO(resBOs), nil
}

// BatchGetCommentaryTasksByOrderIds 根据订单ID批量获取解说任务
func (c *CommentaryMainTaskCtrlImpl) BatchGetCommentaryTasksByOrderIds(ctx context.Context, req *vo.BatchGetCommentaryTasksByOrderIdsReq) (*vo.BatchGetCommentaryTasksByOrderIdsRes, error) {
	g.Log().Infof(ctx, "BatchGetCommentaryTasksByOrderIds req:%+v", req)
	
	// 参数验证
	if req == nil {
		return nil, fmt.Errorf("请求参数不能为空")
	}
	if len(req.OrderIds) == 0 {
		return nil, fmt.Errorf("订单ID列表不能为空")
	}

	// VO 转 BO
	reqBO := conv.BatchGetCommentaryTasksByOrderIdsReqVOToBO(req)

	// 调用服务层查询任务信息
	resBOs, err := c.svc.CommentaryMainTaskService.BatchGetCommentaryTasksByOrderIds(ctx, reqBO)
	if err != nil {
		g.Log().Errorf(ctx, "BatchGetCommentaryTasksByOrderIds err:%v", err)
		return nil, err
	}

	// BO 转 VO
	return conv.BatchGetCommentaryTasksByOrderIdsResBOToVO(resBOs), nil
}
