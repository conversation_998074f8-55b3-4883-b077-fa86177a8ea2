package impl

import (
	"business-workflow/internal/application/svc"
	"business-workflow/internal/common/uerrors"
	"business-workflow/internal/consts"
	"business-workflow/internal/controller/commentary_ctrl"
	"business-workflow/internal/entity/bo"
	"business-workflow/internal/entity/conv"
	"business-workflow/internal/entity/vo"
	"business-workflow/internal/util/obs"
	"context"
	"fmt"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
)

type CommentarySubTaskCtrlImpl struct {
	svc *svc.Service
}

func NewCommentarySubTaskCtrlImpl(svc *svc.Service) commentary_ctrl.ICommentarySubTask {
	return &CommentarySubTaskCtrlImpl{
		svc: svc,
	}
}

func (c *CommentarySubTaskCtrlImpl) RenameSubtitle(ctx context.Context, req *vo.RenameSubtitleReq) (res *vo.RenameSubtitleRes, err error) {
	// TODO: 调用服务层重命名任务
	// 目前 service 层可能还没有对应的重命名方法

	return &vo.RenameSubtitleRes{}, nil
}

func (c *CommentarySubTaskCtrlImpl) SaveSubtitleMode(ctx context.Context, req *vo.SaveSubtitleModeReq) (res *vo.SaveSubtitleModeRes, err error) {
	// TODO: 调用服务层保存字幕开关状态
	// 需要根据主任务ID更新所有子任务的字幕模式

	return &vo.SaveSubtitleModeRes{}, nil
}

func (c *CommentarySubTaskCtrlImpl) SaveGlobalSubtitleStyle(ctx context.Context, req *vo.SaveGlobalSubtitleStyleReq) (res *vo.SaveGlobalSubtitleStyleRes, err error) {
	// 转换VO到BO
	reqBO := conv.SaveGlobalSubtitleStyleVOToBO(req)
	// 调用服务层保存全局字幕样式
	resBO, err := c.svc.CommentarySubTaskService.SaveGlobalSubtitleStyle(ctx, reqBO)
	if err != nil {
		g.Log().Errorf(ctx, "SaveGlobalSubtitleStyle failed, err: %v, req: %v", err, req)
		return nil, err
	}

	// 转换BO到VO
	res = conv.SaveGlobalSubtitleStyleBOToVO(resBO)
	return res, nil
}

// UpdateSubTaskBgm 更换子任务背景音
func (c *CommentarySubTaskCtrlImpl) UpdateSubTaskBgm(ctx context.Context, req *vo.CommentarySubTaskUpdateBgmReq) (res *vo.CommentarySubTaskUpdateBgmRes, err error) {
	// 参数验证
	if req.SubTaskId <= 0 {
		return nil, uerrors.ErrParam
	}

	if req.BgmUrl == "" {
		return nil, uerrors.ErrParam
	}

	// 转换 VO 到 BO
	boReq := &bo.UpdateSubTaskBgmReqBO{
		SubTaskId: req.SubTaskId,
		BgmUrl:    req.BgmUrl,
	}

	// 调用服务层更新BGM
	boRes, err := c.svc.CommentarySubTaskService.UpdateSubTaskBgm(ctx, boReq)
	if err != nil {
		g.Log().Errorf(ctx, "UpdateSubTaskBgm err:%v", err)
		return nil, uerrors.ErrBusy
	}

	return &vo.CommentarySubTaskUpdateBgmRes{
		Success: boRes.Success,
	}, nil
}

// UpdateBgmConfig 更新BGM配置
func (c *CommentarySubTaskCtrlImpl) UpdateBgmConfig(ctx context.Context, req *vo.UpdateSubTaskBgmModeReq) (res *vo.UpdateSubTaskBgmModeRes, err error) {
	// 参数验证
	if req.Tid <= 0 {
		return nil, fmt.Errorf("任务ID无效")
	}

	// DisableBGM: 0表示启用BGM，1表示禁用BGM
	// 需要转换为BgmMode: 0表示关闭BGM，1表示开启BGM
	var bgmMode int
	if req.DisableBGM == 0 {
		bgmMode = int(consts.BgmModeOn) // 启用BGM
	} else {
		bgmMode = int(consts.BgmModeOff) // 禁用BGM
	}

	// 调用服务层根据主任务ID更新所有子任务的BGM模式
	updateReq := &bo.UpdateSubTaskBgmModeReqBO{
		SubTaskId: req.Tid,
		BgmMode:   bgmMode,
	}
	boRes, err := c.svc.CommentarySubTaskService.UpdateSubTaskBgmMode(ctx, updateReq)
	if err != nil {
		return nil, fmt.Errorf("更新BGM配置失败: %w", err)
	}

	return &vo.UpdateSubTaskBgmModeRes{
		Success: boRes.Success,
	}, nil
}

func (c *CommentarySubTaskCtrlImpl) MergeSync(ctx context.Context, req *vo.MergeReq) (res *vo.MergeRes, err error) {
	// TODO: 调用服务层合成视频

	return &vo.MergeRes{
		PostVideoUrl: "", // TODO: 返回实际的视频URL
	}, nil
}

// GetSubTaskInfo 获取解说子任务详情
func (c *CommentarySubTaskCtrlImpl) GetSubTaskInfo(ctx context.Context, req *vo.GetSubTaskInfoReq) (res *vo.GetSubTaskInfoRes, err error) {
	// 调用服务层获取子任务详情和字幕项列表
	subTaskBO, err := c.svc.CommentarySubTaskService.GetSubTaskWithSubtitles(ctx, req.Tid)
	if err != nil {
		return nil, fmt.Errorf("获取子任务详情失败: %w", err)
	}
	if subTaskBO == nil {
		return nil, fmt.Errorf("子任务不存在: %d", req.Tid)
	}

	// 后面加管理员查看能力
	if subTaskBO.TenantId != req.TenantId {
		g.Log().Errorf(ctx, "子任务租户ID不匹配, subTaskId: %d, reqTenantId: %d, subTaskTenantId: %d", subTaskBO.Id, req.TenantId, subTaskBO.TenantId)
		return nil, fmt.Errorf("子任务不存在: %d", req.Tid)
	}
	// 转换 BO 到 VO
	subTaskVO := conv.CommentarySubTaskBOToVO(subTaskBO)

	return &vo.GetSubTaskInfoRes{
		CommentarySubTaskVO: subTaskVO,
	}, nil
}

// BatchGetSubTaskInfo 获取解说子任务详情
func (c *CommentarySubTaskCtrlImpl) BatchGetSubTaskInfo(ctx context.Context, req *vo.BatchGetSubTaskStatusReq) (res *vo.BatchGetSubTaskStatusRes, err error) {
	// 参数验证
	if len(req.Tids) <= 0 {
		return nil, fmt.Errorf("子任务ID不能为空")
	}
	if req.TenantId <= 0 {
		return nil, fmt.Errorf("租户ID无效")
	}
	tids := make([]int64, 0, len(req.Tids))
	for _, tid := range req.Tids {
		tidInt64, err := strconv.ParseInt(tid, 10, 64)
		if err != nil || tidInt64 <= 0 {
			return nil, fmt.Errorf("子任务ID无效: %s", tid)
		}
		tids = append(tids, tidInt64)
	}

	// 调用服务层获取子任务详情和字幕项列表
	subTaskBOs, err := c.svc.CommentarySubTaskService.GetSubTaskListByIds(ctx, tids)
	if err != nil {
		return nil, fmt.Errorf("获取子任务详情失败: %w", err)
	}
	res = &vo.BatchGetSubTaskStatusRes{
		SubTaskList: make([]*vo.CommentarySubTaskVO, 0, len(subTaskBOs)),
	}
	for _, subTaskBO := range subTaskBOs {
		// 后面加管理员查看能力
		if subTaskBO.TenantId != req.TenantId {
			g.Log().Errorf(ctx, "子任务租户ID不匹配, subTaskId: %d, reqTenantId: %d, subTaskTenantId: %d", subTaskBO.Id, req.TenantId, subTaskBO.TenantId)
			continue
		}
		// 转换 BO 到 VO
		subTaskVO := conv.CommentarySubTaskBOToVO(subTaskBO)
		res.SubTaskList = append(res.SubTaskList, subTaskVO)
	}

	return res, nil
}

// preSignUrls 批量预签名URL字段
func (c *CommentarySubTaskCtrlImpl) preSignUrls(ctx context.Context, subTaskVO *vo.CommentarySubTaskVO) error {
	urlFields := map[string]*string{
		"BgmUrl":                &subTaskVO.BgmUrl,
		"MaterialHighlightUrl":  &subTaskVO.MaterialHighlightUrl,
		"EndTagUrl":             &subTaskVO.EndTagUrl,
		"SubtitleFileUrl":       &subTaskVO.SubtitleFileUrl,
		"NoSubtitleVideoUrl":    &subTaskVO.NoSubtitleVideoUrl,
		"NoSubtitleVideoHlsUrl": &subTaskVO.NoSubtitleVideoHlsUrl,
		"MergedVideoUrl":        &subTaskVO.MergedVideoUrl,
		"MergedVideoHlsUrl":     &subTaskVO.MergedVideoHlsUrl,
	}

	for fieldName, urlPtr := range urlFields {
		if *urlPtr != "" {
			signedUrl, err := obs.PreSignUrl(*urlPtr, 3600*24)
			if err != nil {
				g.Log().Errorf(ctx, "%s预签名失败: %v", fieldName, err)
				return err
			}
			// 替换成CDN
			*urlPtr = obs.ReplaceUrlToCdn(signedUrl)
		}
	}
	return nil
}

func (c *CommentarySubTaskCtrlImpl) MergeBatch(ctx context.Context, req *vo.MergeBatchReq) (res *vo.MergeBatchRes, err error) {
	return &vo.MergeBatchRes{}, nil
}
