package commentary_ctrl

import (
	"business-workflow/internal/entity/vo"
	"context"
)

type ICommentarySubtitleItem interface {
	// DeleteSubtitleItem 删除字幕
	DeleteSubtitleItem(ctx context.Context, req *vo.DeleteSubtitleItemReq) (res *vo.DeleteSubtitleItemRes, err error)

	// TextTranslate 文本翻译
	TextTranslate(ctx context.Context, req *vo.TextTranslateReq) (res *vo.TextTranslateRes, err error)

	// GetBatchTextTranslateStatus 获取翻译状态
	GetBatchTextTranslateStatus(ctx context.Context, req *vo.GetBatchTextTranslateStatusReq) (res *vo.GetBatchTextTranslateStatusRes, err error)

	// GenerateVoice 配音
	GenerateVoice(ctx context.Context, req *vo.GenerateVoiceReq) (res *vo.GenerateVoiceRes, err error)

	// GetBatchGenerateVoiceStatus 查询配音状态
	GetBatchGenerateVoiceStatus(ctx context.Context, req *vo.GetBatchGenerateVoiceStatusReq) (res *vo.GetBatchGenerateVoiceStatusRes, err error)

	// ModifySubtitle 修改字幕
	ModifySubtitle(ctx context.Context, req *vo.ModifySubtitleReq) (res *vo.ModifySubtitleRes, err error)

	// AddSubtitle 添加字幕
	AddSubtitle(ctx context.Context, req *vo.AddSubtitleReq) (res *vo.AddSubtitleRes, err error)

	// ModifySubtitleSpeed 修改片段语速
	ModifySubtitleSpeed(ctx context.Context, req *vo.ModifySubtitleSpeedReq) (res *vo.ModifySubtitleSpeedRes, err error)

	// ModifySubtitleVolume 修改片段音量
	ModifySubtitleVolume(ctx context.Context, req *vo.ModifySubtitleVolumeReq) (res *vo.ModifySubtitleVolumeRes, err error)

	// SplitSubtitle 拆分片段
	SplitSubtitle(ctx context.Context, req *vo.SplitSubtitleReq) (res *vo.SplitSubtitleRes, err error)

	// MergeSubtitle 合并片段
	MergeSubtitle(ctx context.Context, req *vo.MergeSubtitleReq) (res *vo.MergeSubtitleRes, err error)
}
