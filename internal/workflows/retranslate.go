package workflows

import (
	text_translate "business-workflow/internal/activities/commentary/text_translate"
	"business-workflow/internal/common/config"
	"business-workflow/internal/entity/bo"
	"time"

	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

func TextTranslateWorkflow(ctx workflow.Context, task *bo.CommentaryMainTaskBO, subTask *bo.CommentarySubTaskBO, subtitleItemId int64, translateBack bool) (*bo.CommentaryMainTaskBO, error) {
	temporalCfg := config.GetConfig().Temporal
	// 设置全局的工作流选项
	ao := workflow.ActivityOptions{
		// 必须设置以下至少一个超时
		StartToCloseTimeout:    time.Duration(1) * time.Minute,
		ScheduleToCloseTimeout: time.Duration(5) * time.Minute,

		// 可选配置
		RetryPolicy: &temporal.RetryPolicy{ // 重试策略
			InitialInterval:    time.Duration(temporalCfg.InitialInterval) * time.Second,
			BackoffCoefficient: temporalCfg.BackoffCoefficient,
			MaximumInterval:    time.Duration(temporalCfg.MaximumInterval) * time.Second,
			MaximumAttempts:    int32(temporalCfg.MaximumAttempts),
		},
	}
	ctx = workflow.WithActivityOptions(ctx, ao)
	// 处理扣费回滚
	err := workflow.ExecuteActivity(ctx, text_translate.TextTranslateDeductProcess, task, subTask, subtitleItemId).Get(ctx, task)
	if err != nil {
		return nil, err
	}
	defer func() {
		// 处理扣费失败回滚
		err := workflow.ExecuteActivity(ctx, text_translate.TextTranslatCleanUpProcess, task, subTask, subtitleItemId).Get(ctx, task)
		if err != nil {
			return
		}
	}()

	// 文本翻译
	err = workflow.ExecuteActivity(ctx, text_translate.TextTranslateProcess, task, subTask, subtitleItemId, translateBack).Get(ctx, task)
	if err != nil {
		return nil, err
	}

	return task, nil
}
