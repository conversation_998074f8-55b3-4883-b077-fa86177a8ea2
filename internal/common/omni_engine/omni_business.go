package omni_engine

import (
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	apiBusiness "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/api_business"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type OmniBusinessGRPCClient struct {
	conn           *grpc.ClientConn
	businessClient apiBusiness.OmniEngineVoiceServiceClient
}

var (
	BusinessClientInstance *OmniBusinessGRPCClient
)

func NewBusinessClient(ctx context.Context, grpcAddress string) (*OmniBusinessGRPCClient, error) {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	conn, err := grpc.Dial(grpcAddress, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithTimeout(300*time.Second), grpc.WithUnaryInterceptor(trace.RequestInterceptorForClient()),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(100*1024*1024), grpc.MaxCallRecvMsgSize(100*1024*1024)))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return nil, err
	}
	businessClient := apiBusiness.NewOmniEngineVoiceServiceClient(conn)

	return &OmniBusinessGRPCClient{
		businessClient: businessClient,
	}, nil
}

func InitOmniBusinessGRPCClient(ctx context.Context, grpcAddress string) error {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	client, err := NewBusinessClient(ctx, grpcAddress)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return err
	}
	BusinessClientInstance = client
	return nil
}

func GetOmniBusinessClient() apiBusiness.OmniEngineVoiceServiceClient {
	return BusinessClientInstance.businessClient
}

func (c *OmniBusinessGRPCClient) Close() {
	c.conn.Close()
}
