package omni_engine

import (
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	pb "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_engine"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type OmnEngineGRPCClient struct {
	conn         *grpc.ClientConn
	engineClient pb.OmniEngineServiceClient
}

var (
	EngineClientInstance *OmnEngineGRPCClient
)

func NewEngineClient(ctx context.Context, grpcAddress string) (*OmnEngineGRPCClient, error) {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	conn, err := grpc.Dial(grpcAddress, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithTimeout(300*time.Second), grpc.WithUnaryInterceptor(trace.RequestInterceptorForClient()),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(100*1024*1024), grpc.MaxCallRecvMsgSize(100*1024*1024)))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return nil, err
	}
	engineClient := pb.NewOmniEngineServiceClient(conn)

	return &OmnEngineGRPCClient{
		engineClient: engineClient,
	}, nil
}

func InitOmnEngineGRPCClient(ctx context.Context, grpcAddress string) error {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	client, err := NewEngineClient(ctx, grpcAddress)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return err
	}
	EngineClientInstance = client
	return nil
}

func GetOmniEngineClient() pb.OmniEngineServiceClient {
	return EngineClientInstance.engineClient
}

func (c *OmnEngineGRPCClient) Close() {
	c.conn.Close()
}
