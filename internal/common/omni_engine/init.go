package omni_engine

import (
	"business-workflow/internal/common/config"
	"context"
)

func InitRPCClient(ctx context.Context) {
	cfg := config.GetConfig()

	err := InitOmniBalanceGRPCClient(ctx, cfg.OmniBalanceGrpc)
	if err != nil {
		panic(err)
	}
	err = InitOmnEngineGRPCClient(ctx, cfg.OmniEngineGrpc)
	if err != nil {
		panic(err)
	}

	err = InitOmniTenantGRPCClient(ctx, cfg.OmniTenantGrpc)
	if err != nil {
		panic(err)
	}

	err = InitOmniBusinessGRPCClient(ctx, cfg.OmniBusinessGrpc)
	if err != nil {
		panic(err)
	}

}
