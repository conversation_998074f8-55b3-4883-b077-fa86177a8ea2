package omni_engine

import (
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	omniBalance "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_balance"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type OmniBalanceGRPCClient struct {
	conn *grpc.ClientConn

	AVLbalanceClient omniBalance.OmniEngineAllVoiceBalanceServiceClient
}

var (
	BalanceClientInstance *OmniBalanceGRPCClient
)

func NewBalanceClient(ctx context.Context, grpcAddress string) (*OmniBalanceGRPCClient, error) {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	conn, err := grpc.Dial(grpcAddress, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithTimeout(300*time.Second), grpc.WithUnaryInterceptor(trace.RequestInterceptorForClient()),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(100*1024*1024), grpc.MaxCallRecvMsgSize(100*1024*1024)))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return nil, err
	}
	AVLbalanceClient := omniBalance.NewOmniEngineAllVoiceBalanceServiceClient(conn)

	return &OmniBalanceGRPCClient{
		AVLbalanceClient: AVLbalanceClient,
	}, nil
}

func InitOmniBalanceGRPCClient(ctx context.Context, grpcAddress string) error {
	client, err := NewBalanceClient(ctx, grpcAddress)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return err
	}
	BalanceClientInstance = client
	return nil
}

func GetOmniBalanceAVLBalance() omniBalance.OmniEngineAllVoiceBalanceServiceClient {
	return BalanceClientInstance.AVLbalanceClient
}

func (c *OmniBalanceGRPCClient) Close() {
	c.conn.Close()
}
