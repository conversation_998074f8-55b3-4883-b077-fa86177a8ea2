package omni_engine

import (
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	omniTenant "gitlab.ttyuyin.com/all-voice-lab/omni-framework/omni-proto/src/omni_tenant"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type OmniTenantGRPCClient struct {
	conn         *grpc.ClientConn
	tenantClient omniTenant.OmniEngineTenantServiceClient
}

var (
	TenantClientInstance *OmniTenantGRPCClient
)

func NewTenantClient(ctx context.Context, grpcAddress string) (*OmniTenantGRPCClient, error) {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	conn, err := grpc.Dial(grpcAddress, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithTimeout(300*time.Second), grpc.WithUnaryInterceptor(trace.RequestInterceptorForClient()),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(100*1024*1024), grpc.MaxCallRecvMsgSize(100*1024*1024)))
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return nil, err
	}
	tenantClient := omniTenant.NewOmniEngineTenantServiceClient(conn)

	return &OmniTenantGRPCClient{
		tenantClient: tenantClient,
	}, nil
}

func InitOmniTenantGRPCClient(ctx context.Context, grpcAddress string) error {
	g.Log().Infof(ctx, "Connecting to gRPC server: %s", grpcAddress)
	client, err := NewTenantClient(ctx, grpcAddress)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to connect to gRPC server: %v", err)
		return err
	}
	TenantClientInstance = client
	return nil
}

func GetOmniTenantClient() omniTenant.OmniEngineTenantServiceClient {
	return TenantClientInstance.tenantClient
}

func (c *OmniTenantGRPCClient) Close() {
	c.conn.Close()
}
