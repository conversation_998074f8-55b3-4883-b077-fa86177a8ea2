//
// Copyright (c) 2024 TT, Ltd. All Rights Reserved.
//

package config

import (
	"context"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/utils/audit_util"
	"reflect"

	"github.com/gogf/gf/v2/os/gcfg"

	"github.com/gogf/gf/v2/frame/g"
)

type Config struct {
	Env    string `json:"env"`
	Logger struct {
		Level  string `json:"level"`
		Stdout bool   `json:"stdout"`
		Path   string `json:"path"`
		File   string `json:"file"`
	} `json:"logger"`
	Server struct {
		Address     string `json:"address"`
		OpenapiPath string `json:"openapi_path"`
		SwaggerPath string `json:"swagger_path"`
		DataPath    string `json:"data_path"`
	} `json:"server"`
	DB struct {
		Link                   string `json:"link"`
		MaxIdleConns           int    `json:"max_idle_conns"`
		MaxOpenConns           int    `json:"max_open_conns"`
		ConnMaxLifeTimeSeconds int    `json:"conn_max_lifetime_seconds"`
	} `json:"db"`
	Redis struct {
		Host     string `json:"host"`
		Port     int    `json:"port"`
		Password string `json:"password"`
		Database int    `json:"database"`
	} `json:"redis"`
	Obs         ObsConfig `json:"obs"`
	LocalPath   string    `json:"local_path"`
	TimerConfig struct {
		ConfigFile string `json:"config_file"`
	} `json:"timer_config"`
	OmniEngineGrpc   string `json:"omni_engine_grpc"`
	OmniBalanceGrpc  string `json:"omni_balance_grpc"`
	OmniTenantGrpc   string `json:"omni_tenant_grpc"`
	OmniBusinessGrpc string `json:"omni_business_grpc"`
	Temporal         struct {
		Queue                  string  `json:"queue"`
		HostPort               string  `json:"host_port"`
		StartToCloseTimeout    int     `json:"start_to_close_timeout"`    //Activity执行时间(单位分）
		ScheduleToCloseTimeout int     `json:"schedule_to_close_timeout"` //Activity排队与执行时间(单位分）),包括重试时间
		InitialInterval        int     `json:"initial_interval"`          // 初始重试间隔(单位秒）)
		BackoffCoefficient     float64 `json:"backoff_coefficient"`       //退避系数
		MaximumInterval        int     `json:"maximum_interval"`          //最大重试间隔(单位秒）)
		MaximumAttempts        int     `json:"maximum_attempts"`          //最大重试次数
	} `json:"temporal"`
	MergeThreadCore int `json:"merge_thread_core"` // 合成视频时使用的线程核心数，0表示不限制
	IdGen           struct {
		Address string `json:"address" yaml:"address"`
	} `json:"id_gen" yaml:"id_gen"`
	TranslatePromptConfig TranslatePromptConfig `json:"translate_prompt_config"` // 翻译提示词配置
	Audit                 struct {
		IsCN       bool                                     `json:"is_cn" yaml:"is_cn"` // 国内审核需要特殊处理
		Host       string                                   `json:"host" yaml:"host"`
		Enable     bool                                     `json:"enable" yaml:"enable"`
		TenantId   string                                   `json:"tenant_id" yaml:"tenant_id"`
		AppCode    string                                   `json:"app_code" yaml:"app_code"`
		IntervalMs int                                      `json:"interval_ms" yaml:"interval_ms"`
		TimeoutMin int                                      `json:"timeout_min" yaml:"timeout_min"`
		Scene      map[audit_util.BizScene]AuditSceneConfig `json:"scene" yaml:"scene"`
	} `json:"audit" yaml:"audit"`
}

var (
	cfg *Config
)

// GetConfig 返回全局配置实例
func GetConfig() *Config {
	return cfg
}

type ObsConfig struct {
	AccessKey                string `json:"access_key"`
	SecretKey                string `json:"secret_key"`
	UserId                   string `json:"user_id"`
	RegionId                 string `json:"region_id"`
	EndPoint                 string `json:"end_point"`
	PublicEndPoint           string `json:"public_end_point"`
	CdnName                  string `json:"cdn_name"`
	BucketName               string `json:"bucket_name"`
	CosUrl                   string `json:"cos_url"`
	Arn                      string `json:"arn"`
	Vendor                   string `json:"vendor"`
	ObjectDir                string `json:"object_dir"`
	PushObjectCacheAccessKey string `json:"push_object_cache_access_key"` // cdn预热key
	PushObjectCacheSecretKey string `json:"push_object_cache_secret_key"` // cdn预热secret
}
type TranslatePromptConfig struct {
	Infos          []TranslatePromptInfo `json:"infos" yaml:"infos"`                     // 翻译提示词列表
	PromptMappings []PromptMapping       `json:"prompt_mappings" yaml:"prompt_mappings"` // 翻译提示词映射
}
type TranslatePromptInfo struct {
	ID     int    `json:"id" yaml:"id"`         // 翻译提示词ID
	Prompt string `json:"prompt" yaml:"prompt"` // 翻译提示词内容
	Label  string `json:"label" yaml:"label"`   // 翻译提示词标签
}

type AuditSceneConfig struct {
	SceneCode     audit_util.AuditScene `json:"scene_code" yaml:"scene_code"`
	AuditType     string                `json:"audit_type" yaml:"audit_type"`
	AuditStrategy string                `json:"audit_strategy" yaml:"audit_strategy"`
}
type PromptMapping struct {
	Prompt    string `json:"prompt" yaml:"prompt"`         // 翻译提示词内容
	MappingTo string `json:"mapping_to" yaml:"mapping_to"` // 映射到的提示词内容
}

func (c TranslatePromptConfig) GetPromptMappingTo(prompt string) string {
	if c.PromptMappings == nil || prompt == "" {
		return ""
	}
	for _, mapping := range c.PromptMappings {
		if mapping.Prompt == prompt {
			return mapping.MappingTo
		}
	}
	return prompt
}

func LoadConfig(ctx context.Context, configFilePath string) (*Config, error) {
	g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetFileName(configFilePath)
	if cfg == nil {
		cfg = &Config{}

		// 设置默认值
		cfg.Env = "dev"
		cfg.Logger.Stdout = true

		v := reflect.ValueOf(cfg).Elem()
		t := v.Type()

		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			jsonTag := field.Tag.Get("json")
			if jsonTag != "" {
				fieldValue := v.Field(i)
				if fieldValue.CanAddr() {
					if err := g.Cfg().MustGet(ctx, jsonTag).Scan(fieldValue.Addr().Interface()); err != nil {
						return nil, err
					}
				}
			}
		}
	}
	return cfg, nil
}
