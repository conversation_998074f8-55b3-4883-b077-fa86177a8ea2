package merge

type SubtitleMergeSubmitReq struct {
	MergeTaskId      int64   `json:"task_id"`
	SubtitleId       int64   `json:"subtitle_id"`
	VocalAudioPath   string  `json:"vocal_audio_path"`
	AssFilePath      string  `json:"ass_file_path"`
	IsWaterMark      bool    `json:"is_water_mark"`
	IsVocalTask      bool    `json:"is_vocal_task"`
	NotitleVideoPath string  `json:"notitle_video_path"`
	TargetObjectName string  `json:"target_object_name"` // 合并后文件在obs上的名字
	PostVideoPath    string  `json:"post_video_path"`    // 合成配音后的视频路径
	VideoDuration    float32 `json:"video_duration"`     // 视频时长，单位：秒
	Crf              string  `json:"crf"`
	Preset           string  `json:"preset"`
}

type SubtitleMergeEngineRes struct {
	TaskId             int64  `json:"task_id"`
	SubtitleId         int64  `json:"subtitle_id"`
	MergeStartAt       int64  `json:"merge_start_at"`
	MergeEndAt         int64  `json:"merge_end_at"`
	MergeUploadStartAt int64  `json:"merge_upload_start_at"`
	MergeUploadEndAt   int64  `json:"merge_upload_end_at"`
	RetCode            int    `json:"ret_code"`
	ErrMsg             string `json:"err_msg"`
}
