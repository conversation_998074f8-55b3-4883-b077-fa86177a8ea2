package redis

import (
	"business-workflow/internal/common/config"
	"context"
	"fmt"
	"sync"

	redis "github.com/go-redis/redis/v8"
	"github.com/gogf/gf/v2/frame/g"
)

var (
	redisClient *redis.Client
	once        sync.Once
)

func GetClient() *redis.Client {
	if redisClient == nil {
		once.Do(func() {
			cfg := config.GetConfig()
			addr := fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port)
			redisClient = redis.NewClient(&redis.Options{
				Addr:     addr,
				Password: cfg.Redis.Password,
				DB:       cfg.Redis.Database,
			})
			// 测试连接
			ctx := context.TODO()
			_, err := redisClient.Ping(ctx).Result()
			if err != nil {
				g.Log().Errorf(ctx, "init redis client error: %v", err)
				panic("init redis client error")
			}
		})
	}
	return redisClient
}
