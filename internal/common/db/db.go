//
//  Copyright (c) 2024 TT, Ltd. All Rights Reserved.
//

package db

import (
	"business-workflow/internal/common/config"
	"business-workflow/internal/entity/do"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/trace"

	"gorm.io/gorm/logger"

	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"golang.org/x/net/context"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	instance *gorm.DB
	once     sync.Once
)

// GetDB 获取数据库连接实例
func GetDB() *gorm.DB {
	if instance == nil {
		once.Do(func() {
			var err error
			instance, err = newDB()
			if err != nil {
				g.Log().Error(context.TODO(), "mysql init err:%s", err)
				panic("mysql init err")
			}
		})
	}
	return instance
}

func newDB() (*gorm.DB, error) {
	// 连接数据库
	cfg := config.GetConfig()
	customLogger := &trace.CustomLogger{Default: logger.Default}
	db, err := gorm.Open(mysql.Open(cfg.DB.Link), &gorm.Config{
		PrepareStmt:            true,
		SkipDefaultTransaction: true,
		Logger:                 customLogger,
	})
	if err != nil {
		return nil, err
	}

	// 设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	sqlDB.SetMaxIdleConns(cfg.DB.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.DB.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.DB.ConnMaxLifeTimeSeconds) * time.Second)
	// 如果不是生产环境，自动迁移表结构
	if cfg.Env != "prod" {
		modelsToMigrate := []interface{}{
			do.BusinessEngineTask{},
			do.CommentaryMainTask{},
			do.CommentarySubTask{},
			do.CommentarySubtitleItem{},
			do.CommentarySubtitleSnapshotItem{},
		}
		for _, model := range modelsToMigrate {
			if err := db.AutoMigrate(model); err != nil {
				return nil, err // 返回错误
			}
		}
	}
	return db, nil
}
