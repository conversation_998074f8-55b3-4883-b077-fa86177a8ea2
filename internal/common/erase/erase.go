package erase

const (
	AiCodeOk              int32 = 0 //成功（完成）
	AiCodeUnauthorized    int32 = 1 //数据保存路径权限不足
	AiCodeUrlNotExist     int32 = 2 //视频url 不存在
	AiCodeInternalError   int32 = 3 //AI服务内部错误
	AiCodePullVideoFailed int32 = 4 //拉取视频数据失败
	AiCodeBusy            int32 = 5 //系统繁忙（处理中）
	AiCodeNeedOcrFirst    int32 = 6 //未执行ocr提取
	AiCodeUploadObsFailed int32 = 7 //上传obs失败
	AiCodeTaskNotExist    int32 = 8 //任务不存在
	AiCodeCudaOutOfMemory int32 = 9
	AiCodeTaskTimeout     int32 = 10
	AiCodeVideoPareError  int32 = 11   //视频解析失败
	AiCodeMockFailedError int32 = 12   // 就是失败
	AiCodeIdle            int32 = 100  //空闲
	AiCodeUnkown          int32 = 1000 //未知错误
)

// OCR提取请求
type ExtractReq struct {
	Id                 int64     `json:"id"`
	VideoUrl           string    `json:"video_url"`
	TextRegionCallback string    `json:"text_region_callback"`
	Regions            []float64 `json:"selected_region"`
	StartMSecond       int64     `json:"start_mseconds"`
	EndMSecond         int64     `json:"end_mseconds"`
	FullScreen         bool      `json:"full_screen"`
	Language           string    `json:"language"`
}

type ExtractRes struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

type OcrExtractTextItem struct {
	Start int32  `json:"start"` // 字幕开始帧
	End   int32  `json:"end"`   // 字幕结束帧
	Texts string `json:"texts"` // 字幕

}

type BBoxes struct {
	FrameIdx    int32     `json:"frame_idx"`
	Bboxes      [][]int32 `json:"bboxes"`
	IsSubtitles []bool    `json:"is_subtitles"`
	IsErase     []bool    `json:"is_erase"`
	Texts       []string  `json:"texts"`
}

type ExtractResultRes struct {
	Code        int32                `json:"code"`
	Msg         string               `json:"message"`
	Id          int64                `json:"id"`
	Spent       int64                `json:"spent"`
	Regions     [][]float64          `json:"region"`
	Fps         float32              `json:"fps"`          // 视频帧率
	Width       int32                `json:"width"`        // 视频宽
	Height      int32                `json:"height"`       // 视频高
	FrameCount  int32                `json:"frame_count"`  // 视频总帧数
	MergedTexts []OcrExtractTextItem `json:"merged_texts"` // 视频字幕
	BBoxesFile  []BBoxes             `json:"bboxes_file"`  // 字幕检测的详细信息
}

type EraseArea struct {
	ActionType string    `json:"type"`
	Start      float32   `json:"start"`
	End        float32   `json:"end"`
	Region     []float64 `json:"region"`
}

// 预处理提交请求
type PreprocessSubmitReq struct {
	Id           int64  `json:"id"`
	TaskType     string `json:"task_type"`
	VideoUrl     string `json:"video_url"`
	StartMSecond int64  `json:"start_msecond"`
	EndMSecond   int64  `json:"end_msecond"`
	Callback     string `json:"callback"`
	//KeepChunks   bool        `json:"keep_chunks"`
	BboxesFile   []BBoxes    `json:"bboxes_file"`
	InpaintMasks []EraseArea `json:"video_inpaint_masks"`
	FullScreen   bool        `json:"full_screen"`
}

// 预处理提交返回
type PreprocessSubmitRes struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

type PreprocessChunk struct {
	ChunkId      int32     `json:"chunk_idx"`
	FramesRange  []int32   `json:"frames_range"`
	ChunkBbox    []int32   `json:"chunk_bbox"`
	InferBbox    [][]int32 `json:"infer_bbox"`
	ChunkPath    string    `json:"chunk_path"`
	MasterFrames []int32   `json:"master_frames"`
	InferSize    []int32   `json:"infer_size"`
	OccupiedSize float32   `json:"occupied_size"`
}

// 预处理结果返回
type PreprocessResultRes struct {
	Id            int64             `json:"id"`
	Code          int32             `json:"code"`
	Msg           string            `json:"message"`
	Spent         int64             `json:"spent"`
	TotalChunk    int32             `json:"total_chunk"`
	ProgressRatio float32           `json:"progress_ratio"`
	Chunks        []PreprocessChunk `json:"chunks"`
}

// 擦除请示
type EraseV2Req struct {
	Id           int64     `json:"id"`
	Callback     string    `json:"callback"`
	ChunkIdx     int32     `json:"chunk_idx"`
	TotalChunk   int32     `json:"total_chunk"`
	FramesRange  []int32   `json:"frames_range"`
	ChunkBbox    []int32   `json:"chunk_bbox"`
	InferBbox    [][]int32 `json:"infer_bbox"`
	ChunkPath    string    `json:"chunk_path"`
	InferSize    []int32   `json:"infer_size"`
	MasterFrames []int32   `json:"master_frames"`
	OccupiedSize float32   `json:"occupied_size"`
}

type EraseV2Res struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

// 合成请求
type MergeV2SubmitReq struct {
	Id           int64  `json:"id"`
	Callback     string `json:"callback"`
	TargetObject string `json:"target_object"`
}

type MergeSubmitV2Res struct {
	Id   int64  `json:"id"`
	Code int32  `json:"code"`
	Msg  string `json:"message"`
}

// 合成查询
type MergeV2QueryReq struct {
	Id int64 `json:"id"`
}

type MergeQueryV2Res struct {
	Id        int64  `json:"id"`
	Code      int32  `json:"code"`
	Msg       string `json:"message"`
	SharePath string `json:"share_path"`
}
