package common

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"strings"

	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/middleware/consts"
	"gitlab.ttyuyin.com/all-voice-lab/basic-pkg/common-pkg/middleware/entity"

	"github.com/gogf/gf/v2/net/ghttp"
)

var (
	GCtx = context.TODO()
)

type TestUser struct {
	Name string
	Age  int32
}

// Rectangle 使用四个点的矩形
type Rectangle struct {
	TopLeft     []float64 //x,y点
	TopRight    []float64
	BottomLeft  []float64
	BottomRight []float64
}

type DefaultHttpResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func init() {
	GCtx = context.WithValue(GCtx, "default", &TestUser{Name: "firefox", Age: 18})
}

func ParseJsonReq(r *ghttp.Request, v any) error {

	data, err := io.ReadAll(r.Body)
	if err != nil {
		return err
	}
	r.Request.Body = io.NopCloser(bytes.NewBuffer(data)) // 恢复请求体
	err = json.Unmarshal(data, v)

	if err != nil {
		return err
	}

	reqValue := reflect.ValueOf(v).Elem()
	// 获取 UserInfo 字段的反射值
	userInfoField := reqValue.FieldByName("UserInfo")
	// 检查 UserInfo 字段是否存在且可设置
	if userInfoField.IsValid() && userInfoField.CanSet() {
		userInfo := GetUserInfo(r)
		if userInfo != nil {
			userInfoField.Set(reflect.ValueOf(*userInfo))
		}
	}
	return nil
}

func ParseGetReq(r *ghttp.Request) (map[string]string, error) {
	vecs := strings.Split(r.RequestURI, "?")
	if len(vecs) != 2 {
		return nil, fmt.Errorf("参数太少, URI: %s", r.RequestURI)
	}
	params := strings.Split(vecs[1], "&")
	req := make(map[string]string)
	for _, val := range params {
		pairs := strings.Split(val, "=")
		if len(pairs) == 2 {
			req[pairs[0]] = pairs[1]
		}
	}
	return req, nil
}

func EnsureOutputDirectory(outputPath ...string) error {
	for _, path := range outputPath {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			if err := os.MkdirAll(path, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create output directory, path: %v, err: %v", path, err)
			}
		}
	}
	return nil
}

func StringFlag(input string) string {
	output := strings.ReplaceAll(input, "/", "_")
	//output = output + "." + strconv.FormatInt(time.Now().UnixMilli(), 10)
	if input[0] == '/' {
		return output[1:]
	}
	return output
}

func GetLocalIp() string {
	conn, err := net.Dial("udp", "*******:8")
	if err != nil {
		return err.Error()
	}
	defer conn.Close()
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	s := localAddr.String()
	pos := strings.Index(s, ":")
	if pos != -1 {
		return s[0:pos]
	}
	return ""
}

func HttpDownload(url, outputFilePath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	err = os.WriteFile(outputFilePath, body, 0644)
	if err != nil {
		return err
	}
	return nil
}

func TextRectToString(rect []int32) string {
	if len(rect) != 4 {
		return ""
	}
	return fmt.Sprintf("%d,%d,%d,%d", rect[0], rect[1], rect[2], rect[3])
}

func TextRectToString2(rect []int32) string {
	if len(rect) != 4 {
		return ""
	}
	return fmt.Sprintf("[%d,%d,%d,%d]", rect[0], rect[1], rect[2], rect[3])
}

func EscapeParamToString(escape []int64) string {
	if len(escape) != 2 {
		return ""
	}
	return fmt.Sprintf("%v,%v", escape[0], escape[1])
}

func StringToTextRect(str string) []int32 {
	var vecs []int32
	if len(str) < 4 {
		return vecs
	}
	if str[0] == '[' {
		str = str[1 : len(str)-1]
	}
	item := strings.Split(str, ",")
	if len(item) != 4 {
		return vecs
	}
	for i := 0; i < len(item); i++ {
		num, err := strconv.ParseInt(strings.TrimSpace(item[i]), 10, 64)
		if err == nil {
			vecs = append(vecs, int32(num))
		}
	}
	return vecs
}

// v1.9.5 片头片尾直接裁剪
func StringToEscapeParam(str string) []int64 {
	item := strings.Split(str, ",")
	var vecs []int64
	if len(item) != 2 {
		return vecs
	}
	for i := 0; i < len(item); i++ {
		num, err := strconv.ParseInt(strings.TrimSpace(item[i]), 10, 64)
		if err == nil {
			vecs = append(vecs, num)
		}
	}
	return vecs
}

func Between01(vals ...float64) bool {
	for _, val := range vals {
		if val < 0 || val > 1 {
			return false
		}
	}
	return true
}

// 开启有效去除片头片尾 true:有效，false:无效
func IsOpenEscape(str string) bool {
	escapes := StringToEscapeParam(str)
	if str == "" || len(escapes) != 2 {
		return false
	}
	if escapes[0] <= 0 && escapes[1] <= 0 {
		return false
	}
	return true
}

func GetUserInfo(r *ghttp.Request) *entity.UserInfo {
	userInfoVar := r.GetParam(consts.UserInfoKey)
	if userInfoVar.IsNil() {
		return nil
	}

	return userInfoVar.Val().(*entity.UserInfo)
}
