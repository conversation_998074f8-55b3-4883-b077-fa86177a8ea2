# Basic Demo

## 项目结构
```
├─ci 构建脚本
│
├─configs 配置文件
│
├─internal
│  ├─application
│  │  ├─app 聚合依赖
│  │  └─svc 初始化service
│  │
│  ├─common 公共组件
│  │  ├─config
│  │  ├─db
│  │  └─redis
│  │
│  ├─consts 全局变量
│  │
│  ├─controller
│  │  └─hello_ctrl API接口定义
│  │      └─impl API接口实现，不包含业务逻辑，只做逻辑编排。业务逻辑在service中实现。
│  │
│  ├─entity 对象定义
│  │  ├─bo 业务对象，在业务逻辑中使用
│  │  ├─conv 实体转换 vo2bo bo2do等
│  │  ├─do 数据源对象，和数据库表对应
│  │  └─vo 显示层对象，和前端交互使用
│  │
│  ├─mocks 单测mock对象
│  │
│  ├─mw 框架自定义中间件
│  │
│  ├─repo
│  │  └─hello_repo dao接口定义
│  │      └─impl dao接口实现
│  │
│  └─service
│      └─hello_svc 业务逻辑接口定义
│          └─impl 业务逻辑接口实现
│
├─utility 工具类，通用工具类放到x-common-pkg仓库
│
└─gen_mock.sh 生成mock文件脚本
```
![请求时序图](./img/req_process.png)
## 推荐第三方库
- [Wire](https://github.com/google/wire/blob/main/_tutorial/README.md)

Google 开源的一个依赖注入工具。它是一个代码生成器，并不是一个框架。我们只需要在一个特殊的go文件中告诉wire类型之间的依赖关系，它会自动帮我们生成代码，帮助我们创建指定类型的对象，并组装它的依赖。

- [Gomock](https://github.com/golang/mock)

Golang官方接口mock框架。在项目需要进行单元测试的时候，往往会发现有一大堆依赖项，此时可以mock依赖性，自定义入参和回参。

- [Convey](https://github.com/smartystreets/goconvey)

GoConvey是一个非常非常好用的Go测试框架，它直接与go test集成，提供了很多丰富的断言函数，能够在终端输出可读的彩色测试结果，并且还支持全自动的Web UI。